{"tests/test_market_hours_session_info.py::TestMarketHours": true, "tests/test_market_hours_session_info.py::TestMarketHours::test_get_utc_datetime": true, "tests/test_market_hours_session_info.py::TestMarketHours::test_get_current_trading_week_boundaries": true, "tests/test_market_hours_session_info.py::TestMarketHours::test_is_market_open": true, "tests/test_metatrader.py::test_get_gemini_signal_buy": true, "tests/test_metatrader.py::test_calculate_indicators_candlestick_pattern_failure": true, "tests/test_metatrader.py::test_place_trade_basic_sl_tp_fixed[BUY-0-1.1001--1-1]": true, "tests/test_metatrader.py::test_place_trade_basic_sl_tp_fixed[SELL-1-1.1-1--1]": true, "tests/test_metatrader.py::test_load_performance_data_file_not_found": true, "tests/test_metatrader.py::test_upload_logs_to_cloud_file_not_found": true, "tests/test_metatrader.py::test_upload_logs_to_cloud_subprocess_error": true, "tests/test_metatrader.py::test_upload_logs_to_cloud_command_error": true, "tests/test_metatrader.py::test_run_bot_shutdown": true, "tests/test_metatrader.py::test_run_bot_initialization_and_shutdown": true, "tests/test_metatrader.py::test_run_bot_single_iteration": true, "tests/test_metatrader.py::test_run_bot_basic_functionality": true, "tests/test_metatrader.py::test_run_bot_error_handling": true, "tests/test_metatrader.py::test_run_bot_with_exception": true, "tests/test_metatrader.py::test_upload_logs_to_cloud_in_run_bot": true, "tests/test_metatrader.py::test_initialize_knowledge_base_components_success": true, "tests/test_metatrader.py::test_get_trading_signal_buy": true, "tests/test_metatrader.py::test_run_bot_once_success": true, "tests/test_metatrader.py::test_is_market_open_weekday_trading_hours": true, "tests/test_metatrader.py::test_upload_logs_to_cloud_function": true, "tests/test_metatrader.py::test_run_bot_once_no_data": true, "tests/test_metatrader.py::test_run_bot_once_hold_signal": true, "tests/test_metatrader.py::test_run_bot_once_with_news_filter": true, "tests/test_metatrader.py::test_run_bot_once_with_garch_and_hmm": true, "tests/test_metatrader.py::test_run_bot_once_with_heikin_ashi": true, "tests/test_metatrader.py::test_run_bot_with_kill_switch": true, "tests/test_metatrader.py::test_news_filtering_in_trading_decision": true, "tests/test_metatrader.py::test_run_bot_once_with_no_signal": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_detect_patterns_error": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_contextuals_error": true, "tests/test_metatrader.py::test_run_bot_once_market_closed": true, "tests/test_metatrader.py::test_run_bot_once_kill_switch": true, "tests/test_metatrader.py::test_run_bot_once_trading_disabled": true, "tests/test_metatrader.py::test_run_bot_once_exception": true, "tests/test_metatrader.py::test_get_historical_data_symbol_select_failure": true, "tests/test_metatrader.py::test_initialize_kb_collection_exists_populated": true, "tests/test_metatrader.py::test_initialize_kb_collection_exists_empty": true, "tests/test_metatrader.py::test_initialize_kb_collection_not_found": true, "tests/test_metatrader.py::test_initialize_kb_not_initialized_global": true, "tests/test_metatrader.py::test_initialize_kb_file_not_found_on_disk": true, "tests/test_metatrader.py::test_initialize_kb_creation_failure": true, "tests/test_metatrader.py::test_initialize_kb_collection_check_other_qdrant_error": true, "tests/test_metatrader.py::test_initialize_kb_collection_check_generic_error": true, "tests/test_metatrader.py::test_initialize_kb_population_success_multi_batch": true, "tests/test_metatrader.py::test_initialize_kb_population_success_single_batch": true, "tests/test_metatrader.py::test_initialize_kb_population_empty_file": true, "tests/test_metatrader.py::test_initialize_kb_population_embedding_error": true, "tests/test_metatrader.py::test_initialize_kb_population_batch_upsert_error": true, "tests/test_metatrader.py::test_initialize_kb_population_final_upsert_error": true, "tests/test_metatrader.py::test_initialize_kb_population_count_error": true, "tests/test_metatrader.py::test_initialize_kb_population_file_read_error": true, "tests/test_metatrader.py::test_get_qdrant_context_qdrant_error": true, "tests/test_metatrader.py::test_get_qdrant_context_collection_not_found": true, "tests/test_metatrader.py::test_get_qdrant_context_embedding_error": true, "tests/test_metatrader.py::test_get_qdrant_context_invalid_embedding_output": true, "tests/test_metatrader.py::test_get_qdrant_context_result_processing_error": true, "tests/test_metatrader.py::test_get_news_context_api_key_missing": true, "tests/test_metatrader.py::test_filter_news_for_symbol_invalid_event_data": true, "tests/test_metatrader.py::test_filter_news_for_symbol_date_parse_failure": true, "tests/test_metatrader.py::test_get_gemini_signal_prompt_blocked": true, "tests/test_metatrader.py::test_get_gemini_signal_response_finish_reason_safety": true, "tests/test_metatrader.py::test_place_trade_sl_adjustment_due_to_stops_level": true, "tests/test_metatrader.py::test_place_trade_normalization": true, "tests/test_metatrader.py::test_close_existing_positions_send_failure": true, "tests/test_metatrader.py::test_load_performance_data_date_parse_failure": true, "tests/test_metatrader.py::test_check_log_closed_trades_missing_opening_order": true, "tests/test_metatrader.py::test_run_bot_initialization_sequence": true, "tests/test_metatrader.py::test_run_bot_keyboard_interrupt_shutdown": true, "tests/test_metatrader.py::test_run_bot_unhandled_exception_shutdown": true, "tests/test_metatrader.py::test_import_error_performance_analyzer": true, "tests/test_metatrader.py": true, "tests/test_metatrader_sleep.py": true, "tests/test_metatrader_uploads.py": true, "tests/test_indicators_new.py::test_calculate_indicators_success": true, "tests/test_indicators_new.py::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators_new.py::test_calculate_indicators_candlestick_patterns_error": true, "tests/test_indicators_fixed.py::test_calculate_indicators_success": true, "tests/test_indicators_fixed.py::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators_fixed.py::test_calculate_indicators_candlestick_patterns_error": true, "tests/test_sentiment_analyzer_direct.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage": true, "tests/test_sentiment_analyzer_direct.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_error": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestGetMacroContext::test_get_macro_context_partial_fetch_failure": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestGetMacroContext::test_get_macro_context_vix_fetch_failure": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_failure_fred_success": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchVixLevel::test_fetch_vix_level_both_fail": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_uptrend_with_mocked_ma": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_downtrend_with_mocked_ma": true, "tests/test_macro_analyzer_context.py::TestGetMacroContext::test_get_macro_context_disabled": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestLoadHMMModel::test_load_hmm_model_file_not_found": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestLoadHMMModel::test_load_hmm_model_wrong_type": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestLoadHMMModel::test_load_hmm_model_wrong_states": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestLoadHMMModel::test_load_hmm_model_exception": true, "tests/test_indicators_comprehensive.py::TestCalculateIndicators::test_calculate_indicators_success": true, "tests/test_indicators_comprehensive.py::TestCalculateIndicators::test_calculate_indicators_missing_volume": true, "tests/test_indicators_comprehensive.py::TestCalculateIndicators::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators_comprehensive.py::TestCalculateIndicators::test_calculate_indicators_candlestick_exception": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_compute_equity": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_compute_equity_empty_deals": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_run_all_basic_metrics": true, "tests/test_mt5_client_comprehensive.py::TestGetHistoricalData::test_get_historical_data_success": true, "tests/test_mt5_client_comprehensive.py::TestGetHistoricalData::test_get_historical_data_no_data": true, "tests/test_mt5_client_comprehensive.py::TestGetHistoricalData::test_get_historical_data_type_conversion_error": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_dry_run": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_success": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_failure": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_exception": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_no_positions": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_dry_run": true, "tests/test_bot_orchestrator_comprehensive.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_comprehensive.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_main.py::TestMain::test_main_calls_run_bot": true, "tests/test_main.py::test_main_calls_run_bot": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_connection_lost": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_news_fetch": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_performance_analysis": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_closed_deals_check": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_macro_context_fetch": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_symbol_processing": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_exception_handling": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_success": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_invalid_header": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_malformed_row": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_limit_trades": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_empty_file": true, "tests/test_performance_analyzer_recovery_metrics.py::test_recovery_metrics_calculation": true, "tests/test_performance_analyzer_recovery_metrics.py::test_recovery_metrics_empty_lists": true, "tests/test_performance_analyzer_recovery_metrics.py::test_recovery_metrics_exception_handling": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_kill_switch": true, "tests/test_bot_orchestrator_main.py::TestMainExecutionBlock::test_main_required_dependencies_missing": true, "tests/test_bot_orchestrator_main.py::TestMainExecutionBlock::test_main_perf_log_header_creation": true, "tests/test_bot_orchestrator_main.py::TestMainExecutionBlock::test_main_perf_log_header_error": true, "tests/test_bot_orchestrator_main.py::TestMainExecutionBlock::test_main_optional_dependencies_missing": true, "tests/test_calculate_indicators.py::test_calculate_indicators_success": true, "tests/test_calculate_indicators.py::test_calculate_indicators_missing_ohlc": true, "tests/test_calculate_indicators.py::test_calculate_indicators_with_nan_values": true, "tests/test_calculate_indicators.py::test_calculate_indicators_candlestick_pattern_failure": true, "tests/test_calculate_indicators.py::test_calculate_indicators_exception": true, "tests/test_models_train_hmm.py::TestLoadTrainingData::test_load_training_data_success": true, "tests/test_models_train_hmm.py::TestLoadTrainingData::test_load_training_data_timestamp_parsing": true, "tests/test_models_train_hmm.py::TestLoadTrainingData::test_load_training_data_nan_handling": true, "tests/test_models_train_hmm.py::TestCalculateFeatures::test_calculate_features_success": true, "tests/test_models_train_hmm.py::TestCalculateFeatures::test_calculate_features_non_finite_values": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_market_closed": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_log_upload": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_credentials_failure": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_missing_credentials": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_logger_setup_failure": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_mt5_init_failure": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_gemini_setup_failure": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_qdrant_init_failure": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_market_closed": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_connection_lost": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_main_loop": true, "tests/test_run_bot_comprehensive.py::TestRunBot::test_run_bot_trading_disabled": true, "tests/test_bot_orchestrator_helpers.py::TestLoadPerformanceData::test_load_performance_data_column_count_less": true, "tests/test_bot_orchestrator_helpers.py::TestLoadPerformanceData::test_load_performance_data_column_count_more": true, "tests/test_bot_orchestrator_helpers.py::TestLoadPerformanceData::test_load_performance_data_empty_dataframe": true, "tests/test_bot_orchestrator_helpers.py::TestLoadPerformanceData::test_load_performance_data_date_parsing_failure": true, "tests/test_bot_orchestrator_helpers.py::TestLoadPerformanceData::test_load_performance_data_missing_essential_data": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_normal_mode_dataframe_error": true, "tests/test_bot_orchestrator_run_bot.py::TestRunBotMainLoop::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_run_bot.py::TestRunBotMainLoop::test_run_bot_connection_lost": true, "tests/test_bot_orchestrator_run_bot.py::TestRunBotMainLoop::test_run_bot_news_fetch": true, "tests/test_bot_orchestrator_closed_trades.py::TestCheckAndLogClosedTradesNormalMode::test_check_and_log_closed_trades_dataframe_creation": true, "tests/test_bot_orchestrator_closed_trades.py::TestCheckAndLogClosedTradesNormalMode::test_check_and_log_closed_trades_with_entry_context": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositionsFromBotOrchestrator::test_close_positions_called_from_run_bot": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositionsFromBotOrchestrator::test_close_positions_exception_handling": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositionsFromBotOrchestrator::test_close_positions_with_multiple_symbols": true, "tests/test_mt5_client_integration.py::TestCloseExistingPositions::test_close_existing_positions_no_positions": true, "tests/test_mt5_client_integration.py::TestCloseExistingPositions::test_close_existing_positions_with_positions": true, "tests/test_mt5_client_integration.py::TestCloseExistingPositions::test_close_existing_positions_with_error": true, "tests/test_mt5_client_integration.py::TestCloseExistingPositions::test_close_existing_positions_with_exception": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_missing_columns": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_with_num_trades": true, "tests/test_bot_orchestrator_load_env_variables.py::TestLoadEnvVariables::test_load_env_variables_missing_login": true, "tests/test_bot_orchestrator_load_env_variables.py::TestLoadEnvVariables::test_load_env_variables_missing_gemini_key": true, "tests/test_bot_orchestrator_load_env_variables.py::TestLoadEnvVariables::test_load_env_variables_missing_news_key": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_file_not_exists": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_empty_file": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_successful_load": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_column_count_less": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_column_count_more": true, "tests/test_load_performance_data_simple.py::TestLoadPerformanceData::test_exception_handling": true, "tests/test_run_bot.py::TestRunBotInitialization::test_run_bot_mt5_init_failed": true, "tests/test_run_bot.py::TestRunBotInitialization::test_run_bot_gemini_init_failed": true, "tests/test_run_bot.py::TestRunBotInitialization::test_run_bot_kb_init_failed": true, "tests/test_run_bot.py::TestRunBotMainLoop::test_run_bot_shutdown": true, "tests/test_bot_orchestrator_run_bot_once.py::TestRunBotSingleIteration::test_run_bot_single_iteration": true, "tests/test_qdrant_service_edge_cases.py::TestImportErrors::test_qdrant_client_import_error": true, "tests/test_qdrant_service_edge_cases.py::TestImportErrors::test_sentence_transformers_import_error": true, "tests/test_qdrant_service_edge_cases.py::TestInitializationErrors::test_missing_qdrant_url_or_api_key": true, "tests/test_qdrant_service_edge_cases.py::TestInitializationErrors::test_qdrant_client_initialization_error": true, "tests/test_qdrant_service_edge_cases.py::TestInitializationErrors::test_embedder_initialization_error": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_file_path_checks": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_batch_processing_error_handling": true, "tests/test_qdrant_service_advanced.py::TestModuleImportErrors::test_qdrant_client_import_error": true, "tests/test_qdrant_service_advanced.py::TestModuleImportErrors::test_sentence_transformers_import_error": true, "tests/test_qdrant_service_advanced.py::TestModuleInitialization::test_missing_qdrant_url": true, "tests/test_qdrant_service_advanced.py::TestModuleInitialization::test_qdrant_client_initialization_error": true, "tests/test_qdrant_service_advanced.py::TestModuleInitialization::test_embedder_initialization_error": true, "tests/test_qdrant_service_advanced.py::TestModuleInitialization::test_file_path_checks": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_invalid_ma_periods": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_invalid_ma_type": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_negative_ma_periods": true, "tests/test_volatility_forecaster_config_validation.py::TestGarchModelConfigValidation::test_invalid_garch_config": true, "tests/test_volatility_forecaster_config_validation.py::TestGarchModelErrorHandling::test_return_calculation_failed": true, "tests/test_bot_orchestrator_run_once.py::TestRunBotOnce::test_run_bot_once_buy_signal": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_success": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_collection_exists_with_data": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_collection_not_found": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_with_metadata": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_components_not_ready": true, "tests/test_qdrant_service_comprehensive.py::TestGetQdrantContext::test_get_qdrant_context_success": true, "tests/test_qdrant_service_comprehensive.py::TestGetQdrantContext::test_get_qdrant_context_no_results": true, "tests/test_qdrant_service_comprehensive.py::TestGetQdrantContext::test_get_qdrant_context_collection_not_found": true, "tests/test_qdrant_service_comprehensive.py::TestGetQdrantContext::test_get_qdrant_context_invalid_embedding": true, "tests/test_qdrant_service_comprehensive.py::TestGetQdrantContext::test_get_qdrant_context_components_not_ready": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeMetricsKnowledgeBase::test_initialize_metrics_knowledge_base": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeStrategyKnowledgeBase::test_initialize_strategy_knowledge_base": true, "tests/test_qdrant_service_simple.py::TestQdrantService::test_initialize_metrics_knowledge_base": true, "tests/test_qdrant_service_simple.py::TestQdrantService::test_initialize_strategy_knowledge_base": true, "tests/test_mt5_client_additional.py::TestPlaceTradeAdditional::test_place_trade_invalid_parameters": true, "tests/test_mt5_client_additional.py::TestPlaceTradeAdditional::test_place_trade_order_check_failure": true, "tests/test_mt5_client_additional.py::TestPlaceTradeAdditional::test_place_trade_order_send_failure": true, "tests/test_mt5_client_additional.py::TestPlaceTradeAdditional::test_place_trade_with_garch_info": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_adaptive_sltp_exception": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_sell_order": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_non_finite_sltp": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_non_finite_normalized_sltp": true, "tests/test_mt5_client_comprehensive.py::TestPlaceTrade::test_place_trade_order_check_exception": true, "tests/test_mt5_client_comprehensive.py::TestCalculateSleepTime::test_calculate_sleep_time_normal": true, "tests/test_mt5_client_comprehensive.py::TestCalculateSleepTime::test_calculate_sleep_time_no_tick_time": true, "tests/test_mt5_client_comprehensive.py::TestCalculateSleepTime::test_calculate_sleep_time_zero_tick_time": true, "tests/test_mt5_client_comprehensive.py::TestCalculateSleepTime::test_calculate_sleep_time_unknown_timeframe": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_invalid_price": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_order_check_failure": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_order_check_exception": true, "tests/test_mt5_client_comprehensive.py::TestCloseExistingPositions::test_close_existing_positions_order_send_failure": true, "tests/test_mt5_client_final.py::TestInitializeMT5Final::test_initialize_mt5_shutdown_exception": true, "tests/test_mt5_client_final.py::TestGetHistoricalDataFinal::test_get_historical_data_missing_columns": true, "tests/test_mt5_client_final.py::TestPlaceTradeFinal::test_place_trade_adaptive_sltp_exception": true, "tests/test_mt5_client_final.py::TestPlaceTradeFinal::test_place_trade_adaptive_sltp_general_exception": true, "tests/test_mt5_client_final.py::TestPlaceTradeFinal::test_place_trade_non_finite_normalized_sltp": true, "tests/test_mt5_client_final.py::TestCloseExistingPositionsFinal::test_close_existing_positions_invalid_price": true, "tests/test_mt5_client_final.py::TestCalculateSleepTimeFinal::test_calculate_sleep_time_normal": true, "tests/test_mt5_client_remaining.py::TestGetHistoricalDataRemaining::test_get_historical_data_empty_dataframe": true, "tests/test_mt5_client_remaining.py::TestGetHistoricalDataRemaining::test_get_historical_data_missing_time_column": true, "tests/test_mt5_client_remaining.py::TestGetHistoricalDataRemaining::test_get_historical_data_missing_columns": true, "tests/test_qdrant_service_init.py::TestInitializeQdrantService::test_initialize_qdrant_service_client_error": true, "tests/test_qdrant_service_init.py::TestInitializeQdrantService::test_initialize_qdrant_service_embedder_error": true, "tests/test_qdrant_service_init.py::TestInitializeQdrantService::test_initialize_qdrant_service_success": true, "tests/test_trend_analyzer_90_coverage.py::TestConfigValidation::test_invalid_ma_periods": true, "tests/test_trend_analyzer_90_coverage.py::TestConfigValidation::test_invalid_ma_type": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_connection_error": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_missing_feed_key": true, "tests/test_sentiment_analyzer_90_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_invalid_method": true, "tests/test_sentiment_analyzer_90_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_valid_texts": true, "tests/test_sentiment_analyzer_90_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_textblob": true, "tests/test_sentiment_analyzer_90_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_90_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_with_vader": true, "tests/test_combined_coverage.py::test_calculate_returns_valid_data": true, "tests/test_combined_coverage.py::test_fit_garch_model_valid_data": true, "tests/test_combined_coverage.py::test_get_volatility_context_valid_data": true, "tests/test_combined_coverage.py::test_get_volatility_context_fitting_fails": true, "tests/test_combined_coverage.py::test_calculate_returns_log": true, "tests/test_combined_coverage.py::test_calculate_returns_percentage": true, "tests/test_combined_coverage.py::test_fit_garch_model_success": true, "tests/test_combined_coverage.py::test_fit_garch_model_exception": true, "tests/test_combined_coverage.py::test_forecast_volatility_success": true, "tests/test_combined_coverage.py::test_get_volatility_context_success": true, "tests/test_combined_coverage.py::test_get_volatility_context_returns_fail": true, "tests/test_combined_coverage.py::test_get_volatility_context_fit_fail": true, "tests/test_combined_coverage.py::test_get_volatility_context_forecast_fail": true, "tests/test_combined_coverage.py::test_garch_constants": true, "tests/test_combined_coverage.py::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_combined_coverage.py::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_log_method": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_percentage_method": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_with_nan_values": true, "tests/test_combined_coverage.py::TestFitGarchModel::test_fit_garch_model_success": true, "tests/test_combined_coverage.py::TestFitGarchModel::test_fit_garch_model_custom_parameters": true, "tests/test_combined_coverage.py::TestFitGarchModel::test_fit_garch_model_fit_error": true, "tests/test_combined_coverage.py::TestForecastVolatility::test_forecast_volatility_success": true, "tests/test_combined_coverage.py::TestForecastVolatility::test_forecast_volatility_custom_horizon": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_returns_calculation_failed": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_garch_fitting_failed": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_forecast_failed": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_negative_forecast": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_low_volatility": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_medium_volatility": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_high_volatility": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_http_error": true, "tests/test_symbol_processing.py::TestSymbolProcessing::test_signal_generation_hold": true, "tests/test_symbol_processing.py::TestSymbolProcessing::test_signal_generation_buy": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_get_trend_status_with_invalid_ma_type": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_get_trend_status_with_short_gt_long": true, "tests/test_trend_analyzer_config_validation.py::TestTrendAnalyzerConfigValidation::test_get_trend_status_with_negative_ma_period": true, "tests/test_volatility_forecaster_config_validation.py::TestGarchModelErrorHandling::test_return_calculation_failed_with_close_column": true, "tests/test_volatility_forecaster_config_validation.py::TestGarchModelErrorHandling::test_garch_model_fitting_failed": true, "tests/test_volatility_forecaster_config_validation.py::TestGarchModelErrorHandling::test_volatility_forecast_failed": true, "tests/test_gemini_client_final_coverage.py::TestFormatDataframeForPromptErrors::test_attribute_error_handling": true, "tests/test_gemini_client_final_coverage.py::TestFormatDataframeForPromptErrors::test_general_exception_handling": true, "tests/test_gemini_client_final_coverage.py::TestGetGeminiSignalErrors::test_response_parsing_error": true, "tests/test_mt5_client_final_90_coverage.py::TestGetHistoricalDataFinal::test_get_historical_data_datetime_conversion_error": true, "tests/test_mt5_client_final_90_coverage.py::TestPlaceTradeFinal::test_place_trade_with_garch_hmm_info": true, "tests/test_mt5_client_final_90_coverage.py::TestPlaceTradeFinal::test_place_trade_with_non_finite_sl_tp": true, "tests/test_mt5_client_final_90_coverage.py::TestCloseExistingPositionsFinal::test_close_existing_positions_order_send_exception": true, "tests/test_mt5_client_final_90_coverage.py::TestCalculateSleepTimeFinal::test_calculate_sleep_time_with_zero_bars_per_day": true, "tests/test_mt5_client_final_90_coverage.py::TestCalculateSleepTimeFinal::test_calculate_sleep_time_with_negative_bars_per_day": true, "tests/test_mt5_client_final_90_coverage.py::TestCalculateSleepTimeFinal::test_calculate_sleep_time_exception": true, "tests/test_mt5_client_final_90_coverage.py::TestCalculateSleepTimeFinal::test_calculate_sleep_time_no_tick": true, "tests/test_mt5_client_final_coverage_complete.py::TestInitializeMT5Final::test_initialize_mt5_exception_during_login": true, "tests/test_mt5_client_final_coverage_complete.py::TestPlaceTradeFinal::test_place_trade_order_check_none": true, "tests/test_mt5_client_final_coverage_complete.py::TestInitializeMT5Final::test_initialize_mt5_initialize_exception": true, "tests/test_mt5_client_final_coverage_complete.py::TestInitializeMT5Final::test_initialize_mt5_shutdown_exception": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_success": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_error_status": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_json_decode_error": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_timeout": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_request_exception": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_news_success": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_news_error": true, "tests/test_sentiment_analyzer_final_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_vader_with_headlines": true, "tests/test_config_loader_90_coverage.py::TestModuleImportHandling::test_garch_model_import_error": true, "tests/test_config_loader_90_coverage.py::TestModuleImportHandling::test_hmm_model_import_error": true, "tests/test_config_loader_90_coverage.py::TestDependencyChecks::test_qdrant_import_error": true, "tests/test_qdrant_service_70_coverage.py::TestInitializeQdrantService::test_initialize_qdrant_service_embedder_init_error": true, "tests/test_qdrant_service_70_coverage.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_unexpected_response_error": true, "tests/test_qdrant_service_70_coverage.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_batch_processing_error": true, "tests/test_qdrant_service_70_coverage.py::TestGetQdrantContext::test_get_qdrant_context_invalid_hit": true, "tests/test_position_sizer_additional.py::TestKellyParametersAdditional::test_estimate_kelly_parameters_missing_hmm_column": true, "tests/test_trend_analyzer_additional.py::TestGetTrendStatusAdditional::test_get_trend_status_empty_valid_index": true, "tests/test_trend_analyzer_additional.py::TestGetTrendStatusAdditional::test_get_trend_status_ma_calculation_error": true, "tests/test_macro_analyzer_additional.py::TestAnalyzerConfig::test_config_invalid_thresholds": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContextAdditional::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_success": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_fred_rate_trends_success": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_partial_fetch_error": true, "tests/test_integration_data_flow.py::TestDataFlowBetweenModules::test_mt5_to_indicators_data_flow": true, "tests/test_integration_data_flow.py::TestDataFlowBetweenModules::test_indicators_to_trend_analysis_data_flow": true, "tests/test_integration_data_flow.py::TestDataFlowBetweenModules::test_indicators_to_pattern_recognition_data_flow": true, "tests/test_integration_data_flow.py::TestDataFlowBetweenModules::test_indicators_to_volatility_forecast_data_flow": true, "tests/test_integration_data_flow.py::TestDataFlowBetweenModules::test_trade_history_to_performance_metrics_data_flow": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_success": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_trend_status_insufficient_data": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_trend_status_uptrend": true, "tests/test_combined_analyzers.py::TestVolatilityForecaster::test_calculate_returns_valid_data": true, "tests/test_combined_analyzers.py::TestVolatilityForecaster::test_get_volatility_context_valid_data": true, "tests/test_integration_end_to_end.py::TestEndToEndWorkflow::test_qdrant_initialization_and_context_retrieval": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_calculate_slope_direct": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_vix_level_yfinance_success": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_nan_vix": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_valid_series": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_flat_series": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_decreasing_series": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_with_nan_values": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_success": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_mixed": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_nan_vix": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_success": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_adaptive": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_no_wins": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_no_losses": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_success": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_error": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_no_news": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_vader_no_headlines": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_method_none": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_unsupported_method": true, "tests/test_sentiment_analyzer_90_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_method_none": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataTimezoneConversion::test_get_benchmark_data_timezone_conversion_error": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataNaNHandling::test_get_benchmark_data_resample_error": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_kurtosis_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_kurtosis_exception": true, "tests/test_bot_orchestrator_main_loop.py::TestRunBotMainLoop::test_run_bot_main_loop_full_cycle": true, "tests/test_bot_orchestrator_main_loop.py::TestRunBotMainLoop::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_main_loop.py::TestRunBotMainLoop::test_run_bot_mt5_connection_lost": true, "tests/test_bot_orchestrator_signal_execution.py::TestRunBotOnceSignalGeneration::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_signal_execution.py::TestRunBotOnceSignalGeneration::test_run_bot_once_sell_signal": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_load_performance_data_permission_error": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_load_performance_data_unexpected_error": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_load_performance_data_column_mismatch": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_check_and_log_closed_trades_error": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_mt5_init_failure": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_gemini_setup_error": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_main_loop_exception": true, "tests/test_bot_orchestrator_optional_modules.py::TestOptionalModuleImports::test_volatility_forecaster_import_failure": true, "tests/test_bot_orchestrator_optional_modules.py::TestOptionalModuleImports::test_regime_detector_import_failure": true, "tests/test_bot_orchestrator_optional_modules.py::TestOptionalModuleImports::test_heikin_ashi_import_failure": true, "tests/test_bot_orchestrator_optional_modules.py::TestOptionalModuleImports::test_macro_analyzer_import_failure": true, "tests/test_bot_orchestrator_optional_modules.py::TestOptionalModuleImports::test_sentiment_analyzer_import_failure": true, "tests/test_bot_orchestrator_specific_functions.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator_specific_functions.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_specific_functions.py::TestLoadPerformanceData::test_load_performance_data_permission_error": true, "tests/test_bot_orchestrator_specific_functions.py::TestLoadPerformanceData::test_load_performance_data_unexpected_error": true, "tests/test_bot_orchestrator_specific_functions.py::TestLoadPerformanceData::test_load_performance_data_column_mismatch": true, "tests/test_bot_orchestrator_specific_functions.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator_specific_functions.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_error": true, "tests/test_bot_orchestrator_specific_functions.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_run_once.py::TestRunBotOnce::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_run_once.py::TestRunBotOnce::test_run_bot_once_sell_signal": true, "tests/test_gemini_client_100_coverage.py::TestGeminiImport::test_import_error_handling": true, "tests/test_config_loader_100_coverage.py::TestRemainingLines::test_rclone_warning_line": true, "tests/test_config_loader_100_coverage.py::TestRemainingLines::test_qdrant_warning_line": true, "tests/test_config_loader_100_coverage.py::TestRemainingLines::test_sentence_transformers_warning_line": true, "tests/test_config_loader_100_coverage.py::TestRemainingLines::test_gemini_warning_line": true, "tests/test_position_sizer_sizer_final.py::TestMainBlock::test_main_block_default_method": true, "tests/test_position_sizer_sizer_final.py::TestMainBlock::test_main_block_forced_elder": true, "tests/test_position_sizer_sizer_final.py::TestMainBlock::test_main_block_kelly_global": true, "tests/test_position_sizer_sizer_final.py::TestMainBlock::test_main_block_kelly_adaptive": true, "tests/test_position_sizer_sizer_90_plus.py::TestKellyFractionClamping::test_kelly_fraction_clamping": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextEdgeCases::test_get_volatility_context_custom_parameters": true, "tests/test_mt5_client_90_plus.py::TestGetHistoricalDataEdgeCases::test_get_historical_data_missing_columns": true, "tests/test_mt5_client_90_plus.py::TestGetHistoricalDataEdgeCases::test_get_historical_data_type_conversion_error": true, "tests/test_mt5_client_90_plus.py::TestPlaceTradeEdgeCases::test_place_trade_adaptive_sltp_value_error": true, "tests/test_mt5_client_90_plus.py::TestCloseExistingPositionsEdgeCases::test_close_existing_positions_invalid_close_price": true, "tests/test_mt5_client_90_plus.py::TestCalculateSleepTimeEdgeCases::test_calculate_sleep_time_exception": true, "tests/test_pattern_recognizer_additional.py::TestCalculateContextualsErrorHandling::test_calculate_contextuals_exception_in_vol_sma": true, "tests/test_pattern_recognizer_additional.py::TestCalculateContextualsErrorHandling::test_calculate_contextuals_exception_in_atr": true, "tests/test_pattern_recognizer_additional.py::TestCalculateContextualsErrorHandling::test_calculate_contextuals_insufficient_data_for_atr": true, "tests/test_pattern_recognizer_additional.py::TestCalculateContextualsErrorHandling::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_additional.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_index_mismatch_error": true, "tests/test_pattern_recognizer_additional.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_reindex_error": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_loop_errors": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_key_error": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_general_error": true, "tests/test_indicators_additional.py::TestCalculateIndicatorsAdditional::test_candlestick_patterns_calculation": true, "tests/test_indicators_additional.py::TestCalculateIndicatorsAdditional::test_missing_columns_after_calculation": true, "tests/test_indicators_additional.py::TestCalculateIndicatorsAdditional::test_missing_indicator_columns": true, "tests/test_indicators_additional.py::TestCalculateIndicatorsAdditional::test_candlestick_patterns_exception": true, "tests/test_gemini_client_comprehensive.py::TestFormatDataframeForPrompt::test_format_dataframe_for_prompt_success": true, "tests/test_gemini_client_comprehensive.py::TestFormatDataframeForPrompt::test_format_dataframe_for_prompt_missing_columns": true, "tests/test_gemini_client_edge_cases.py::TestFormatDataframeForPromptEdgeCases::test_format_dataframe_for_prompt_invalid_volume": true, "tests/test_gemini_client_edge_cases.py::TestFormatDataframeForPromptEdgeCases::test_format_dataframe_for_prompt_attribute_error": true, "tests/test_gemini_client_edge_cases.py::TestFormatDataframeForPromptEdgeCases::test_format_dataframe_for_prompt_general_exception": true, "tests/test_gemini_client_enhanced.py::TestFormatDataframeForPrompt::test_format_dataframe_for_prompt_success": true, "tests/test_gemini_client_enhanced.py::TestFormatDataframeForPrompt::test_format_dataframe_for_prompt_max_bars_limit": true, "tests/test_gemini_client_enhanced.py::TestFormatDataframeForPrompt::test_format_dataframe_for_prompt_missing_columns": true, "tests/test_indicators_additional.py::TestCalculateIndicatorsAdditional::test_atr_column_not_found": true, "tests/test_indicators_direct.py::test_calculate_indicators_missing_columns": true, "tests/test_indicators_direct.py::test_calculate_indicators_missing_volume": true, "tests/test_indicators_direct.py::test_calculate_indicators_nan_values": true, "tests/test_indicators_direct.py::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators_direct.py::test_calculate_indicators_candlestick_patterns_error": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_valid_data": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_missing_volume": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_missing_columns": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_nan_values": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_all_nan_values": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_nan_vix": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestGetMacroContext::test_get_macro_context_disabled": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestGetMacroContext::test_get_macro_context_fresh_data": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_rising": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_falling": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_stable": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_with_nan": true, "tests/test_macro_analyzer_fetcher.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_success": true, "tests/test_macro_analyzer_fetcher.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_stable": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_increasing": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_decreasing": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_flat": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_with_nan_values": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_with_linear_regression": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_rising": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_falling": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_stable": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_slope_none": true, "tests/test_position_sizer_sizer_90_plus.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_file_read_error": true, "tests/test_position_sizer_sizer_90_plus.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_missing_columns": true, "tests/test_position_sizer_sizer_final.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_data_inconsistency": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_success": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_no_results": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_collection_not_found": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_http_error": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_general_exception": true, "tests/test_qdrant_service.py::TestGetQdrantContext::test_get_qdrant_context_invalid_hit": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_qdrant_http_error": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_warning_for_invalid_kb_hit": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_general_error_handling": true, "tests/test_qdrant_service_advanced.py::TestBatchProcessingErrors::test_batch_upsert_error": true, "tests/test_qdrant_service_batch.py::TestBatchProcessing::test_batch_upsert_success": true, "tests/test_qdrant_service_batch.py::TestBatchProcessing::test_batch_upsert_error": true, "tests/test_qdrant_service_batch.py::TestBatchProcessing::test_create_collection_error": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_pattern_recognizer_final.py::TestCalculateContextualsATRError": true, "tests/test_pattern_recognizer_final.py::TestCalculateContextualsATRError::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_key_error": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_general_error": true, "tests/test_pattern_recognizer_final.py::TestConfigurationLoading::test_config_loading_warning": true, "tests/test_pattern_recognizer_final.py::TestConfigurationLoading::test_config_loading_error": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceKeyError::test_calculate_pattern_confidence_index_mismatch_error": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_slice_type_handling": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_boolean_array_handling": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_key_error_handling": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_general_error_handling": true, "tests/test_pattern_recognizer_coverage.py::TestConfigurationLoading::test_config_loading_warning": true, "tests/test_pattern_recognizer_coverage.py::TestConfigurationLoading::test_config_loading_error": true, "tests/test_pattern_recognizer_coverage.py::TestCalculateContextualsATRError::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_coverage.py::TestCalculateContextualsATRError::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_coverage.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_index_mismatch_error": true, "tests/test_pattern_recognizer_coverage.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_general_error_in_loop": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_none_start": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_pattern_recognizer_config.py::TestConfigurationLoading::test_config_loading_warning": true, "tests/test_pattern_recognizer_config.py::TestConfigurationLoading::test_config_loading_error": true, "tests/test_pattern_recognizer_config.py::TestCalculateContextualsATRError::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_config.py::TestCalculateContextualsATRError::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_edge_cases.py::TestConfigurationLoading::test_config_loading_warning": true, "tests/test_pattern_recognizer_edge_cases.py::TestConfigurationLoading::test_config_loading_error": true, "tests/test_pattern_recognizer_edge_cases.py::TestCalculateContextualsATRError::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_edge_cases.py::TestCalculateContextualsATRError::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_edge_cases.py::TestCalculatePatternConfidenceErrors::test_calculate_pattern_confidence_index_mismatch_error": true, "tests/test_pattern_recognizer_edge_cases.py::TestCalculatePatternConfidenceErrors::test_calculate_pattern_confidence_key_error_in_loop": true, "tests/test_pattern_recognizer_edge_cases.py::TestCalculatePatternConfidenceErrors::test_calculate_pattern_confidence_general_error_in_loop": true, "tests/test_pattern_recognizer_edge_cases.py::TestGetPatternsWithConfidenceErrors::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_edge_cases.py::TestGetPatternsWithConfidenceErrors::test_get_patterns_with_confidence_get_loc_none_start": true, "tests/test_pattern_recognizer_edge_cases.py::TestGetPatternsWithConfidenceErrors::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_pattern_recognizer_uncovered.py::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_uncovered.py::test_calculate_pattern_confidence_index_mismatch": true, "tests/test_pattern_recognizer_uncovered.py::test_calculate_pattern_confidence_general_error": true, "tests/test_pattern_recognizer_uncovered.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_uncovered.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_none_start": true, "tests/test_pattern_recognizer_uncovered.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_pattern_recognizer_direct.py::test_config_weights_warning": true, "tests/test_pattern_recognizer_direct.py::test_config_loading_error": true, "tests/test_pattern_recognizer_direct.py::test_calculate_contextuals_atr_error": true, "tests/test_pattern_recognizer_direct.py::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_direct.py::test_calculate_pattern_confidence_index_mismatch": true, "tests/test_pattern_recognizer_direct.py::test_calculate_pattern_confidence_key_error": true, "tests/test_pattern_recognizer_direct.py::test_calculate_pattern_confidence_general_error": true, "tests/test_pattern_recognizer_direct.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_slice": true, "tests/test_pattern_recognizer_direct.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_none_start": true, "tests/test_pattern_recognizer_direct.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_bot_orchestrator_combined.py::TestRunBotErrors::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotErrors::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotMainLoopErrors::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_run_bot_comprehensive.py::TestRunBotComprehensive::test_run_bot_full_cycle": true, "tests/test_bot_orchestrator_run_bot_comprehensive.py::TestRunBotComprehensive::test_run_bot_with_performance_analysis": true, "tests/test_bot_orchestrator_run_bot_comprehensive.py::TestRunBotComprehensive::test_run_bot_log_upload": true, "tests/test_bot_orchestrator_run_bot_simple.py::TestRunBotSimple::test_run_bot_market_open": true, "tests/test_bot_orchestrator_run_bot_simple.py::TestRunBotSimple::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_load_performance_data": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_get_recent_performance_summary": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_get_recent_performance_summary_empty_df": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_close_existing_positions": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_upload_log_file": true, "tests/test_bot_orchestrator_functions.py::TestBotOrchestratorFunctions::test_check_and_log_closed_trades": true, "tests/test_bot_orchestrator_isolated.py::TestBotOrchestratorIsolated::test_load_performance_data": true, "tests/test_bot_orchestrator_isolated.py::TestBotOrchestratorIsolated::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_isolated.py::TestBotOrchestratorIsolated::test_get_recent_performance_summary_file_not_exists": true, "tests/test_bot_orchestrator_isolated.py::TestBotOrchestratorIsolated::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_direct.py::TestBotOrchestratorDirect::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_direct.py::TestBotOrchestratorDirect::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_direct.py::TestBotOrchestratorDirect::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_trade_context.py::TestTradeEntryContext::test_trade_entry_context_basic": true, "tests/test_bot_orchestrator_trade_context.py::TestTradeEntryContext::test_trade_entry_context_with_session_info": true, "tests/test_bot_orchestrator_trade_context.py::TestTradeEntryContext::test_trade_entry_context_with_all_contexts": true, "tests/test_bot_orchestrator_trade_context.py::TestTradeEntryContext::test_trade_entry_context_exception": true, "tests/test_bot_orchestrator_run_bot_function.py::TestRunBot::test_run_bot_single_iteration": true, "tests/test_bot_orchestrator_run_bot_function.py::TestRunBot::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_run_bot_function.py::TestRunBot::test_run_bot_with_check_closed_trades": true, "tests/test_bot_orchestrator_run_bot_function.py::TestRunBot::test_run_bot_exception": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration::test_run_bot_once_buy_signal": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration::test_run_bot_once_sell_signal": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration::test_run_bot_once_hold_signal": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration::test_run_bot_once_error_handling": true, "tests/test_run_bot_once_signals.py::TestRunBotOnceSignalGeneration::test_run_bot_once_with_error_and_close_positions": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotErrors::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotErrors::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotMainLoopErrors::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_mock.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_mock.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_mock.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_mocked.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_mocked.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_perf_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_load_performance_data": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_run_bot_once_no_historical_data": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_run_bot_once_exception": true, "tests/test_bot_orchestrator_specific_functions.py::TestCloseExistingPositions::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_specific_functions.py::TestCloseExistingPositions::test_close_existing_positions_error": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_empty_file": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_missing_columns": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_no_matching_symbol": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_invalid_profit_value": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_successful_summary": true, "tests/test_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_limit_num_trades": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_symbol_info_returns_none": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_history_orders_get_returns_none": true, "tests/test_bot_orchestrator_run_bot_fixed.py::TestRunBotFixed::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_run_bot_fixed.py::TestRunBotFixed::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_missing_columns": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_num_trades": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_success": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_invalid_profit": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_num_trades": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_empty_file": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_missing_columns": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_invalid_profit": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_success": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_num_trades": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_gemini_setup_failure": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_qdrant_initialization_exception": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_mt5_connection_lost": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_news_fetch": true, "tests/test_bot_orchestrator_90_coverage.py::TestLoadPerformanceDataAdvanced::test_load_performance_data_with_missing_essential_columns": true, "tests/test_bot_orchestrator_90_coverage.py::TestLoadPerformanceDataAdvanced::test_load_performance_data_with_date_conversion": true, "tests/test_bot_orchestrator_90_coverage.py::TestLoadPerformanceDataAdvanced::test_load_performance_data_with_numeric_conversion": true, "tests/test_bot_orchestrator_90_coverage.py::TestCheckAndLogClosedTradesAdvanced::test_check_and_log_closed_trades_with_multiple_deals": true, "tests/test_bot_orchestrator_90_coverage.py::TestCheckAndLogClosedTradesAdvanced::test_check_and_log_closed_trades_with_detailed_context": true, "tests/test_bot_orchestrator_90_coverage.py::TestCheckAndLogClosedTradesAdvanced::test_check_and_log_closed_trades_with_order_error": true, "tests/test_bot_orchestrator_90_coverage.py::TestRunBotAdvanced::test_run_bot_initialization_and_shutdown": true, "tests/test_bot_orchestrator_90_coverage.py::TestRunBotAdvanced::test_run_bot_with_performance_analysis": true, "tests/test_bot_orchestrator_90_coverage.py::TestRunBotAdvanced::test_run_bot_initialization_errors": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_multiple_deals_and_orders": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_entry_context": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_partial_context": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_multiple_deals_same_order": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_error_retrieving_order": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_non_exit_deals": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_exception": true, "tests/test_bot_orchestrator_check_closed_trades_comprehensive.py::TestCheckAndLogClosedTradesComprehensive::test_check_and_log_closed_trades_with_different_deal_types": true, "tests/test_bot_orchestrator_check_closed_trades_edge_cases.py::TestCheckAndLogClosedTradesEdgeCases::test_check_and_log_closed_trades_invalid_deal_structure": true, "tests/test_bot_orchestrator_check_closed_trades_edge_cases.py::TestCheckAndLogClosedTradesEdgeCases::test_check_and_log_closed_trades_dataframe_exception": true, "tests/test_bot_orchestrator_check_closed_trades_edge_cases.py::TestCheckAndLogClosedTradesEdgeCases::test_check_and_log_closed_trades_different_entry_types": true, "tests/test_bot_orchestrator_check_closed_trades_edge_cases.py::TestCheckAndLogClosedTradesEdgeCases::test_check_and_log_closed_trades_empty_dataframe": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_symbol_info_none": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_invalid_point": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_position_already_open": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_historical_data_none": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_h1_data_none": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceEdgeCases::test_h4_data_none": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceSignalGeneration::test_gemini_unavailable": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceSignalGeneration::test_successful_trade_placement": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceSignalGeneration::test_failed_trade_placement": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceSignalGeneration::test_unhandled_exception": true, "tests/test_bot_orchestrator_run_bot_once_coverage.py::TestRunBotOnceSignalGeneration::test_dry_run_mode": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotSimple::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases.py::TestRunBotOnceEdgeCases::test_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases.py::TestRunBotOnceEdgeCases::test_symbol_info_none": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases.py::TestRunBotOnceEdgeCases::test_invalid_point": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases.py::TestRunBotOnceEdgeCases::test_position_already_open": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases.py::TestRunBotOnceEdgeCases::test_historical_data_none": true, "tests/test_bot_orchestrator_check_closed_trades.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_check_closed_trades.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_deals": true, "tests/test_bot_orchestrator_check_closed_trades.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_check_closed_trades.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_error": true, "tests/test_bot_orchestrator_check_closed_trades_simple.py::TestCheckAndLogClosedTradesSimple::test_check_and_log_closed_trades_basic": true, "tests/test_bot_orchestrator_check_closed_trades_simple.py::TestCheckAndLogClosedTradesSimple::test_check_and_log_closed_trades_with_entry_context": true, "tests/test_bot_orchestrator_check_closed_trades_simple.py::TestCheckAndLogClosedTradesSimple::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_check_closed_trades_simple.py::TestCheckAndLogClosedTradesSimple::test_check_and_log_closed_trades_error_fetching_deals": true, "tests/test_bot_orchestrator_check_closed_trades_simple.py::TestCheckAndLogClosedTradesSimple::test_check_and_log_closed_trades_already_processed": true, "tests/test_bot_orchestrator_close_positions_func.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_close_positions_func.py::TestCloseExistingPositions::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_close_positions_func.py::TestCloseExistingPositions::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_close_positions_wrapper.py::TestCloseExistingPositionsWrapper::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_close_positions_wrapper.py::TestCloseExistingPositionsWrapper::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_close_positions_wrapper.py::TestCloseExistingPositionsWrapper::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_close_positions_wrapper.py::TestCloseExistingPositionsWrapper::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_combined.py::TestRunBot::test_run_bot_credentials_failed": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_mt5_error": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_exception": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceErrors::test_run_bot_once_exception": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_run_bot_once_error_handling": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_invalid_point": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_position_already_open": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_historical_data_none": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTradesPytestMode::test_check_and_log_closed_trades_pytest_mode": true, "tests/test_bot_orchestrator_combined.py::TestCheckAndLogClosedTradesPytestMode::test_check_and_log_closed_trades_pytest_with_context": true, "tests/test_bot_orchestrator_combined.py::TestRunBotSimple::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotSimple::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceData::test_load_performance_data_column_mismatch": true, "tests/test_bot_orchestrator_combined_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_combined_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_bot_orchestrator_combined_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_combined_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_matching_trades": true, "tests/test_bot_orchestrator_combined_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_exception": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_deals_fetch_failed": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBot::test_run_bot_credentials_failed": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_mt5_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_exception": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceErrors::test_run_bot_once_exception": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_run_bot_once_error_handling": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_invalid_point": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_position_already_open": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_historical_data_none": true, "tests/test_bot_orchestrator_combined_coverage.py::TestUploadLogFile::test_upload_log_file_success": true, "tests/test_bot_orchestrator_combined_coverage.py::TestUploadLogFile::test_upload_log_file_failure": true, "tests/test_bot_orchestrator_combined_coverage.py::TestUploadLogFile::test_upload_log_file_exception": true, "tests/test_bot_orchestrator_combined_coverage.py::TestUploadLogFile::test_upload_log_file_empty_list": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCloseExistingPositions::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCloseExistingPositions::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_comprehensive.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator_comprehensive.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_comprehensive.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_once_error_handling": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_once_with_error_and_close_positions": true, "tests/test_bot_orchestrator_error_handling.py::TestErrorHandling::test_run_bot_missing_credentials": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_invalid_profit": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_column_count_mismatch": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_empty_after_load": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_date_parsing_failure": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_missing_essential_data": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator_run_bot_once_function.py::TestRunBotOnce::test_run_bot_once_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_function.py::TestRunBotOnce::test_run_bot_once_no_historical_data": true, "tests/test_bot_orchestrator_run_bot_once_function.py::TestRunBotOnce::test_run_bot_once_with_signal_hold": true, "tests/test_bot_orchestrator_run_bot_once_function.py::TestRunBotOnce::test_run_bot_once_with_signal_buy": true, "tests/test_bot_orchestrator_run_bot_once_function.py::TestRunBotOnce::test_run_bot_once_exception": true, "tests/test_bot_orchestrator_run_bot_once_specific.py::TestRunBotOnceSpecific::test_run_bot_once_symbol_info_failure": true, "tests/test_bot_orchestrator_run_bot_once_specific.py::TestRunBotOnceSpecific::test_run_bot_once_invalid_point": true, "tests/test_bot_orchestrator_run_bot_once_specific.py::TestRunBotOnceSpecific::test_run_bot_once_has_open_position": true, "tests/test_bot_orchestrator_run_bot_once_specific.py::TestRunBotOnceSpecific::test_run_bot_once_data_fetch_failure": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_sell_signal": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_with_kill_switch": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_with_trading_disabled": true, "tests/test_bot_orchestrator_signals.py::TestBotOrchestratorSignals::test_run_bot_once_market_closed": true, "tests/test_bot_orchestrator_simple.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_simple.py::TestRunBotSimple::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_close_positions_simple.py::TestCloseExistingPositionsSimple::test_close_positions_success": true, "tests/test_bot_orchestrator_close_positions_simple.py::TestCloseExistingPositionsSimple::test_close_positions_no_positions": true, "tests/test_bot_orchestrator_close_positions_simple.py::TestCloseExistingPositionsSimple::test_close_positions_failure": true, "tests/test_bot_orchestrator_close_positions_simple.py::TestCloseExistingPositionsSimple::test_close_positions_exception": true, "tests/test_bot_orchestrator.py::TestLoadPerformanceData::test_load_performance_data_success": true, "tests/test_bot_orchestrator.py::TestLoadPerformanceData::test_load_performance_data_column_mismatch": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_file_not_found": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_empty_file": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_read_csv_error": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_successful_load_and_processing": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_load_with_column_mismatch_fewer_cols": true, "tests/test_performance_tracker.py::TestLoadPerformanceData::test_load_with_invalid_dates": true, "tests/test_performance_tracker.py::TestGetRecentPerformanceSummary::test_summary_empty_file": true, "tests/test_performance_tracker.py::TestGetRecentPerformanceSummary::test_summary_missing_required_columns": true, "tests/test_performance_tracker.py::TestGetRecentPerformanceSummary::test_summary_success": true, "tests/test_performance_tracker.py::TestGetRecentPerformanceSummary::test_summary_invalid_profit_value": true, "tests/test_performance_tracker.py::TestCheckAndLogClosedTrades::test_no_deals_found": true, "tests/test_performance_tracker.py::TestCheckAndLogClosedTrades::test_deals_fetch_failed": true, "tests/test_performance_tracker.py::TestCheckAndLogClosedTrades::test_log_one_closed_trade_no_context": true, "tests/test_performance_tracker.py::TestCheckAndLogClosedTrades::test_log_one_closed_trade_with_context": true, "tests/test_performance_tracker.py::TestCheckAndLogClosedTrades::test_skip_already_processed_deal": true, "tests/test_performance_tracker.py::TestStoreAndGetTradeContext::test_store_get_clear_context": true, "tests/test_performance_tracker.py::TestStoreAndGetTradeContext::test_store_context_none_ticket": true, "tests/test_performance_tracker.py::TestTestingUtilities::test_clear_all_tracked_data": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_other_error": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMarketInfoEdgeCases::test_format_market_info_with_empty_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatTrendInfoEdgeCases::test_format_trend_info_with_empty_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatGarchInfoEdgeCases::test_format_garch_info_with_empty_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHmmInfoEdgeCases::test_format_hmm_info_with_empty_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHaInfoEdgeCases::test_format_ha_info_with_empty_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMacroInfoEdgeCases::test_format_macro_info_with_empty_dict": true, "tests/test_heikin_ashi_calculator_final_90.py::TestConfigurationLoading::test_config_loading_exception": true, "tests/test_heikin_ashi_calculator_final_90.py::TestGetHaContextEdgeCases::test_get_ha_context_with_indeterminate_trend": true, "tests/test_heikin_ashi_calculator_final_90.py::TestMainExecution::test_main_execution": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_nan_values": true, "tests/test_heikin_ashi_combined.py::TestConfigurationLoading::test_config_loading_exception": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextEdgeCases::test_get_ha_context_with_indeterminate_trend": true, "tests/test_heikin_ashi_combined.py::TestMainExecution::test_main_execution": true, "tests/test_pattern_recognizer_final_coverage.py::TestConfigurationLoading::test_config_loading_exception": true, "tests/test_pattern_recognizer_final_coverage.py::TestDetectPatterns::test_detect_patterns_with_exception_during_selection": true, "tests/test_pattern_recognizer_final_coverage.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_key_error": true, "tests/test_pattern_recognizer_final_coverage.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_type_error": true, "tests/test_pattern_recognizer_final_coverage.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_with_key_error_during_bars_ago": true, "tests/test_market_hours_session_info_final_100.py::TestGetCurrentTradingWeekBoundaries::test_get_current_trading_week_boundaries_with_error_in_nz_and_syd": true, "tests/test_market_hours_session_info_final_100.py::TestGetCurrentTradingWeekBoundaries::test_get_current_trading_week_boundaries_with_only_nz_available": true, "tests/test_market_hours_session_info_final_100.py::TestGetCurrentSessionInfo::test_get_current_session_info_with_tokyo_london_overlap": true, "tests/test_market_hours_session_info_final_100.py::TestMainExecution::test_main_execution": true, "tests/test_performance_analyzer_combined.py::test_performance_metrics_module_loaded": true, "tests/test_performance_analyzer_combined.py::test_performance_utils_module_loaded": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_compute_equity": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_compute_equity_empty_deals": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_run_all_basic_metrics": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_returns_with_gaps": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_avg_win_loss_with_invalid_data": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_consecutive_wins_losses_with_invalid_data": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_avg_trade_duration_with_invalid_data": true, "tests/test_performance_analyzer_metrics_simple.py::TestTradeMetrics::test_calculate_avg_trade_duration_missing_columns": true, "tests/test_performance_analyzer_metrics_simple.py::TestTradeMetrics::test_calculate_drawdown_recovery_times_edge_cases": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestEquityCurveEdgeCases::test_equity_curve_with_negative_initial_capital": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestEquityCurveEdgeCases::test_equity_curve_with_duplicate_timestamps": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_with_resampling_error": true, "tests/test_config_loader_90_coverage.py::TestRcloneExecutableCheck::test_rclone_executable_check_exception": true, "tests/test_config_loader_90_coverage.py::TestModuleImportHandling::test_module_import_errors": true, "tests/test_config_loader_90_coverage.py::TestDependencyChecks::test_rclone_check_exception": true, "tests/test_config_loader_90_coverage.py::TestDependencyChecks::test_dependency_import_errors": true, "tests/test_config_loader_90_coverage.py::TestPathCalculation::test_calculate_paths_fallback_to_cwd": true, "tests/test_config_loader_90_coverage.py::TestPathCalculation::test_calculate_paths_parent_dir_equals_project_root": true, "tests/test_config_loader_90_coverage.py::TestPathCalculation::test_calculate_paths_file_not_defined": true, "tests/test_config_loader_90_coverage.py::TestPathCalculation::test_calculate_paths_name_error": true, "tests/test_config_loader_90_coverage.py::TestPathCalculation::test_load_from_env_no_dotenv_file": true, "tests/test_config_loader_90_coverage.py::TestEnvVariableLoading::test_get_env_int_invalid_value": true, "tests/test_config_loader_90_coverage.py::TestEnvVariableLoading::test_get_env_float_invalid_value": true, "tests/test_config_loader_90_coverage.py::TestEnvVariableLoading::test_load_from_env_invalid_symbols": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_skewness_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_skewness_exception": true, "tests/test_performance_analyzer_analyzer.py::test_analyzer_config_valid": true, "tests/test_performance_analyzer_analyzer.py::test_analyzer_config_invalid": true, "tests/test_performance_analyzer_analyzer.py::test_performance_analyzer_init_valid": true, "tests/test_performance_analyzer_analyzer.py::test_performance_analyzer_init_invalid": true, "tests/test_performance_analyzer_analyzer.py::test_compute_equity_valid": true, "tests/test_performance_analyzer_analyzer.py::test_compute_equity_empty_deals": true, "tests/test_performance_analyzer_analyzer.py::test_get_benchmark_no_symbol": true, "tests/test_performance_analyzer_analyzer.py::test_get_benchmark_success": true, "tests/test_performance_analyzer_analyzer.py::test_get_benchmark_empty_equity": true, "tests/test_performance_analyzer_analyzer.py::test_run_all_valid": true, "tests/test_performance_analyzer_analyzer.py::test_run_all_empty_deals": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestComputeEquityInvalidInput::test_compute_equity_invalid_input": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetBenchmark::test_get_benchmark_cached_none": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetBenchmark::test_get_benchmark_empty_equity": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetBenchmark::test_get_benchmark_exception": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestRunAll::test_run_all_with_recovery_times": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestRunAll::test_run_all_recovery_times_exception": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestRunAll::test_run_all_with_benchmark": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestRunAll::test_run_all_benchmark_exception": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_missing_columns": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_exception": true, "tests/test_performance_analyzer_analyzer_90_coverage.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestAnalyzerConfig::test_analyzer_config_default_values": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestAnalyzerConfig::test_analyzer_config_custom_values": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_init_valid_data": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_init_invalid_data_type": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_init_missing_columns": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_get_benchmark_with_symbol": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_get_benchmark_no_symbol": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_get_benchmark_empty_equity": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_run_all_advanced_metrics": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_run_all_with_exception": true, "tests/test_performance_analyzer_analyzer_comprehensive.py::TestPerformanceAnalyzer::test_run_all_empty_deals": true, "tests/test_performance_analyzer_benchmark_metrics.py::test_run_all_benchmark_metrics": true, "tests/test_performance_analyzer_benchmark_metrics.py::test_run_all_benchmark_no_data": true, "tests/test_performance_analyzer_benchmark_metrics.py::test_run_all_benchmark_metrics_exception": true, "tests/test_performance_analyzer_benchmark_metrics.py::test_run_all_no_benchmark_symbol": true, "tests/test_performance_analyzer_combined.py::TestAnalyzerConfig::test_analyzer_config_default_values": true, "tests/test_performance_analyzer_combined.py::TestAnalyzerConfig::test_analyzer_config_custom_values": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_init_valid_data": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_init_invalid_data_type": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_init_missing_columns": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_get_benchmark_with_symbol": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_get_benchmark_no_symbol": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_get_benchmark_empty_equity": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_run_all_advanced_metrics": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_run_all_with_exception": true, "tests/test_performance_analyzer_combined.py::TestPerformanceAnalyzer::test_run_all_empty_deals": true, "tests/test_performance_analyzer_combined.py::test_analyzer_config_valid": true, "tests/test_performance_analyzer_combined.py::test_analyzer_config_invalid": true, "tests/test_performance_analyzer_combined.py::test_performance_analyzer_init_valid": true, "tests/test_performance_analyzer_combined.py::test_performance_analyzer_init_invalid": true, "tests/test_performance_analyzer_combined.py::test_compute_equity_valid": true, "tests/test_performance_analyzer_combined.py::test_compute_equity_empty_deals": true, "tests/test_performance_analyzer_combined.py::test_get_benchmark_no_symbol": true, "tests/test_performance_analyzer_combined.py::test_get_benchmark_success": true, "tests/test_performance_analyzer_combined.py::test_get_benchmark_empty_equity": true, "tests/test_performance_analyzer_combined.py::test_run_all_valid": true, "tests/test_performance_analyzer_combined.py::test_run_all_empty_deals": true, "tests/test_performance_analyzer_combined.py::test_performance_analyzer_module_loaded": true, "tests/test_performance_analyzer_get_summary.py::TestWithMockCSV::test_get_recent_performance_summary_success": true, "tests/test_performance_analyzer_get_summary.py::TestWithMockCSV::test_get_recent_performance_summary_limit_trades": true, "tests/test_performance_analyzer_get_summary.py::TestWithMockCSV::test_get_recent_performance_summary_empty_file": true, "tests/test_performance_analyzer_get_summary.py::TestWithMockCSV::test_get_recent_performance_summary_invalid_header": true, "tests/test_performance_analyzer_get_summary.py::TestWithMockCSV::test_get_recent_performance_summary_malformed_row": true, "tests/test_performance_analyzer_get_summary.py::test_get_recent_performance_summary_file_not_found": true, "tests/test_performance_analyzer_get_summary.py::test_get_recent_performance_summary_file_not_found_race_condition": true, "tests/test_performance_analyzer_get_summary.py::test_get_recent_performance_summary_general_exception": true, "tests/test_performance_analyzer_recovery_metrics.py::test_run_all_recovery_metrics": true, "tests/test_performance_analyzer_recovery_metrics.py::test_run_all_no_recovery_times": true, "tests/test_performance_analyzer_recovery_metrics.py::test_run_all_recovery_metrics_exception": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_no_file": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_file_not_found": true, "tests/test_performance_analyzer_summary.py::test_get_recent_performance_summary_general_exception": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_invalid_symbol": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_invalid_date_format": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_start_after_end": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_fred_success": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_fred_failure": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_success": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_failure": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_missing_columns": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_close_only": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_non_datetime_index": true, "tests/test_performance_analyzer_utils.py::test_get_benchmark_data_yfinance_timezone_naive": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestEquityCurveInvalidInitialCapital::test_equity_curve_negative_initial_capital": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestEquityCurveInvalidInitialCapital::test_equity_curve_nan_initial_capital": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestEquityCurveInvalidInitialCapital::test_equity_curve_infinite_initial_capital": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataFRED::test_get_benchmark_data_fred_empty_data": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataFRED::test_get_benchmark_data_fred_missing_column": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataTimezoneConversion::test_get_benchmark_data_localization_error": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataNaNHandling::test_get_benchmark_data_with_nans": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataNaNHandling::test_get_benchmark_data_with_leading_nans": true, "tests/test_performance_analyzer_utils_90_coverage.py::TestGetBenchmarkDataNaNHandling::test_get_benchmark_data_all_nans": true, "tests/test_performance_analyzer_utils_final_90.py::TestEquityCurveInvalidInput::test_equity_curve_invalid_dataframe": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataErrorScenarios::test_get_benchmark_data_with_exception": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataErrorScenarios::test_get_benchmark_data_no_providers": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataErrorScenarios::test_get_benchmark_data_empty_dataframe": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataErrorScenarios::test_get_benchmark_data_missing_adj_close": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataTimezoneHandling::test_get_benchmark_data_with_timezone_naive_index": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataNaNHandlingAdditional::test_get_benchmark_data_with_trailing_nans": true, "tests/test_performance_analyzer_utils_final_90.py::TestGetBenchmarkDataNaNHandlingAdditional::test_get_benchmark_data_with_middle_nans": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestEquityCurveEdgeCases::test_equity_curve_with_non_finite_profits": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestEquityCurveEdgeCases::test_equity_curve_logging": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_with_timezone_conversion_error": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_with_all_nans_after_ffill": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_fred_with_import_error": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_yfinance_with_import_error": true, "tests/test_performance_analyzer_utils_final_coverage.py::TestGetBenchmarkDataEdgeCases::test_get_benchmark_data_logging": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_valid_data": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_empty_dataframe": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_invalid_input_type": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_invalid_initial_capital": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_missing_columns": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_non_numeric_profit": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_non_finite_profit": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_non_datetime_exit_time": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_timezone_naive_exit_time": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_non_utc_exit_time": true, "tests/test_performance_analyzer_utils.py::TestEquityCurve::test_equity_curve_duplicate_timestamps": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_create_collection_other_error": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_general_collection_setup_error": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_population_error": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_batch_processing_with_multiple_chunks": true, "tests/test_qdrant_service_additional.py::TestErrorHandling::test_initialization_error_handling": true, "tests/test_qdrant_service_batch.py::TestBatchProcessing::test_batch_processing_with_multiple_batches": true, "tests/test_qdrant_service_comprehensive.py::TestInitializeKnowledgeBase::test_initialize_knowledge_base_file_not_found": true, "tests/test_qdrant_service_edge_cases.py::TestErrorHandling::test_create_collection_error": true, "tests/test_qdrant_service_edge_cases.py::TestErrorHandling::test_batch_processing_error": true, "tests/test_qdrant_service_edge_cases.py::TestErrorHandling::test_invalid_kb_hit": true, "tests/test_qdrant_service_edge_cases.py::TestErrorHandling::test_general_query_error": true, "tests/test_combined_coverage.py::test_calculate_returns_empty_df": true, "tests/test_combined_coverage.py::test_calculate_returns_missing_close": true, "tests/test_combined_coverage.py::test_calculate_returns_single_row": true, "tests/test_combined_coverage.py::test_fit_garch_model_none_returns": true, "tests/test_combined_coverage.py::test_forecast_volatility_none_result": true, "tests/test_combined_coverage.py::test_forecast_volatility_exception": true, "tests/test_combined_coverage.py::test_get_volatility_context_none_df": true, "tests/test_combined_coverage.py::test_get_volatility_context_small_df": true, "tests/test_combined_coverage.py::test_sentiment_constants": true, "tests/test_combined_coverage.py::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_combined_coverage.py::test_fetch_alphavantage_sentiment_invalid_scores": true, "tests/test_combined_coverage.py::test_fetch_news_headlines_stub": true, "tests/test_combined_coverage.py::test_calculate_sentiment_score_vader_exception": true, "tests/test_combined_coverage.py::test_calculate_sentiment_score_transformer_exception": true, "tests/test_combined_coverage.py::test_get_sentiment_context_alphavantage_bearish": true, "tests/test_combined_coverage.py::test_get_sentiment_context_alphavantage_somewhat_bearish": true, "tests/test_combined_coverage.py::test_combined_coverage": true, "tests/test_garch_model_basic.py::test_calculate_returns_log": true, "tests/test_garch_model_basic.py::test_calculate_returns_percentage": true, "tests/test_garch_model_basic.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_basic.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_basic.py::test_fit_garch_model_success": true, "tests/test_garch_model_basic.py::test_fit_garch_model_failure": true, "tests/test_garch_model_basic.py::test_fit_garch_model_insufficient_data": true, "tests/test_garch_model_basic.py::test_forecast_volatility_success": true, "tests/test_garch_model_basic.py::test_forecast_volatility_failure": true, "tests/test_garch_model_basic.py::test_forecast_volatility_none_result": true, "tests/test_garch_model_basic.py::test_get_volatility_context_success": true, "tests/test_garch_model_basic.py::test_get_volatility_context_insufficient_data": true, "tests/test_garch_model_basic.py::test_get_volatility_context_returns_failure": true, "tests/test_garch_model_basic.py::test_get_volatility_context_fit_failure": true, "tests/test_garch_model_basic.py::test_get_volatility_context_forecast_failure": true, "tests/test_garch_model_coverage.py::test_calculate_returns_log": true, "tests/test_garch_model_coverage.py::test_calculate_returns_percentage": true, "tests/test_garch_model_coverage.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_coverage.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_coverage.py::test_calculate_returns_empty_df": true, "tests/test_garch_model_coverage.py::test_calculate_returns_none_df": true, "tests/test_garch_model_coverage.py::test_calculate_returns_insufficient_data": true, "tests/test_garch_model_coverage.py::test_fit_garch_model_success": true, "tests/test_garch_model_coverage.py::test_fit_garch_model_failure": true, "tests/test_garch_model_coverage.py::test_fit_garch_model_insufficient_data": true, "tests/test_garch_model_coverage.py::test_fit_garch_model_none_returns": true, "tests/test_garch_model_coverage.py::test_forecast_volatility_success": true, "tests/test_garch_model_coverage.py::test_forecast_volatility_failure": true, "tests/test_garch_model_coverage.py::test_forecast_volatility_none_result": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_success": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_insufficient_data": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_returns_failure": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_fit_failure": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_forecast_failure": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_negative_variance": true, "tests/test_garch_model_coverage.py::test_get_volatility_context_none_df": true, "tests/test_garch_model_direct.py::test_calculate_returns_log": true, "tests/test_garch_model_direct.py::test_calculate_returns_percentage": true, "tests/test_garch_model_direct.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_direct.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_direct.py::test_fit_garch_model_success": true, "tests/test_garch_model_direct.py::test_fit_garch_model_failure": true, "tests/test_garch_model_direct.py::test_fit_garch_model_insufficient_data": true, "tests/test_garch_model_direct.py::test_forecast_volatility_success": true, "tests/test_garch_model_direct.py::test_forecast_volatility_failure": true, "tests/test_garch_model_direct.py::test_forecast_volatility_none_result": true, "tests/test_garch_model_direct.py::test_get_volatility_context_success": true, "tests/test_garch_model_direct.py::test_get_volatility_context_insufficient_data": true, "tests/test_garch_model_direct.py::test_get_volatility_context_returns_failure": true, "tests/test_garch_model_direct.py::test_get_volatility_context_fit_failure": true, "tests/test_garch_model_direct.py::test_get_volatility_context_forecast_failure": true, "tests/test_garch_model_isolated.py::test_calculate_returns_log": true, "tests/test_garch_model_isolated.py::test_calculate_returns_percentage": true, "tests/test_garch_model_isolated.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_isolated.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_isolated.py::test_fit_garch_model_success": true, "tests/test_garch_model_isolated.py::test_fit_garch_model_failure": true, "tests/test_garch_model_isolated.py::test_fit_garch_model_insufficient_data": true, "tests/test_garch_model_isolated.py::test_forecast_volatility_success": true, "tests/test_garch_model_isolated.py::test_forecast_volatility_failure": true, "tests/test_garch_model_isolated.py::test_forecast_volatility_none_result": true, "tests/test_garch_model_isolated.py::test_get_volatility_context_success": true, "tests/test_garch_model_isolated.py::test_get_volatility_context_insufficient_data": true, "tests/test_garch_model_isolated.py::test_get_volatility_context_returns_failure": true, "tests/test_garch_model_isolated.py::test_get_volatility_context_fit_failure": true, "tests/test_garch_model_isolated.py::test_get_volatility_context_forecast_failure": true, "tests/test_garch_model_mocked.py::test_calculate_returns_log": true, "tests/test_garch_model_mocked.py::test_calculate_returns_percentage": true, "tests/test_garch_model_mocked.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_mocked.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_mocked.py::test_fit_garch_model_success": true, "tests/test_garch_model_mocked.py::test_fit_garch_model_insufficient_data": true, "tests/test_garch_model_mocked.py::test_forecast_volatility_success": true, "tests/test_garch_model_mocked.py::test_forecast_volatility_none_result": true, "tests/test_garch_model_mocked.py::test_get_volatility_context_success": true, "tests/test_garch_model_mocked.py::test_get_volatility_context_insufficient_data": true, "tests/test_garch_model_mocked.py::test_get_volatility_context_returns_failure": true, "tests/test_garch_model_mocked.py::test_get_volatility_context_fit_failure": true, "tests/test_garch_model_mocked.py::test_get_volatility_context_forecast_failure": true, "tests/test_garch_model_simple.py::test_calculate_returns_log": true, "tests/test_garch_model_simple.py::test_calculate_returns_percentage": true, "tests/test_garch_model_simple.py::test_calculate_returns_invalid_method": true, "tests/test_garch_model_simple.py::test_calculate_returns_missing_close": true, "tests/test_garch_model_simple.py::test_calculate_returns_empty_df": true, "tests/test_garch_model_simple.py::test_calculate_returns_none_df": true, "tests/test_garch_model_simple.py::test_calculate_returns_insufficient_data": true, "tests/test_heikin_ashi_calculator.py::test_calculate_heikin_ashi_valid_data": true, "tests/test_heikin_ashi_calculator.py::test_calculate_heikin_ashi_invalid_data": true, "tests/test_heikin_ashi_calculator.py::test_calculate_heikin_ashi_nan_values": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_success": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_none_input": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_calculation_failure": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_strong_uptrend": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_strong_downtrend": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_calculator.py::test_get_ha_context_exception_handling": true, "tests/test_heikin_ashi_calculator_direct.py::test_calculate_heikin_ashi_success": true, "tests/test_heikin_ashi_calculator_direct.py::test_calculate_heikin_ashi_empty_df": true, "tests/test_heikin_ashi_calculator_direct.py::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_calculator_direct.py::test_calculate_heikin_ashi_with_nan": true, "tests/test_heikin_ashi_calculator_direct.py::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_strong_uptrend": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_strong_downtrend": true, "tests/test_heikin_ashi_calculator_direct.py::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_valid_data": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_invalid_data": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_nan_values": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_success": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_none_input": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_calculation_failure": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_strong_uptrend": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_strong_downtrend": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_exception_handling": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_success": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_empty_df": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_with_nan": true, "tests/test_heikin_ashi_combined.py::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_combined.py::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_combined.py::test_heikin_ashi_calculator_module_loaded": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_high_vix": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_rising_rates": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_medium_event_risk": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_invalid_vix": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_nan_vix": true, "tests/test_macro_analyzer_analyzer.py::TestClassifyMacroRegime::test_classify_macro_regime_none_rate_trends": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_disabled": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_fresh_data": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_vix_fetch_error": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_rate_fetch_error": true, "tests/test_macro_analyzer_analyzer.py::TestGetMacroContext::test_get_macro_context_events_fetch_error": true, "tests/test_macro_analyzer_combined.py::test_macro_analyzer_module_loaded": true, "tests/test_macro_analyzer_combined.py::test_macro_fetcher_module_loaded": true, "tests/test_market_hours_session_info.py::test_is_market_open_during_london": true, "tests/test_market_hours_session_info.py::test_is_market_closed_after_london": true, "tests/test_market_hours_session_info.py::test_is_market_closed_on_weekend": true, "tests/test_market_hours_session_info.py::test_is_market_closed_on_holiday": true, "tests/test_market_hours_session_info.py::test_get_current_session_info_overlap": true, "tests/test_market_hours_session_info.py::test_get_current_session_info_single_session": true, "tests/test_market_hours_session_info.py::test_get_current_session_info_no_sessions": true, "tests/test_market_hours_session_info.py::test_is_significant_holiday": true, "tests/test_market_hours_session_info.py::test_get_utc_datetime": true, "tests/test_market_hours_session_info.py::test_get_current_trading_week_boundaries": true, "tests/test_market_hours_session_info.py::test_all_defined_sessions_have_time_bounds": true, "tests/test_market_hours_session_info.py::test_major_and_minor_sessions_are_valid": true, "tests/test_market_hours_session_info_additional.py::test_zoneinfo_error_handling": true, "tests/test_market_hours_session_info_additional.py::test_get_utc_datetime_error_handling": true, "tests/test_market_hours_session_info_additional.py::test_get_current_trading_week_boundaries_normal_case": true, "tests/test_market_hours_session_info_additional.py::test_get_current_trading_week_boundaries_sunday": true, "tests/test_market_hours_session_info_additional.py::test_get_current_trading_week_boundaries_error_handling": true, "tests/test_market_hours_session_info_additional.py::test_get_current_trading_week_boundaries_both_errors": true, "tests/test_market_hours_session_info_additional.py::test_get_current_session_info_invalid_timestamp": true, "tests/test_market_hours_session_info_additional.py::test_get_current_session_info_weekend": true, "tests/test_market_hours_session_info_additional.py::test_get_current_session_info_holiday": true, "tests/test_market_hours_session_info_additional.py::test_get_current_session_info_sydney_tokyo_overlap": true, "tests/test_market_hours_session_info_final_100.py::TestGetCurrentTradingWeekBoundaries::test_get_current_trading_week_boundaries_with_error_in_ny": true, "tests/test_market_hours_session_info_final_100.py::TestGetCurrentTradingWeekBoundaries::test_get_current_trading_week_boundaries_with_only_syd_available": true, "tests/test_market_hours_session_info_final_100.py::TestIsMarketOpen::test_is_market_open_with_no_major_sessions": true, "tests/test_market_hours_session_info_final_100.py::TestIsMarketOpen::test_is_market_open_with_non_utc_timestamp": true, "tests/test_market_hours_session_info_final_100.py::TestIsMarketOpen::test_is_market_open_with_naive_timestamp": true, "tests/test_position_sizer_combined.py::test_position_sizer_module_loaded": true, "tests/test_position_sizer_sizer.py::TestElderVolume::test_calculate_elder_volume_valid_inputs": true, "tests/test_position_sizer_sizer.py::TestElderVolume::test_calculate_elder_volume_invalid_inputs": true, "tests/test_position_sizer_sizer.py::TestElderVolume::test_calculate_elder_volume_with_logger": true, "tests/test_position_sizer_sizer.py::TestElderVolume::test_calculate_elder_volume_edge_cases": true, "tests/test_position_sizer_sizer.py::TestKellyParameters::test_estimate_kelly_parameters_valid_data": true, "tests/test_position_sizer_sizer.py::TestKellyParameters::test_estimate_kelly_parameters_insufficient_trades": true, "tests/test_position_sizer_sizer.py::TestKellyParameters::test_estimate_kelly_parameters_file_not_found": true, "tests/test_position_sizer_sizer.py::TestKellyParameters::test_estimate_kelly_parameters_adaptive": true, "tests/test_position_sizer_sizer.py::TestKellyParameters::test_estimate_kelly_parameters_all_wins": true, "tests/test_position_sizer_sizer.py::TestKellyVolume::test_calculate_kelly_volume_valid_inputs": true, "tests/test_position_sizer_sizer.py::TestKellyVolume::test_calculate_kelly_volume_invalid_inputs": true, "tests/test_position_sizer_sizer.py::TestKellyVolume::test_calculate_kelly_volume_edge_cases": true, "tests/test_position_sizer_sizer.py::TestPositionSize::test_calculate_position_size_elder_method": true, "tests/test_position_sizer_sizer.py::TestPositionSize::test_calculate_position_size_kelly_method": true, "tests/test_position_sizer_sizer.py::TestPositionSize::test_calculate_position_size_kelly_fallback": true, "tests/test_position_sizer_sizer.py::TestPositionSize::test_calculate_position_size_missing_args": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_sentiment_constants": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_fetch_alphavantage_sentiment_invalid_scores": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_fetch_news_headlines_stub": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_calculate_sentiment_score_vader_exception": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_calculate_sentiment_score_transformer_exception": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_get_sentiment_context_alphavantage_bearish": true, "tests/test_sentiment_analyzer_analyzer_full.py::test_get_sentiment_context_alphavantage_somewhat_bearish": true, "tests/test_sentiment_analyzer_combined.py::test_sentiment_constants": true, "tests/test_sentiment_analyzer_combined.py::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_combined.py::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_sentiment_analyzer_combined.py::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_combined.py::test_fetch_alphavantage_sentiment_invalid_scores": true, "tests/test_sentiment_analyzer_combined.py::test_fetch_news_headlines_stub": true, "tests/test_sentiment_analyzer_combined.py::test_calculate_sentiment_score_vader_exception": true, "tests/test_sentiment_analyzer_combined.py::test_calculate_sentiment_score_transformer_exception": true, "tests/test_sentiment_analyzer_combined.py::test_get_sentiment_context_alphavantage_bearish": true, "tests/test_sentiment_analyzer_combined.py::test_get_sentiment_context_alphavantage_somewhat_bearish": true, "tests/test_sentiment_analyzer_combined.py::test_sentiment_analyzer_module_loaded": true, "tests/test_volatility_forecaster_garch_model.py::test_calculate_returns_valid_data": true, "tests/test_volatility_forecaster_garch_model.py::test_calculate_returns_invalid_data": true, "tests/test_volatility_forecaster_garch_model.py::test_calculate_returns_insufficient_data": true, "tests/test_volatility_forecaster_garch_model.py::test_fit_garch_model_valid_data": true, "tests/test_volatility_forecaster_garch_model.py::test_fit_garch_model_insufficient_data": true, "tests/test_volatility_forecaster_garch_model.py::test_forecast_volatility_valid_result": true, "tests/test_volatility_forecaster_garch_model.py::test_forecast_volatility_invalid_result": true, "tests/test_volatility_forecaster_garch_model.py::test_get_volatility_context_valid_data": true, "tests/test_volatility_forecaster_garch_model.py::test_get_volatility_context_invalid_data": true, "tests/test_volatility_forecaster_garch_model.py::test_get_volatility_context_fitting_fails": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_calculate_returns_log": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_calculate_returns_percentage": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_calculate_returns_empty_df": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_calculate_returns_missing_close": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_calculate_returns_single_row": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_fit_garch_model_success": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_fit_garch_model_none_returns": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_fit_garch_model_insufficient_data": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_fit_garch_model_exception": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_forecast_volatility_success": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_forecast_volatility_none_result": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_forecast_volatility_exception": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_success": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_none_df": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_small_df": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_returns_fail": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_fit_fail": true, "tests/test_volatility_forecaster_garch_model_direct.py::test_get_volatility_context_forecast_fail": true, "tests/test_volatility_forecaster_garch_model_final_90.py::test_config_exception_direct": true, "tests/test_volatility_forecaster_garch_model_final_90.py::test_fit_garch_model_fit_exception": true, "tests/test_volatility_forecaster_garch_model_final_90.py::test_forecast_volatility_negative_variance": true, "tests/test_volatility_forecaster_garch_model_final_90.py::test_get_volatility_context_negative_variance": true, "tests/test_volatility_forecaster_garch_model_full.py::test_garch_constants": true, "tests/test_volatility_forecaster_garch_model_full.py::test_calculate_returns_valid_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_calculate_returns_invalid_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_calculate_returns_insufficient_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_fit_garch_model_valid_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_fit_garch_model_insufficient_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_forecast_volatility_valid_result": true, "tests/test_volatility_forecaster_garch_model_full.py::test_forecast_volatility_invalid_result": true, "tests/test_volatility_forecaster_garch_model_full.py::test_get_volatility_context_valid_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_get_volatility_context_invalid_data": true, "tests/test_volatility_forecaster_garch_model_full.py::test_get_volatility_context_fitting_fails": true, "tests/test_bot_orchestrator.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_deals_fetch_failed": true, "tests/test_bot_orchestrator.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator.py::TestRunBot::test_run_bot_credentials_failed": true, "tests/test_bot_orchestrator_70_coverage.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_mt5_error": true, "tests/test_bot_orchestrator_70_coverage.py::TestCheckAndLogClosedTradesErrors::test_check_and_log_closed_trades_exception": true, "tests/test_bot_orchestrator_70_coverage.py::TestRunBotOnceErrors::test_run_bot_once_exception": true, "tests/test_bot_orchestrator_70_coverage.py::TestRunBotErrors::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_70_coverage.py::TestRunBotErrors::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_70_coverage_part2.py::TestRunBotMainLoopErrors::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_run_bot_once_error_handling": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_invalid_point": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_position_already_open": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_historical_data_none": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestUploadLogFile::test_upload_log_file_success": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestUploadLogFile::test_upload_log_file_upload_failure": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestUploadLogFile::test_upload_log_file_exception": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestCloseExistingPositions::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestCloseExistingPositions::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestCloseExistingPositions::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotOnceEdgeCases::test_run_bot_once_invalid_point": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotOnceEdgeCases::test_run_bot_once_missing_m5_data": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotOnceEdgeCases::test_run_bot_once_invalid_atr": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotOnceEdgeCases::test_run_bot_once_with_open_position": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_credentials_missing": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotInitialization::test_run_bot_mt5_init_failure": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotInitialization::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotInitialization::test_run_bot_gemini_setup_failure": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotInitialization::test_run_bot_kb_init_error": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotInitialization::test_run_bot_market_closed": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotInitialization::test_run_bot_mt5_connection_lost": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotOnce::test_run_bot_once_invalid_point": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotOnce::test_run_bot_once_missing_m5_data": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotOnce::test_run_bot_once_invalid_atr": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotOnce::test_run_bot_once_with_open_position": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestUploadLogFile::test_upload_log_file_success": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestUploadLogFile::test_upload_log_file_upload_failure": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestCloseExistingPositions::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestCloseExistingPositions::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestCloseExistingPositions::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestCloseExistingPositions::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_mt5_error": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_deals": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_already_processed": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_80_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_exception": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_symbol_info_error": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_invalid_point": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_with_open_position": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_missing_m5_data": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_invalid_atr": true, "tests/test_bot_orchestrator_80_coverage.py::TestRunBotOnceAdditional::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_80_coverage.py::TestCloseExistingPositionsAdditional::test_close_existing_positions_success": true, "tests/test_bot_orchestrator_80_coverage.py::TestCloseExistingPositionsAdditional::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_80_coverage.py::TestCloseExistingPositionsAdditional::test_close_existing_positions_failure": true, "tests/test_bot_orchestrator_80_coverage.py::TestCloseExistingPositionsAdditional::test_close_existing_positions_exception": true, "tests/test_bot_orchestrator_80_coverage.py::TestUploadLogFile::test_upload_log_file_success": true, "tests/test_bot_orchestrator_80_coverage.py::TestUploadLogFile::test_upload_log_file_upload_failure": true, "tests/test_bot_orchestrator_80_coverage.py::TestUploadLogFile::test_upload_log_file_exception": true, "tests/test_bot_orchestrator_90_coverage.py::TestRunBotAdvanced::test_run_bot_log_upload": true, "tests/test_bot_orchestrator_check_closed_trades_simple_2.py::TestCheckAndLogClosedTradesSimple2::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_check_closed_trades_simple_2.py::TestCheckAndLogClosedTradesSimple2::test_check_and_log_closed_trades_deals_fetch_failed": true, "tests/test_bot_orchestrator_check_closed_trades_simple_2.py::TestCheckAndLogClosedTradesSimple2::test_check_and_log_closed_trades_with_deals": true, "tests/test_bot_orchestrator_check_closed_trades_simple_2.py::TestCheckAndLogClosedTradesSimple2::test_check_and_log_closed_trades_already_processed": true, "tests/test_bot_orchestrator_check_closed_trades_simple_2.py::TestCheckAndLogClosedTradesSimple2::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositions::test_close_positions_no_positions": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositions::test_close_positions_with_positions": true, "tests/test_bot_orchestrator_close_positions.py::TestClosePositions::test_close_positions_error_handling": true, "tests/test_bot_orchestrator_comprehensive.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_missing_columns": true, "tests/test_bot_orchestrator_comprehensive.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_bot_orchestrator_comprehensive.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator_comprehensive.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_with_context": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_pytest_mode": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_pytest_with_context": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_normal_mode_deals_fetch_failed": true, "tests/test_bot_orchestrator_init.py::TestRunBotInitialization::test_run_bot_initialization": true, "tests/test_bot_orchestrator_load_env_variables.py::TestLoadEnvVariables::test_load_env_variables_success": true, "tests/test_bot_orchestrator_load_env_variables.py::TestLoadEnvVariables::test_load_env_variables_empty_model_name": true, "tests/test_bot_orchestrator_load_performance_data_simple.py::TestLoadPerformanceDataSimple::test_read_csv_exception": true, "tests/test_bot_orchestrator_load_performance_data_simple.py::TestLoadPerformanceDataSimple::test_column_count_mismatch": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_exists": true, "tests/test_bot_orchestrator_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_error": true, "tests/test_bot_orchestrator_run_bot_fixed.py::TestRunBotFixed::test_run_bot_mt5_init_error": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_symbol_info_none": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_invalid_point": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_position_already_open": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_historical_data_none": true, "tests/test_bot_orchestrator_run_bot_once_edge_cases_2.py::TestRunBotOnceEdgeCases2::test_exception_handling": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotOnceSimple::test_run_bot_once_market_closed": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotOnceSimple::test_run_bot_once_symbol_info_none": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotOnceSimple::test_run_bot_once_invalid_point": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotOnceSimple::test_run_bot_once_position_already_open": true, "tests/test_bot_orchestrator_run_bot_once_simple.py::TestRunBotOnceSimple::test_run_bot_once_historical_data_none": true, "tests/test_bot_orchestrator_simple.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_mode": true, "tests/test_bot_orchestrator_upload_log_file.py::TestUploadLogsToCloud::test_upload_logs_to_cloud_success": true, "tests/test_bot_orchestrator_upload_log_file.py::TestUploadLogsToCloud::test_upload_logs_to_cloud_partial_success": true, "tests/test_bot_orchestrator_upload_log_file.py::TestUploadLogsToCloud::test_upload_logs_to_cloud_all_failed": true, "tests/test_bot_orchestrator_upload_log_file.py::TestUploadLogsToCloud::test_upload_logs_to_cloud_no_files": true, "tests/test_check_and_log_closed_trades.py::TestPytestMode::test_history_deals_get_returns_none": true, "tests/test_check_and_log_closed_trades.py::TestPytestMode::test_process_deal_with_context": true, "tests/test_check_and_log_closed_trades.py::TestPytestMode::test_exception_handling": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_history_deals_get_returns_none": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_dataframe_creation_error": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_main_exception_handling": true, "tests/test_check_and_log_closed_trades.py::TestCheckAndLogClosedTradesErrorHandling::test_unexpected_deals_format": true, "tests/test_signal_generator_final_coverage.py::TestSignalGeneratorFinalCoverage::test_run_analysis_modules_with_all_features_enabled": true, "tests/test_signal_generator_final_coverage.py::TestSignalGeneratorFinalCoverage::test_get_knowledge_base_context_with_qdrant_available": true, "tests/test_signal_generator_final_coverage.py::TestSignalGeneratorFinalCoverage::test_generate_signal_with_gemini_unavailable": true, "tests/test_signal_generator_final_coverage.py::TestSignalGeneratorFinalCoverage::test_generate_signal_with_error": true, "tests/test_bot_orchestrator_simple_70.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_success": true, "tests/test_bot_orchestrator_simple_70.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_error": true, "tests/test_bot_orchestrator_simple_70.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_data": true, "tests/test_bot_orchestrator_simple_70.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_simple_70.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_error": true, "tests/test_bot_orchestrator_simple_70.py::TestRunBotOnce::test_run_bot_once_error": true, "tests/test_bot_orchestrator_simple_70.py::TestRunBot::test_run_bot_error": true, "tests/test_order_flow_analyzer/test_client.py": true, "tests/test_news_analyzer_90_coverage.py": true, "tests/test_news_analyzer_direct.py": true, "tests/test_news_analyzer_simple.py": true, "tests/test_news_analyzer.py": true, "tests/test_news_analyzer_isolated.py": true, "tests/backtester/test_backtester_service.py": true, "tests/backtester/test_order_simulator.py": true, "tests/test_aaa_sample.py::TestMetricsWithAAAPattern::test_net_profit": true, "tests/test_aaa_sample.py::TestMetricsWithAAAPattern::test_total_return_pct": true, "tests/test_aaa_sample.py::TestMetricsWithAAAPattern::test_batting_avg": true, "tests/test_aaa_sample.py::TestMetricsWithAAAPattern::test_profit_factor": true, "tests/test_bot_orchestrator.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_bot_orchestrator.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_missing_columns": true, "tests/test_bot_orchestrator.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_bot_orchestrator.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_70_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_70_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_70_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_invalid_csv": true, "tests/test_bot_orchestrator_70_coverage.py::TestRunBotErrors::test_run_bot_log_setup_error": true, "tests/test_bot_orchestrator_70_coverage_part2.py::TestRunBotMainLoopErrors::test_run_bot_macro_analysis_error": true, "tests/test_bot_orchestrator_70_coverage_part2.py::TestRunBotMainLoopErrors::test_run_bot_news_fetch_error": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_run_bot_once_sell_signal": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_market_closed": true, "tests/test_bot_orchestrator_70_coverage_part3.py::TestRunBotOnceSignalGeneration::test_symbol_info_none": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestUploadLogFile::test_upload_log_file_empty_list": true, "tests/test_bot_orchestrator_70_coverage_part4.py::TestRunBotOnceEdgeCases::test_run_bot_once_missing_symbol_info": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestRunBotOnce::test_run_bot_once_missing_symbol_info": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_with_data": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_no_symbol_data": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_exception": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestUploadLogFile::test_upload_log_file_empty_list": true, "tests/test_bot_orchestrator_70_coverage_part5.py::TestUploadLogFile::test_upload_log_file_exception": true, "tests/test_bot_orchestrator_80_coverage.py::TestUploadLogFile::test_upload_log_file_empty_list": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceData::test_load_performance_data_missing_columns": true, "tests/test_bot_orchestrator_combined.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_mode": true, "tests/test_bot_orchestrator_combined.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_success": true, "tests/test_bot_orchestrator_combined.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_found": true, "tests/test_bot_orchestrator_combined.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceDataErrors::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceDataErrors::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_combined.py::TestLoadPerformanceDataErrors::test_load_performance_data_invalid_csv": true, "tests/test_bot_orchestrator_combined.py::TestRunBotErrors::test_run_bot_log_setup_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotMainLoopErrors::test_run_bot_macro_analysis_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotMainLoopErrors::test_run_bot_news_fetch_error": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_run_bot_once_sell_signal": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_market_closed": true, "tests/test_bot_orchestrator_combined.py::TestRunBotOnceSignalGeneration::test_symbol_info_none": true, "tests/test_bot_orchestrator_combined.py::test_bot_orchestrator_module_loaded": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceData::test_load_performance_data_exception": true, "tests/test_bot_orchestrator_combined_coverage.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_combined_coverage.py::TestLoadPerformanceDataErrors::test_load_performance_data_invalid_csv": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotErrors::test_run_bot_log_setup_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotMainLoopErrors::test_run_bot_macro_analysis_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotMainLoopErrors::test_run_bot_news_fetch_error": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_run_bot_once_buy_signal": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_run_bot_once_sell_signal": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_market_closed": true, "tests/test_bot_orchestrator_combined_coverage.py::TestRunBotOnceSignalGeneration::test_symbol_info_none": true, "tests/test_bot_orchestrator_comprehensive.py::TestLoadPerformanceData::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_comprehensive.py::TestLoadPerformanceData::test_load_performance_data_missing_columns": true, "tests/test_bot_orchestrator_comprehensive.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_file_not_exists": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_no_matching_symbol": true, "tests/test_bot_orchestrator_get_perf_summary.py::TestGetRecentPerformanceSummary::test_exception": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_file_not_exists": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_empty_file": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_missing_columns": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_no_matching_symbol": true, "tests/test_bot_orchestrator_get_perf_summary_simple.py::TestGetRecentPerformanceSummary::test_exception": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_file_not_exists": true, "tests/test_bot_orchestrator_get_recent_performance_summary.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_exception": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_mode": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_file_not_found": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_missing_columns": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_no_matching_symbol": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_pytest_exception": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_normal_success": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_normal_empty_file": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_normal_missing_columns": true, "tests/test_bot_orchestrator_helpers.py::TestGetRecentPerformanceSummary::test_get_recent_performance_summary_normal_no_matching_symbol": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_normal_mode_no_deals": true, "tests/test_bot_orchestrator_helpers.py::TestCheckAndLogClosedTrades::test_check_and_log_closed_trades_normal_mode_success": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_file_not_exists": true, "tests/test_bot_orchestrator_load_perf_data.py::TestLoadPerformanceData::test_load_performance_data_empty_file": true, "tests/test_bot_orchestrator_load_performance_data_simple.py::TestLoadPerformanceDataSimple::test_file_not_found": true, "tests/test_bot_orchestrator_load_performance_data_simple.py::TestLoadPerformanceDataSimple::test_empty_file": true, "tests/test_bot_orchestrator_load_performance_data_simple.py::TestLoadPerformanceDataSimple::test_successful_load": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_get_recent_performance_summary_file_not_exists": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_get_recent_performance_summary_empty_file": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_get_recent_performance_summary_exception": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_check_and_log_closed_trades_no_deals": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_check_and_log_closed_trades_mt5_error": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_check_and_log_closed_trades_exception": true, "tests/test_bot_orchestrator_simple_functions.py::TestBotOrchestratorSimpleFunctions::test_run_bot_once_market_closed": true, "tests/test_bot_orchestrator_trade_context.py": true, "tests/test_check_and_log_closed_trades.py": true, "tests/test_close_existing_positions.py::TestCloseExistingPositions::test_successful_close": true, "tests/test_close_existing_positions.py::TestCloseExistingPositions::test_no_positions_to_close": true, "tests/test_close_existing_positions.py::TestCloseExistingPositions::test_failed_close": true, "tests/test_close_existing_positions.py::TestCloseExistingPositions::test_exception_handling": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_trend_status_none_input": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_trend_status_empty_df": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_trend_status_missing_close_column": true, "tests/test_combined_analyzers.py::TestTrendAnalyzer::test_get_mta_context_both_timeframes": true, "tests/test_combined_analyzers.py::TestSentimentAnalyzer::test_fetch_alphavantage_sentiment_success": true, "tests/test_combined_analyzers.py::TestSentimentAnalyzer::test_fetch_news_headlines_stub": true, "tests/test_combined_analyzers.py::TestSentimentAnalyzer::test_get_sentiment_context_cache_hit": true, "tests/test_combined_analyzers.py::TestVolatilityForecaster::test_calculate_returns_invalid_data": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_throttling": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_api_limit_reached": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_error_message": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_success": true, "tests/test_combined_coverage.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_combined_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_stub": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_empty_texts": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bullish": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bearish": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_neutral": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bullish": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bearish": true, "tests/test_combined_coverage.py::TestCalculateSentimentScore::test_calculate_sentiment_score_unsupported_method": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_disabled": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_cache_hit": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_success": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_error": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_no_news": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_vader_no_headlines": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_method_none": true, "tests/test_combined_coverage.py::TestGetSentimentContext::test_get_sentiment_context_unsupported_method": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_none_dataframe": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_empty_dataframe": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_missing_close_column": true, "tests/test_combined_coverage.py::TestCalculateReturns::test_calculate_returns_insufficient_data": true, "tests/test_combined_coverage.py::TestFitGarchModel::test_fit_garch_model_none_series": true, "tests/test_combined_coverage.py::TestFitGarchModel::test_fit_garch_model_insufficient_data": true, "tests/test_combined_coverage.py::TestForecastVolatility::test_forecast_volatility_none_result": true, "tests/test_combined_coverage.py::TestForecastVolatility::test_forecast_volatility_forecast_error": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_none_dataframe": true, "tests/test_combined_coverage.py::TestGetVolatilityContext::test_get_volatility_context_insufficient_data": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_success": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_error": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_note": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_json_error": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_combined_coverage.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_correlation_matrix/test_client.py::test_correlation_matrix_client_init": true, "tests/test_correlation_matrix/test_client.py::test_get_correlation_matrix": true, "tests/test_correlation_matrix/test_client.py::test_get_correlation": true, "tests/test_correlation_matrix/test_client.py::test_get_correlations_for_symbol": true, "tests/test_correlation_matrix/test_client.py::test_get_highly_correlated_pairs": true, "tests/test_correlation_matrix/test_client.py::test_get_weakly_correlated_pairs": true, "tests/test_correlation_matrix/test_client.py::test_plot_correlation_matrix": true, "tests/test_correlation_matrix/test_client.py::test_plot_correlation_network": true, "tests/test_correlation_matrix/test_client.py::test_clear_cache": true, "tests/test_correlation_matrix/test_client.py::test_get_correlation_matrix_client": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_none_input": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_empty_input": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_nan_values": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_pandas_ta_returns_empty": true, "tests/test_heikin_ashi_calculator_additional.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextErrorHandling::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextErrorHandling::test_get_ha_context_none_input": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextErrorHandling::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextErrorHandling::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextErrorHandling::test_get_ha_context_not_enough_ha_bars": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextTrendAnalysis::test_get_ha_context_strong_up_trend": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextTrendAnalysis::test_get_ha_context_strong_down_trend": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextTrendAnalysis::test_get_ha_context_weak_up_trend": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextTrendAnalysis::test_get_ha_context_weak_down_trend": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextTrendAnalysis::test_get_ha_context_indeterminate_trend": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextPatternAnalysis::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_calculator_additional.py::TestGetHaContextPatternAnalysis::test_get_ha_context_neutral_color": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_success": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_empty_df": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_with_nan": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_strong_uptrend": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_strong_downtrend": true, "tests/test_heikin_ashi_calculator_comprehensive.py::TestGetHAContext::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_success": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_empty_df": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_with_nan": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshi::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_strong_uptrend": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_strong_downtrend": true, "tests/test_heikin_ashi_combined.py::TestGetHAContext::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_none_input": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_empty_input": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_missing_columns": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_pandas_ta_returns_empty": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiErrorHandling::test_calculate_heikin_ashi_exception": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextErrorHandling::test_get_ha_context_disabled": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextErrorHandling::test_get_ha_context_none_input": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextErrorHandling::test_get_ha_context_insufficient_data": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextErrorHandling::test_get_ha_context_calculation_failed": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextErrorHandling::test_get_ha_context_not_enough_ha_bars": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextTrendAnalysis::test_get_ha_context_strong_up_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextTrendAnalysis::test_get_ha_context_strong_down_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextTrendAnalysis::test_get_ha_context_weak_up_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextTrendAnalysis::test_get_ha_context_weak_down_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextTrendAnalysis::test_get_ha_context_indeterminate_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextPatternAnalysis::test_get_ha_context_doji_pattern": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextPatternAnalysis::test_get_ha_context_neutral_color": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiEdgeCases::test_calculate_heikin_ashi_with_exception_in_pandas_ta": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiEdgeCases::test_calculate_heikin_ashi_with_column_rename_error": true, "tests/test_heikin_ashi_combined.py::TestCalculateHeikinAshiEdgeCases::test_calculate_heikin_ashi_with_to_numeric_error": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextEdgeCases::test_get_ha_context_with_weak_up_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextEdgeCases::test_get_ha_context_with_weak_down_trend": true, "tests/test_heikin_ashi_combined.py::TestGetHaContextEdgeCases::test_get_ha_context_with_neutral_last_color": true, "tests/test_indicators.py::TestCalculateIndicators::test_calculate_indicators_success": true, "tests/test_indicators.py::TestCalculateIndicators::test_calculate_indicators_with_candlestick_patterns": true, "tests/test_indicators.py::TestCalculateIndicators::test_calculate_indicators_candlestick_patterns_error": true, "tests/test_integration_signal_generation.py::TestSignalGenerationWorkflow::test_build_comprehensive_prompt": true, "tests/test_integration_signal_generation.py::TestSignalGenerationWorkflow::test_integrated_signal_generation": true, "tests/test_integration_signal_generation.py::TestSignalGenerationWorkflow::test_historical_data_processing": true, "tests/test_log_manager.py::TestLogManager::test_init": true, "tests/test_log_manager.py::TestLogManager::test_setup_logging": true, "tests/test_log_manager.py::TestLogManager::test_setup_logging_exception": true, "tests/test_log_manager.py::TestLogManager::test_initialize_perf_log_header_new_file": true, "tests/test_log_manager.py::TestLogManager::test_initialize_perf_log_header_existing_file": true, "tests/test_log_manager.py::TestLogManager::test_create_logger_adapter": true, "tests/test_log_manager.py::TestLogManager::test_upload_log_file_success": true, "tests/test_log_manager.py::TestLogManager::test_upload_log_file_failure": true, "tests/test_log_manager.py::TestLogManager::test_upload_log_file_rclone_not_available": true, "tests/test_log_manager.py::TestLogManager::test_upload_log_file_file_not_found": true, "tests/test_log_manager.py::TestLogManager::test_upload_log_file_exception": true, "tests/test_log_manager.py::TestLogManager::test_upload_logs_to_cloud_success": true, "tests/test_log_manager.py::TestLogManager::test_upload_logs_to_cloud_partial_success": true, "tests/test_log_manager.py::TestLogManager::test_upload_logs_to_cloud_no_remote_config": true, "tests/test_log_manager.py::TestLogManager::test_upload_logs_to_cloud_no_files": true, "tests/test_log_manager.py::TestLogManager::test_cleanup_logs": true, "tests/test_log_manager.py::TestLogManager::test_cleanup_logs_exception": true, "tests/test_log_manager.py::TestLogManager::test_close_handlers": true, "tests/test_log_manager.py::TestLogManager::test_close_handlers_exception": true, "tests/test_log_manager.py::TestModuleFunctions::test_setup_logging": true, "tests/test_log_manager.py::TestModuleFunctions::test_upload_log_file_function": true, "tests/test_log_manager.py::TestModuleFunctions::test_upload_logs_to_cloud_function": true, "tests/test_log_manager.py::TestModuleFunctions::test_create_logger_adapter_function": true, "tests/test_log_manager.py::TestModuleFunctions::test_cleanup_logs_function": true, "tests/test_macro_analyzer_additional.py::TestAnalyzerConfig::test_config_valid_thresholds": true, "tests/test_macro_analyzer_additional.py::TestAnalyzerConfig::test_classify_macro_regime_risk_off": true, "tests/test_macro_analyzer_additional.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on": true, "tests/test_macro_analyzer_additional.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_additional.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_disabled": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_fetch_error": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_classify_macro_regime_invalid_vix": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_classify_macro_regime_medium_impact_event": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_classify_macro_regime_invalid_rate_trends": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_classify_macro_regime_main_block": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_cache_update": true, "tests/test_macro_analyzer_additional.py::TestGetMacroContext::test_get_macro_context_all_fetch_error": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_missing_time": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_fred_rate_trends_error_handling": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_disabled": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_no_api_key": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_unknown_provider": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_request_exception": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_json_exception": true, "tests/test_macro_analyzer_additional.py::TestFetcherAdditional::test_fetch_economic_calendar_events_main_block": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_high_vix": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_rising_rates": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on_low_vix": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_medium_event_risk": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_invalid_vix": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestClassifyMacroRegime::test_classify_macro_regime_none_rate_trends": true, "tests/test_macro_analyzer_analyzer_comprehensive.py::TestGetMacroContext::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_analyzer_final_90.py::TestConfigurationLoading::test_config_loading_value_error": true, "tests/test_macro_analyzer_analyzer_final_90.py::TestConfigurationLoading::test_config_loading_general_exception": true, "tests/test_macro_analyzer_analyzer_final_90.py::TestConfigurationLoading::test_config_validation_warning": true, "tests/test_macro_analyzer_analyzer_final_90.py::TestGetMacroContextEdgeCases::test_get_macro_context_with_rates_fetch_exception": true, "tests/test_macro_analyzer_analyzer_final_90.py::TestGetMacroContextEdgeCases::test_get_macro_context_with_events_fetch_exception": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_disabled": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_no_api_key": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_success": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_error": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_malformed_data": true, "tests/test_macro_analyzer_calendar.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_empty_response": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_high_vix": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_rising_rates": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on_low_vix": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_medium_event_risk": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_invalid_vix": true, "tests/test_macro_analyzer_classify.py::TestClassifyMacroRegime::test_classify_macro_regime_none_rate_trends": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_high_vix": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_rising_rates": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on_low_vix": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_medium_event_risk": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_invalid_vix": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_nan_vix": true, "tests/test_macro_analyzer_combined.py::TestClassifyMacroRegime::test_classify_macro_regime_none_rate_trends": true, "tests/test_macro_analyzer_combined.py::TestGetMacroContext::test_get_macro_context_disabled": true, "tests/test_macro_analyzer_combined.py::TestGetMacroContext::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_combined.py::TestGetMacroContext::test_get_macro_context_fresh_data": true, "tests/test_macro_analyzer_combined.py::TestGetMacroContext::test_get_macro_context_partial_fetch_failure": true, "tests/test_macro_analyzer_combined.py::TestGetMacroContext::test_get_macro_context_vix_fetch_failure": true, "tests/test_macro_analyzer_context.py::TestGetMacroContext::test_get_macro_context_cache_hit": true, "tests/test_macro_analyzer_context.py::TestGetMacroContext::test_get_macro_context_fresh_data": true, "tests/test_macro_analyzer_context.py::TestGetMacroContext::test_get_macro_context_partial_fetch_failure": true, "tests/test_macro_analyzer_context.py::TestGetMacroContext::test_get_macro_context_vix_fetch_failure": true, "tests/test_macro_analyzer_fetcher.py::TestFetchVixLevel::test_fetch_vix_level_cache_hit": true, "tests/test_macro_analyzer_fetcher.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_intraday_success": true, "tests/test_macro_analyzer_fetcher.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_daily_fallback": true, "tests/test_macro_analyzer_fetcher.py::TestFetchVixLevel::test_fetch_vix_level_fred_fallback": true, "tests/test_macro_analyzer_fetcher.py::TestFetchVixLevel::test_fetch_vix_level_all_sources_fail": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_insufficient_data": true, "tests/test_macro_analyzer_fetcher.py::TestCalculateSlope::test_calculate_slope_none_input": true, "tests/test_macro_analyzer_fetcher.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_insufficient_data": true, "tests/test_macro_analyzer_fetcher.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_data_error": true, "tests/test_macro_analyzer_fetcher.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_disabled": true, "tests/test_macro_analyzer_fetcher.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_no_api_key": true, "tests/test_macro_analyzer_fetcher.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_success": true, "tests/test_macro_analyzer_fetcher.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_error": true, "tests/test_macro_analyzer_fetcher.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_malformed_data": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_empty_series": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_single_value": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestCalculateSlope::test_calculate_slope_with_all_nan_values": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchVixLevel::test_fetch_vix_level_cache_hit": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_intraday": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchVixLevel::test_fetch_vix_level_yfinance_daily": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchVixLevel::test_fetch_vix_level_fred_fallback": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchVixLevel::test_fetch_vix_level_all_sources_fail": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_insufficient_data": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_empty_data": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_api_error": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_disabled": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_no_api_key": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_success": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_error": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_malformed_data": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_empty_response": true, "tests/test_macro_analyzer_fetcher_comprehensive.py::TestFetchEconomicCalendarEvents::test_fetch_economic_calendar_events_finnhub_json_error": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestConfigurationLoading::test_config_loading_value_error": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestConfigurationLoading::test_config_validation_warning": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_empty_yf_intraday_data": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_empty_yf_daily_data": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_none_yf_value": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_empty_fred_data": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_none_fred_value": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchVixLevelEdgeCases::test_fetch_vix_level_with_non_finite_value": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchFredRateTrendsEdgeCases::test_fetch_fred_rate_trends_with_empty_ticker_list": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchFredRateTrendsEdgeCases::test_fetch_fred_rate_trends_with_empty_ticker_string": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchFredRateTrendsEdgeCases::test_fetch_fred_rate_trends_with_none_slope": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchEconomicCalendarEventsEdgeCases::test_fetch_economic_calendar_events_with_invalid_event_time": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchEconomicCalendarEventsEdgeCases::test_fetch_economic_calendar_events_with_missing_time": true, "tests/test_macro_analyzer_fetcher_final_90.py::TestFetchEconomicCalendarEventsEdgeCases::test_fetch_economic_calendar_events_with_country_mapping": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_empty_series": true, "tests/test_macro_analyzer_fetcher_simple.py::TestCalculateSlope::test_calculate_slope_single_value_series": true, "tests/test_macro_analyzer_fetcher_simple.py::TestFetchFredRateTrends::test_fetch_fred_rate_trends_error": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_off_high_vix": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_risk_on_low_vix": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_neutral": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_high_event_risk": true, "tests/test_macro_analyzer_simple.py::TestClassifyMacroRegime::test_classify_macro_regime_invalid_vix": true, "tests/test_market_hours_session_info.py::TestGetSessionSettings::test_get_session_settings_creates_instance": true, "tests/test_market_hours_session_info.py::TestGetSessionSettings::test_get_session_settings_caches_instance": true, "tests/test_models_train_hmm.py::TestLoadTrainingData::test_load_training_data_file_not_found": true, "tests/test_models_train_hmm.py::TestLoadTrainingData::test_load_training_data_missing_columns": true, "tests/test_models_train_hmm.py::TestCalculateFeatures::test_calculate_features_empty_dataframe": true, "tests/test_models_train_hmm.py::TestCalculateFeatures::test_calculate_features_missing_close_column": true, "tests/test_models_train_hmm.py::TestHMMTraining::test_main_training_flow": true, "tests/test_mt5_event_producer.py": true, "tests/test_multilingual_news_90_coverage.py::test_fetch_news_status_codes[forex-en-0-200]": true, "tests/test_multilingual_news_90_coverage.py::test_fetch_news_status_codes[forex-en-0-401]": true, "tests/test_multilingual_news_90_coverage.py::test_fetch_news_status_codes[forex-en-0-500]": true, "tests/test_multilingual_news_90_coverage.py::test_fetch_news_exception": true, "tests/test_multilingual_news_90_coverage.py::test_fetch_news_by_currency_empty": true, "tests/test_multilingual_news_90_coverage.py::test_analyze_sentiment_empty_text": true, "tests/test_multilingual_news_90_coverage.py::test_analyze_sentiment_exception": true, "tests/test_multilingual_news_90_coverage.py::test_get_news_context_exception": true, "tests/test_multilingual_news_direct.py": true, "tests/test_news_analyzer_90_coverage_part2.py": true, "tests/test_performance_analyzer_metrics.py::TestBasicMetrics::test_net_profit": true, "tests/test_performance_analyzer_metrics.py::TestBasicMetrics::test_total_return_pct": true, "tests/test_performance_analyzer_metrics.py::TestBasicMetrics::test_calculate_batting_avg": true, "tests/test_performance_analyzer_metrics.py::TestBasicMetrics::test_profit_factor": true, "tests/test_performance_analyzer_metrics.py::TestBasicMetrics::test_calculate_avg_win_loss": true, "tests/test_performance_analyzer_metrics.py::TestDealBasedMetrics::test_calculate_consecutive_wins_losses": true, "tests/test_performance_analyzer_metrics.py::TestDealBasedMetrics::test_calculate_avg_trade_duration": true, "tests/test_performance_analyzer_metrics.py::TestEquityCurveMetrics::test_calculate_returns": true, "tests/test_performance_analyzer_metrics.py::TestEquityCurveMetrics::test_annual_volatility": true, "tests/test_performance_analyzer_metrics.py::TestEquityCurveMetrics::test_calculate_drawdown_recovery_times": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_sharpe_ratio": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_sortino_ratio": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_max_drawdown": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_skewness": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_kurtosis": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_var": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_cvar": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_alpha_beta": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_calculate_capture_ratios": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_empyrical_metrics_when_library_unavailable": true, "tests/test_performance_analyzer_metrics.py::TestEmpyricalBasedMetrics::test_aligned_daily_returns": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_annual_volatility_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_annual_volatility_empty_equity": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_annual_volatility_zero_std_dev": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sharpe_ratio_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sharpe_ratio_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sharpe_ratio_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sortino_ratio_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sortino_ratio_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_sortino_ratio_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_max_drawdown_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_max_drawdown_positive_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_max_drawdown_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_kurtosis_empty_equity": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_kurtosis_library_unavailable": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_var_invalid_confidence_level": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_var_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_var_positive_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_var_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_cvar_invalid_confidence_level": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_cvar_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_cvar_positive_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_cvar_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_alpha_beta_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_alpha_beta_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_alpha_beta_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_aligned_daily_returns_invalid_inputs": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_aligned_daily_returns_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_aligned_daily_returns_no_overlap": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_aligned_daily_returns_insufficient_data": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_returns_with_non_series_input": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_returns_with_single_value": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_profit_factor_edge_cases": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_skewness_empty_equity": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_skewness_library_unavailable": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_capture_ratios_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_capture_ratios_library_unavailable": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_capture_ratios_no_aligned_returns": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_capture_ratios_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_capture_ratios_exception": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_avg_trade_duration_with_nan_values": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_avg_trade_duration_with_all_nan_values": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_consecutive_wins_losses_with_zeros": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_consecutive_wins_losses_empty_df": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_consecutive_wins_losses_all_zeros": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_avg_trade_duration_missing_columns": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_avg_trade_duration_empty_df": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_batting_avg_missing_profit_column": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_batting_avg_empty_df": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_calculate_batting_avg_non_numeric_profits": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_profit_factor_missing_profit_column": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_profit_factor_empty_df": true, "tests/test_performance_analyzer_metrics_90_coverage.py::TestErrorHandlingInMetrics::test_profit_factor_non_numeric_profits": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_capture_ratios_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_capture_ratios_empty_returns": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_capture_ratios_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_capture_ratios_exception": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_alpha_beta_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_alpha_beta_empty_returns": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_alpha_beta_non_finite_result": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCaptureRatiosAndAlphaBeta::test_calculate_alpha_beta_exception": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_returns_empty_equity": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_returns_single_value": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_aligned_daily_returns_empty_series": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_aligned_daily_returns_non_overlapping": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_aligned_daily_returns_partial_overlap": true, "tests/test_performance_analyzer_metrics_90_coverage_part2.py::TestCalculateReturnsAndAlignedReturns::test_calculate_aligned_daily_returns_different_frequencies": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_net_profit_with_non_numeric_values": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_annual_volatility_with_single_value": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_annual_volatility_with_all_nan_returns": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_batting_avg_with_all_nan_profits": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_batting_avg_with_mixed_profits": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_avg_win_loss_with_no_wins": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_avg_win_loss_with_no_losses": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_avg_win_loss_with_mixed_values": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_consecutive_wins_losses_with_mixed_values": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_avg_trade_duration_with_missing_columns": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_avg_trade_duration_with_invalid_dates": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_drawdown_recovery_times_with_empty_equity": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_drawdown_recovery_times_with_single_value": true, "tests/test_performance_analyzer_metrics_final_90.py::TestRemainingEdgeCases::test_calculate_drawdown_recovery_times_with_no_drawdowns": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_net_profit_warning_for_non_numeric_values": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_annual_volatility_zero_std_dev_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_var_invalid_confidence_level_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_cvar_invalid_confidence_level_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_alpha_beta_invalid_trading_days_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_capture_ratios_invalid_trading_days_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_aligned_daily_returns_insufficient_data_log": true, "tests/test_performance_analyzer_metrics_final_90_part2.py::TestRemainingLines::test_calculate_aligned_daily_returns_no_overlap_log": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_aligned_daily_returns_mismatched_dates": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_aligned_daily_returns_no_overlap": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_var_invalid_confidence": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_cvar_invalid_confidence": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_profit_factor_with_invalid_data": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_calculate_alpha_beta_exception_handling": true, "tests/test_performance_analyzer_metrics_final_coverage.py::TestEdgeCasesAndErrorHandling::test_logger_calls": true, "tests/test_performance_analyzer_metrics_simple.py::TestVaRAndCVaR::test_calculate_var_invalid_confidence": true, "tests/test_performance_analyzer_metrics_simple.py::TestVaRAndCVaR::test_calculate_cvar_invalid_confidence": true, "tests/test_performance_analyzer_metrics_simple.py::TestAlphaBetaAndCapture::test_calculate_alpha_beta_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_simple.py::TestAlphaBetaAndCapture::test_calculate_capture_ratios_invalid_trading_days": true, "tests/test_performance_analyzer_metrics_simple.py::TestAlphaBetaAndCapture::test_calculate_aligned_daily_returns_no_overlap": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_init": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_init_default_perf_adapter": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_load_performance_data_file_not_found": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_load_performance_data_empty_file": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_load_performance_data_csv_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_load_performance_data_missing_columns": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_load_performance_data_date_conversion_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_no_data": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_with_data": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_missing_columns": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_production": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_production_real": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_production_empty_df": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_production_no_matching_symbol": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_empty_df": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_empty_symbol_df": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_valid_data": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_positive_sell_pl": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_negative_buy_pl": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_default_num_trades": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_with_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_with_empty_dataframe": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_with_no_symbol_data": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_store_and_get_trade_context": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_with_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_mt5_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_no_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_already_processed_deal": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_exception_in_test_mode": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_no_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_real": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_empty_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_deal_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message_no_context": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_real_implementation": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_with_context": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_with_deal_processing_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_with_overall_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_with_no_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_with_mt5_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_mocked_methods": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_already_processed_deal": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_no_deals": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_mt5_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_deal_processing_error": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_overall_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message_timestamp_exception": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message_with_all_context": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message_with_partial_context": true, "tests/test_performance_tracker.py::TestPerformanceTracker::test_format_performance_log_message_with_empty_context_fields": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_load_performance_data_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_recent_performance_summary_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_check_and_log_closed_trades_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_store_trade_entry_context_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_trade_entry_context_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_clear_trade_entry_context_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_processed_deal_tickets_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_clear_all_tracked_data_for_testing_module_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_recent_performance_summary_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_check_and_log_closed_trades_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_store_trade_entry_context_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_trade_entry_context_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_clear_trade_entry_context_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_get_processed_deal_tickets_function": true, "tests/test_performance_tracker.py::TestModuleFunctions::test_clear_all_tracked_data_for_testing_function": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_init": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_init_default_perf_adapter": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_load_performance_data_file_not_found": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_load_performance_data_empty_file": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_load_performance_data_csv_error": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_load_performance_data_missing_columns": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_load_performance_data_date_conversion_error": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_no_data": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_with_data": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_missing_columns": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_no_matching_symbol": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_production": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_production_real": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_production_empty_df": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_production_no_matching_symbol": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_empty_df": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_empty_symbol_df": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_valid_data": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_positive_sell_pl": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_negative_buy_pl": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_get_recent_performance_summary_real_implementation_with_default_num_trades": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_store_and_get_trade_context": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_with_deals": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_mt5_error": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_no_deals": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_test_mode_with_already_processed_deal": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_exception_in_test_mode": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_no_deals": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_real": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_empty_deals": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_deal_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message_no_context": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_production_mode_real_implementation": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_mocked_methods": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_already_processed_deal": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_no_deals": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_mt5_error": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_deal_processing_error": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_check_and_log_closed_trades_real_implementation_with_overall_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message_timestamp_exception": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message_with_all_context": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message_with_partial_context": true, "tests/test_performance_tracker_new.py::TestPerformanceTracker::test_format_performance_log_message_with_empty_context_fields": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_load_performance_data_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_recent_performance_summary_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_check_and_log_closed_trades_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_store_trade_entry_context_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_trade_entry_context_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_clear_trade_entry_context_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_processed_deal_tickets_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_clear_all_tracked_data_for_testing_module_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_recent_performance_summary_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_check_and_log_closed_trades_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_store_trade_entry_context_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_trade_entry_context_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_clear_trade_entry_context_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_get_processed_deal_tickets_function": true, "tests/test_performance_tracker_new.py::TestModuleFunctions::test_clear_all_tracked_data_for_testing_function": true, "tests/test_position_sizer_additional.py::TestKellyParametersAdditional::test_estimate_kelly_parameters_no_hmm_state": true, "tests/test_position_sizer_additional.py::TestKellyParametersAdditional::test_estimate_kelly_parameters_data_inconsistency": true, "tests/test_position_sizer_additional.py::TestPositionSizeAdditional::test_calculate_position_size_unknown_method": true, "tests/test_position_sizer_additional.py::TestPositionSizeAdditional::test_calculate_position_size_kelly_missing_symbol": true, "tests/test_position_sizer_additional.py::TestPositionSizeAdditional::test_calculate_position_size_kelly_missing_adapter": true, "tests/test_position_sizer_additional.py::TestMainBlock::test_main_block": true, "tests/test_position_sizer_combined.py::TestElderVolume::test_calculate_elder_volume_valid_inputs": true, "tests/test_position_sizer_combined.py::TestElderVolume::test_calculate_elder_volume_invalid_inputs": true, "tests/test_position_sizer_combined.py::TestElderVolume::test_calculate_elder_volume_with_logger": true, "tests/test_position_sizer_combined.py::TestElderVolume::test_calculate_elder_volume_edge_cases": true, "tests/test_position_sizer_combined.py::TestKellyParameters::test_estimate_kelly_parameters_valid_data": true, "tests/test_position_sizer_combined.py::TestKellyParameters::test_estimate_kelly_parameters_insufficient_trades": true, "tests/test_position_sizer_combined.py::TestKellyParameters::test_estimate_kelly_parameters_file_not_found": true, "tests/test_position_sizer_combined.py::TestKellyParameters::test_estimate_kelly_parameters_adaptive": true, "tests/test_position_sizer_combined.py::TestKellyParameters::test_estimate_kelly_parameters_all_wins": true, "tests/test_position_sizer_combined.py::TestKellyVolume::test_calculate_kelly_volume_valid_inputs": true, "tests/test_position_sizer_combined.py::TestKellyVolume::test_calculate_kelly_volume_invalid_inputs": true, "tests/test_position_sizer_combined.py::TestKellyVolume::test_calculate_kelly_volume_edge_cases": true, "tests/test_position_sizer_combined.py::TestPositionSize::test_calculate_position_size_elder_method": true, "tests/test_position_sizer_combined.py::TestPositionSize::test_calculate_position_size_kelly_method": true, "tests/test_position_sizer_combined.py::TestPositionSize::test_calculate_position_size_kelly_fallback": true, "tests/test_position_sizer_combined.py::TestPositionSize::test_calculate_position_size_missing_args": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_success": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_invalid_inputs": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_min_volume_constraint": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_max_volume_constraint": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_volume_step": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_zero_volume_step": true, "tests/test_position_sizer_comprehensive.py::TestCalculateElderVolume::test_calculate_elder_volume_no_adapter": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_file_not_found": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_missing_columns": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_insufficient_trades": true, "tests/test_position_sizer_comprehensive.py::TestEstimateKellyParameters::test_estimate_kelly_parameters_exception": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_success": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_invalid_params": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_zero_edge": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_infinite_b": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_clamping": true, "tests/test_position_sizer_comprehensive.py::TestCalculateKellyVolume::test_calculate_kelly_volume_elder_failure": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_elder_default": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_elder_explicit": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_elder_custom_risk": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_missing_args": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_success": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_adaptive": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_disabled": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_missing_symbol": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_estimation_failure": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_kelly_volume_failure": true, "tests/test_position_sizer_comprehensive.py::TestCalculatePositionSize::test_calculate_position_size_unsupported_method": true, "tests/test_position_sizer_final_90_coverage.py::TestConfigurationLoading::test_config_loading_exception": true, "tests/test_position_sizer_final_90_coverage.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_with_zero_volume_step": true, "tests/test_position_sizer_final_90_coverage.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_with_invalid_input_types": true, "tests/test_position_sizer_final_90_coverage.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_with_default_logger": true, "tests/test_position_sizer_final_90_coverage.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_with_invalid_kelly_fraction": true, "tests/test_position_sizer_final_90_coverage.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_with_negative_optimal_fraction": true, "tests/test_position_sizer_final_90_coverage.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_with_clamped_fraction": true, "tests/test_position_sizer_final_90_coverage.py::TestPositionSizeEdgeCases::test_calculate_position_size_with_invalid_method": true, "tests/test_position_sizer_final_90_coverage.py::TestPositionSizeEdgeCases::test_calculate_position_size_with_default_logger": true, "tests/test_position_sizer_final_90_coverage.py::TestPositionSizeEdgeCases::test_calculate_position_size_kelly_with_no_adapter": true, "tests/test_position_sizer_sizer_90_plus.py::TestKellyFractionValidation::test_kelly_fraction_validation": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateElderVolumeEdgeCases::test_calculate_elder_volume_invalid_input_types": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateElderVolumeEdgeCases::test_calculate_elder_volume_negative_account_equity": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateElderVolumeEdgeCases::test_calculate_elder_volume_zero_stop_loss": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateElderVolumeEdgeCases::test_calculate_elder_volume_invalid_volume_range": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateKellyVolumeEdgeCases::test_calculate_kelly_volume_invalid_kelly_fraction": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateKellyVolumeEdgeCases::test_calculate_kelly_volume_invalid_kelly_params": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculateKellyVolumeEdgeCases::test_calculate_kelly_volume_excessive_f_actual": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_missing_args": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_no_adapter": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_invalid_method": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_kelly_estimation_failed": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_kelly_calculation_failed": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_kelly_adaptive": true, "tests/test_position_sizer_sizer_90_plus.py::TestCalculatePositionSizeEdgeCases::test_calculate_position_size_non_elder_method_failed": true, "tests/test_position_sizer_sizer_90_plus.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_file_not_found": true, "tests/test_position_sizer_sizer_90_plus.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_adaptive_no_state": true, "tests/test_position_sizer_sizer_90_plus.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_adaptive_missing_column": true, "tests/test_position_sizer_sizer_final.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_zero_risk_per_lot": true, "tests/test_position_sizer_sizer_final.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_raw_volume_less_than_min": true, "tests/test_position_sizer_sizer_final.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_zero_volume_step": true, "tests/test_position_sizer_sizer_final.py::TestElderVolumeEdgeCases::test_calculate_elder_volume_final_volume_less_than_min": true, "tests/test_position_sizer_sizer_final.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_zero_edge": true, "tests/test_position_sizer_sizer_final.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_negative_edge": true, "tests/test_position_sizer_sizer_final.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_very_small_b": true, "tests/test_position_sizer_sizer_final.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_infinite_b": true, "tests/test_position_sizer_sizer_final.py::TestKellyVolumeEdgeCases::test_calculate_kelly_volume_kelly_derived_volume_none": true, "tests/test_position_sizer_sizer_final.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_no_trades": true, "tests/test_position_sizer_sizer_final.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_all_losses": true, "tests/test_position_sizer_sizer_final.py::TestEstimateKellyParametersEdgeCases::test_estimate_kelly_parameters_all_wins": true, "tests/test_regime_detector_hmm_model.py::TestLoadHmmModel::test_load_hmm_model_from_cache": true, "tests/test_regime_detector_hmm_model.py::TestLoadHmmModel::test_load_hmm_model_from_file": true, "tests/test_regime_detector_hmm_model.py::TestLoadHmmModel::test_load_hmm_model_file_not_found": true, "tests/test_regime_detector_hmm_model.py::TestLoadHmmModel::test_load_hmm_model_state_mismatch": true, "tests/test_regime_detector_hmm_model.py::TestLoadHmmModel::test_load_hmm_model_load_error": true, "tests/test_regime_detector_hmm_model.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_valid_data": true, "tests/test_regime_detector_hmm_model.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_none_input": true, "tests/test_regime_detector_hmm_model.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_insufficient_data": true, "tests/test_regime_detector_hmm_model.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_missing_close": true, "tests/test_regime_detector_hmm_model.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_nan_close": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_valid_input": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_none_model": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_none_features": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_empty_features": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_non_finite_features": true, "tests/test_regime_detector_hmm_model.py::TestPredictHmmRegime::test_predict_hmm_regime_prediction_error": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_disabled": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_model_load_failed": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_no_data": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_insufficient_data": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_feature_calculation_failed": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_successful_prediction": true, "tests/test_regime_detector_hmm_model.py::TestGetHmmContext::test_get_hmm_context_prediction_failed": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestLoadHMMModel::test_load_hmm_model_success": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_success": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_empty_dataframe": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_insufficient_data": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestCalculatePredictionFeatures::test_calculate_prediction_features_missing_close": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestPredictHMMRegime::test_predict_hmm_regime_success": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestPredictHMMRegime::test_predict_hmm_regime_no_model": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestPredictHMMRegime::test_predict_hmm_regime_exception": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestGetHMMContext::test_get_hmm_context_success": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestGetHMMContext::test_get_hmm_context_disabled": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestGetHMMContext::test_get_hmm_context_no_model": true, "tests/test_regime_detector_hmm_model_comprehensive.py::TestGetHMMContext::test_get_hmm_context_feature_calculation_failed": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_timeout_error": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_api_error": true, "tests/test_sentiment_analyzer_90_coverage.py::TestFetchNewsHeadlinesAdditional::test_fetch_news_headlines_basic": true, "tests/test_sentiment_analyzer_90_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_empty_texts": true, "tests/test_sentiment_analyzer_90_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_exception": true, "tests/test_sentiment_analyzer_additional.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_additional.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_sentiment_analyzer_additional.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_additional.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_invalid_scores": true, "tests/test_sentiment_analyzer_additional.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_invalid_items": true, "tests/test_sentiment_analyzer_additional.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_vader_bearish": true, "tests/test_sentiment_analyzer_additional.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_transformer_bearish": true, "tests/test_sentiment_analyzer_additional.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_exception": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_error": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_no_news": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_vader_no_headlines": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_transformer_no_headlines": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_none_method": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_invalid_method": true, "tests/test_sentiment_analyzer_additional.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_score_ranges": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_success": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_api_error": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_api_limit": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_no_relevant_ticker": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_analyzer.py::TestFetchNewsHeadlines::test_fetch_news_headlines_stub": true, "tests/test_sentiment_analyzer_analyzer.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader": true, "tests/test_sentiment_analyzer_analyzer.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer": true, "tests/test_sentiment_analyzer_analyzer.py::TestCalculateSentimentScore::test_calculate_sentiment_score_invalid_method": true, "tests/test_sentiment_analyzer_analyzer.py::TestCalculateSentimentScore::test_calculate_sentiment_score_empty_texts": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_success": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_error": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_vader_no_headlines": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_none_method": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_invalid_method": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_cache_hit": true, "tests/test_sentiment_analyzer_analyzer.py::TestGetSentimentContext::test_get_sentiment_context_cache_miss": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_invalid_items_value": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_invalid_score_values": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_vader_exception": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_transformer_exception": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_vader_bearish": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestTransformerSentimentScoreEdgeCases::test_calculate_sentiment_score_transformer_bearish": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestTransformerSentimentScoreEdgeCases::test_calculate_sentiment_score_transformer_mixed_labels": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_alphavantage_bearish": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_alphavantage_somewhat_bearish": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_alphavantage_neutral": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_alphavantage_no_news": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_vader_stub": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_transformer_stub": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_none_method": true, "tests/test_sentiment_analyzer_analyzer_90_coverage.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_invalid_method": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_throttling": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_api_limit_reached": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_error_message": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_success": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchNewsHeadlines::test_fetch_news_headlines_stub": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_empty_texts": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bullish": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bearish": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_neutral": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bullish": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bearish": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScore::test_calculate_sentiment_score_unsupported_method": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_cache_hit": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_success": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_error": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_alphavantage_no_news": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_vader_no_headlines": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_method_none": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContext::test_get_sentiment_context_unsupported_method": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_success": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_note": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_http_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_json_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_connection_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_timeout_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_sentiment_analyzer_combined.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_api_error": true, "tests/test_sentiment_analyzer_combined.py::TestFetchNewsHeadlinesAdditional::test_fetch_news_headlines_basic": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_empty_texts": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_exception": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_valid_texts": true, "tests/test_sentiment_analyzer_combined.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_with_textblob": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContextAdditional::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContextAdditional::test_get_sentiment_context_method_none": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContextAdditional::test_get_sentiment_context_invalid_method": true, "tests/test_sentiment_analyzer_combined.py::TestGetSentimentContextAdditional::test_get_sentiment_context_with_vader": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_throttling": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_api_limit_reached": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_error_message": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_empty_feed": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_success": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_comprehensive.py::TestFetchAlphavantageFunction::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_empty_texts": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bullish": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_bearish": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader_neutral": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bullish": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer_bearish": true, "tests/test_sentiment_analyzer_comprehensive.py::TestCalculateSentimentScore::test_calculate_sentiment_score_unsupported_method": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_comprehensive.py::TestGetSentimentContext::test_get_sentiment_context_cache_hit": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_success": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_error": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_note": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_http_error": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_json_error": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_no_api_key": true, "tests/test_sentiment_analyzer_direct.py::TestFetchAlphavantage::test_fetch_alphavantage_sentiment_invalid_symbol": true, "tests/test_sentiment_analyzer_direct.py::TestCalculateSentimentScore::test_calculate_sentiment_score_vader": true, "tests/test_sentiment_analyzer_direct.py::TestCalculateSentimentScore::test_calculate_sentiment_score_transformer": true, "tests/test_sentiment_analyzer_direct.py::TestCalculateSentimentScore::test_calculate_sentiment_score_empty": true, "tests/test_sentiment_analyzer_direct.py::TestGetSentimentContext::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_direct.py::TestGetSentimentContext::test_get_sentiment_context_method_none": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_json_decode_error": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_timeout": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_request_exception": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_throttling": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchAlphavantageAdditional::test_fetch_alphavantage_sentiment_invalid_scores": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_vader_exception": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_transformer_exception": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_vader_bearish": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_vader_neutral": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_vader_bullish": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_transformer_positive": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_transformer_negative": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_transformer_mixed": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_empty_texts": true, "tests/test_sentiment_analyzer_final_coverage.py::TestCalculateSentimentScoreAdditional::test_calculate_sentiment_score_unsupported_method": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_stub": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_with_data": true, "tests/test_sentiment_analyzer_final_coverage.py::TestFetchNewsHeadlines::test_fetch_news_headlines_logs_warning": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_bearish": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_somewhat_bearish": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_neutral": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_error_no_news": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_alphavantage_error_other": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_vader_method": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_method_none": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_disabled": true, "tests/test_sentiment_analyzer_final_coverage.py::TestGetSentimentContextAdditional::test_get_sentiment_context_cache_hit": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestConfigurationLoading::test_config_loading_exception": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_with_invalid_symbol_length": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_with_throttling": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_with_invalid_items_value": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestFetchAlphavantageEdgeCases::test_fetch_alphavantage_sentiment_with_invalid_ticker_sentiment_scores": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_with_empty_texts": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_vader_with_exception": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_transformer_with_exception": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestCalculateSentimentScoreEdgeCases::test_calculate_sentiment_score_transformer_with_neutral_label": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_with_sentiment_disabled": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_with_cache": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_with_method_none": true, "tests/test_sentiment_analyzer_final_coverage_90.py::TestGetSentimentContextEdgeCases::test_get_sentiment_context_with_unsupported_method": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_none_dataframe": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_empty_dataframe": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_missing_close_column": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_insufficient_data": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_log_method": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_percentage_method": true, "tests/test_volatility_forecaster_comprehensive.py::TestCalculateReturns::test_calculate_returns_with_nan_values": true, "tests/test_volatility_forecaster_comprehensive.py::TestFitGarchModel::test_fit_garch_model_none_series": true, "tests/test_volatility_forecaster_comprehensive.py::TestFitGarchModel::test_fit_garch_model_insufficient_data": true, "tests/test_volatility_forecaster_comprehensive.py::TestFitGarchModel::test_fit_garch_model_success": true, "tests/test_volatility_forecaster_comprehensive.py::TestFitGarchModel::test_fit_garch_model_custom_parameters": true, "tests/test_volatility_forecaster_comprehensive.py::TestFitGarchModel::test_fit_garch_model_fit_error": true, "tests/test_volatility_forecaster_comprehensive.py::TestForecastVolatility::test_forecast_volatility_none_result": true, "tests/test_volatility_forecaster_comprehensive.py::TestForecastVolatility::test_forecast_volatility_success": true, "tests/test_volatility_forecaster_comprehensive.py::TestForecastVolatility::test_forecast_volatility_custom_horizon": true, "tests/test_volatility_forecaster_comprehensive.py::TestForecastVolatility::test_forecast_volatility_forecast_error": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_none_dataframe": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_insufficient_data": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_returns_calculation_failed": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_garch_fitting_failed": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_forecast_failed": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_negative_forecast": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_low_volatility": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_medium_volatility": true, "tests/test_volatility_forecaster_comprehensive.py::TestGetVolatilityContext::test_get_volatility_context_high_volatility": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestCalculateReturnsEdgeCases::test_calculate_returns_empty_dataframe": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestCalculateReturnsEdgeCases::test_calculate_returns_missing_close_column": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestCalculateReturnsEdgeCases::test_calculate_returns_single_row": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestCalculateReturnsEdgeCases::test_calculate_returns_invalid_method": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestConfigExceptionHandlingIndividual::test_config_exception_handling_individual": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestConfigExceptionHandling::test_config_exception_handling": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestFitGarchModelEdgeCases::test_fit_garch_model_empty_returns": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestFitGarchModelEdgeCases::test_fit_garch_model_insufficient_data": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestFitGarchModelEdgeCases::test_fit_garch_model_constant_returns": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestForecastVolatilityEdgeCases::test_forecast_volatility_none_result": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestForecastVolatilityEdgeCases::test_forecast_volatility_invalid_horizon": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestForecastVolatilityEdgeCases::test_forecast_volatility_negative_variance": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestFitGarchModelExceptionHandling::test_fit_garch_model_exception_handling": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextReturnCalculationFailed::test_get_volatility_context_return_calculation_failed": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextEdgeCases::test_get_volatility_context_with_patched_constants": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextEdgeCases::test_get_volatility_context_empty_dataframe": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextEdgeCases::test_get_volatility_context_negative_forecast": true, "tests/test_volatility_forecaster_garch_model_90_plus.py::TestGetVolatilityContextEdgeCases::test_get_volatility_context_zero_forecast": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_calculate_correlation_trend_success": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_detect_correlation_changes_exception": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_detect_correlation_changes_success": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap_exception": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap_success": true, "tests/test_log_manager_isolated.py::TestLogManager::test_init": true, "tests/test_log_manager_isolated.py::TestLogManager::test_setup_logging": true, "tests/test_log_manager_isolated.py::TestLogManager::test_setup_logging_exception": true, "tests/test_log_manager_isolated.py::TestLogManager::test_initialize_perf_log_header_new_file": true, "tests/test_log_manager_isolated.py::TestLogManager::test_initialize_perf_log_header_existing_file": true, "tests/test_log_manager_isolated.py::TestLogManager::test_create_logger_adapter": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_log_file_success": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_log_file_failure": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_log_file_rclone_not_available": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_log_file_file_not_found": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_log_file_exception": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_logs_to_cloud_success": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_logs_to_cloud_partial_success": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_logs_to_cloud_no_remote_config": true, "tests/test_log_manager_isolated.py::TestLogManager::test_upload_logs_to_cloud_no_files": true, "tests/test_log_manager_isolated.py::TestLogManager::test_cleanup_logs": true, "tests/test_log_manager_isolated.py::TestLogManager::test_cleanup_logs_exception": true, "tests/test_log_manager_isolated.py::TestLogManager::test_close_handlers": true, "tests/test_log_manager_isolated.py::TestLogManager::test_close_handlers_exception": true, "tests/test_log_manager_isolated.py::TestModuleFunctions::test_setup_logging": true, "tests/test_log_manager_isolated.py::TestModuleFunctions::test_upload_log_file_function": true, "tests/test_log_manager_isolated.py::TestModuleFunctions::test_upload_logs_to_cloud_function": true, "tests/test_log_manager_isolated.py::TestModuleFunctions::test_create_logger_adapter_function": true, "tests/test_log_manager_isolated.py::TestModuleFunctions::test_cleanup_logs_function": true, "tests/test_market_depth_visualizer/test_client.py::test_market_depth_client_init": true, "tests/test_market_depth_visualizer/test_client.py::test_add_snapshot": true, "tests/test_market_depth_visualizer/test_client.py::test_add_trade": true, "tests/test_market_depth_visualizer/test_client.py::test_get_latest_snapshot": true, "tests/test_market_depth_visualizer/test_client.py::test_get_latest_snapshot_no_data": true, "tests/test_market_depth_visualizer/test_client.py::test_get_snapshots": true, "tests/test_market_depth_visualizer/test_client.py::test_get_snapshots_no_data": true, "tests/test_market_depth_visualizer/test_client.py::test_get_trades": true, "tests/test_market_depth_visualizer/test_client.py::test_get_trades_no_data": true, "tests/test_market_depth_visualizer/test_client.py::test_create_depth_chart": true, "tests/test_market_depth_visualizer/test_client.py::test_create_heatmap": true, "tests/test_market_depth_visualizer/test_client.py::test_create_time_and_sales": true, "tests/test_market_depth_visualizer/test_client.py::test_create_dashboard": true, "tests/test_market_depth_visualizer/test_client.py::test_get_visualization": true, "tests/test_market_depth_visualizer/test_client.py::test_get_visualization_not_found": true, "tests/test_market_depth_visualizer/test_client.py::test_get_dashboard": true, "tests/test_market_depth_visualizer/test_client.py::test_get_dashboard_not_found": true, "tests/test_market_depth_visualizer/test_client.py::test_clear_data": true, "tests/test_market_depth_visualizer/test_client.py::test_get_market_depth_client": true, "tests/test_market_depth_visualizer/test_models.py::test_trade_entry": true, "tests/test_market_depth_visualizer/test_models.py::test_market_depth_snapshot": true, "tests/test_market_depth_visualizer/test_models.py::test_market_depth_visualization": true, "tests/test_market_depth_visualizer/test_models.py::test_market_depth_dashboard": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_news_article_with_all_fields": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_news_article_age_hours_zero": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_news_article_age_hours_future": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_sentiment_analysis_with_error": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_entity_with_all_fields": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_news_summary_with_all_fields": true, "tests/test_multilingual_news_90_coverage.py::TestNewsModelsExtended::test_news_context_with_all_fields": true, "tests/test_performance_analyzer_utils_final_simple.py::TestEquityCurve::test_equity_curve_with_empty_dataframe": true, "tests/test_performance_analyzer_utils_final_simple.py::TestEquityCurve::test_equity_curve_with_duplicate_timestamps": true, "tests/test_performance_analyzer_utils_final_simple.py::TestEquityCurve::test_equity_curve_with_non_utc_timestamps": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_timezone_conversion": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_empty_data": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_invalid_columns": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_localization_error": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_nan_values": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_download_error": true, "tests/test_performance_analyzer_utils_final_simple.py::TestGetBenchmarkData::test_get_benchmark_data_with_invalid_dates": true, "tests/test_phase3_integration.py::TestPhase3Integration::test_correlation_matrix_integration": true, "tests/test_phase3_integration.py::TestPhase3Integration::test_market_depth_integration": true, "tests/test_phase3_integration.py::TestPhase3Integration::test_metrics_dashboard_integration": true, "tests/test_phase3_integration.py::TestPhase3Integration::test_order_flow_integration": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_ranging": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_insufficient_data": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_empty_dataframe": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_none_dataframe": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_missing_close_column": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_with_nan_values": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_with_custom_parameters": true, "tests/test_trend_analyzer_90_coverage.py::TestGetTrendStatus::test_get_trend_status_with_exception": true, "tests/test_trend_analyzer_90_coverage.py::TestDirectFunctionCalls::test_get_trend_status_with_all_nan_values": true, "tests/test_trend_analyzer_90_coverage.py::TestDirectFunctionCalls::test_get_trend_status_with_invalid_ma_type": true, "tests/test_trend_analyzer_90_coverage.py::TestDirectFunctionCalls::test_get_trend_status_with_ma_calculation_error": true, "tests/test_trend_analyzer_90_coverage.py::TestEdgeCases::test_validation_code_coverage": true, "tests/test_trend_analyzer_90_coverage.py::TestEdgeCases::test_get_trend_status_with_single_value": true, "tests/test_trend_analyzer_90_coverage.py::TestEdgeCases::test_get_trend_status_with_exactly_long_ma_period_values": true, "tests/test_trend_analyzer_90_coverage.py::TestGetMtaContext::test_get_mta_context_with_valid_dataframes": true, "tests/test_trend_analyzer_90_coverage.py::TestGetMtaContext::test_get_mta_context_with_none_dataframes": true, "tests/test_trend_analyzer_90_coverage.py::TestGetMtaContext::test_get_mta_context_with_mixed_dataframes": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap_disabled": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_network_disabled": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_network_exception": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_network_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_network_no_networkx": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_network_success": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_heatmap_disabled": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_heatmap_exception": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_heatmap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_heatmap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_disabled": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_exception": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_no_networkx": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_success_full": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_with_empty_pairs": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_network_with_exception_in_graph_creation": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_correlation_alert_str_representation": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_correlation_visualization_str_representation": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_clustermap_success": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_exception": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_success": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_exception": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_success": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_exception": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_no_heatmap": true, "tests/test_correlation_matrix/test_visualizer_part2.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_success": true, "tests/test_correlation_matrix/test_visualizer_part2_mock.py::TestCorrelationVisualizerPart2Mock::test_create_correlation_visualization_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_mock.py::TestCorrelationVisualizerPart2Mock::test_create_correlation_visualization_no_heatmap": true, "tests/test_correlation_matrix/test_visualizer_part2_mock.py::TestCorrelationVisualizerPart2Mock::test_create_correlation_visualization_success": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_correlation_alert_dict": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_correlation_visualization_dict": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_create_correlation_alert_success": true, "tests/test_correlation_matrix/test_visualizer_part2_simple.py::TestCorrelationVisualizerPart2Simple::test_create_correlation_visualization_success": true, "tests/test_correlation_matrix/test_visualizer_simple.py::TestCorrelationVisualizer::test_create_correlation_heatmap_success_full": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsModels::test_news_article_age_hours_without_timezone": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsAnalyzer::test_analyze_sentiment_with_transformers": true, "tests/test_multilingual_news_models_90_coverage.py::TestNewsModels::test_news_article_age_hours_without_timezone": true, "tests/test_correlation_matrix.py": true, "tests/test_event_bus_producer.py::TestKafkaProducer::test_initialization": true, "tests/test_event_bus_producer.py::TestKafkaProducer::test_produce": true, "tests/test_event_bus_producer.py::TestKafkaProducer::test_flush": true, "tests/test_event_bus_producer.py::TestKafkaProducer::test_close": true, "tests/test_event_bus_producer.py::TestKafkaProducer::test_context_manager": true, "tests/test_event_bus_producer.py::TestMarketDataProducer::test_publish_ohlcv": true, "tests/test_event_bus_producer.py::TestMarketDataProducer::test_publish_tick": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_initialization": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_subscribe": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_start_stop": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_context_manager": true, "tests/test_event_bus_consumer.py::TestMarketDataConsumer::test_initialization": true, "tests/test_order_flow_event_bus_integration.py::TestOrderFlowEventBusIntegration::test_publish_order_flow_analysis": true, "tests/test_event_bus_producer_extended.py::TestOrderProducer::test_publish_order_with_error": true, "tests/test_event_bus_producer_extended.py::TestTradeProducer::test_publish_trade_with_error": true, "tests/test_event_bus_producer_extended.py::TestAnalysisProducer::test_publish_analysis_with_error": true, "tests/test_event_bus_producer_extended.py::TestKafkaProducerExtended::test_initialization_with_ssl": true, "tests/test_event_bus_producer_extended.py::TestKafkaProducerExtended::test_initialization_with_sasl": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_from_file": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_initialization_with_ssl": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_initialization_with_sasl": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_consume_loop": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_consume_loop_with_exception": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_parse_message": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_parse_message_with_error": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_initialization": true, "tests/test_metrics_dashboard_event_bus_integration.py": true, "tests/test_event_bus_schemas.py::TestOHLCVData::test_ohlcv_data_serialization": true, "tests/test_event_bus_schemas.py::TestTradeEvent::test_trade_event_initialization": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_initialization": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_produce": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_produce_with_error": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_flush": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_close": true, "tests/test_event_bus_producer_comprehensive.py::TestKafkaProducerComprehensive::test_context_manager": true, "tests/test_event_bus_producer_comprehensive.py::TestMarketDataProducerComprehensive::test_publish_ohlcv": true, "tests/test_event_bus_producer_comprehensive.py::TestMarketDataProducerComprehensive::test_publish_tick": true, "tests/test_event_bus_producer_comprehensive.py::TestMarketDataProducerComprehensive::test_publish_order_book": true, "tests/test_event_bus_producer_comprehensive.py::TestOrderProducerComprehensive::test_publish_order": true, "tests/test_event_bus_producer_comprehensive.py::TestOrderProducerComprehensive::test_publish_order_with_error": true, "tests/test_event_bus_producer_comprehensive.py::TestTradeProducerComprehensive::test_publish_trade": true, "tests/test_event_bus_producer_comprehensive.py::TestTradeProducerComprehensive::test_publish_trade_with_error": true, "tests/test_event_bus_producer_comprehensive.py::TestAnalysisProducerComprehensive::test_publish_analysis": true, "tests/test_event_bus_producer_comprehensive.py::TestAnalysisProducerComprehensive::test_publish_analysis_with_error": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_initialization": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_start_and_stop": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_context_manager": true, "tests/test_enhanced_metrics_dashboard.py": true, "tests/event_bus/test_config.py::TestKafkaConfig::test_kafka_config_defaults": true, "tests/event_bus/test_config.py::TestKafkaConfig::test_kafka_config_custom_values": true, "tests/event_bus/test_config.py::TestKafkaConfig::test_kafka_config_validation_security_protocol": true, "tests/event_bus/test_config.py::TestKafkaConfig::test_kafka_config_validation_sasl_mechanism": true, "tests/event_bus/test_config.py::TestKafkaConfig::test_kafka_config_validation_sasl_credentials": true, "tests/event_bus/test_config.py::TestTopicConfig::test_topic_config_defaults": true, "tests/event_bus/test_config.py::TestTopicConfig::test_topic_config_custom_values": true, "tests/event_bus/test_config.py::TestTopicConfig::test_get_topic_by_event_type": true, "tests/event_bus/test_config.py::TestEventBusConfig::test_event_bus_config_defaults": true, "tests/event_bus/test_config.py::TestEventBusConfig::test_event_bus_config_custom_values": true, "tests/event_bus/test_config.py::TestEventBusConfig::test_event_bus_config_validation_provider": true, "tests/event_bus/test_config.py::TestGetEventBusConfig::test_get_event_bus_config_from_env": true, "tests/event_bus/test_config.py::TestGetEventBusConfig::test_get_event_bus_config_singleton": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_register_handler": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_register_handler_with_string": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_unregister_handler": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_dispatch_event": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_dispatch_event_with_error": true, "tests/event_bus/test_consumer.py::TestBaseConsumer::test_base_consumer_context_manager": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_init": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_init_with_topics": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_subscribe": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_start": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_stop": true, "tests/event_bus/test_consumer.py::TestKafkaConsumer::test_kafka_consumer_create_event_from_data": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_market_data_consumer_init": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_register_ohlcv_handler": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_register_tick_handler": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_filter_and_dispatch_market_data_ohlcv": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_filter_and_dispatch_market_data_filtered_by_symbol": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_filter_and_dispatch_market_data_filtered_by_timeframe": true, "tests/event_bus/test_consumer.py::TestMarketDataConsumer::test_filter_and_dispatch_market_data_filtered_by_data_type": true, "tests/event_bus/test_consumer.py::TestOrderConsumer::test_order_consumer_init": true, "tests/event_bus/test_consumer.py::TestOrderConsumer::test_register_order_handler": true, "tests/event_bus/test_consumer.py::TestOrderConsumer::test_filter_and_dispatch_order": true, "tests/event_bus/test_consumer.py::TestOrderConsumer::test_filter_and_dispatch_order_filtered_by_symbol": true, "tests/event_bus/test_consumer.py::TestOrderConsumer::test_filter_and_dispatch_order_filtered_by_order_type": true, "tests/event_bus/test_event_schemas.py::TestBaseEvent::test_base_event_creation": true, "tests/event_bus/test_event_schemas.py::TestBaseEvent::test_base_event_serialization": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_with_tick": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_with_ohlcv": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_with_ohlcv_list": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_validation_error": true, "tests/event_bus/test_event_schemas.py::TestOrderEvent::test_order_event_creation": true, "tests/event_bus/test_event_schemas.py::TestTradeEvent::test_trade_event_creation": true, "tests/event_bus/test_event_schemas.py::TestTradeEvent::test_closed_trade_event_creation": true, "tests/event_bus/test_event_schemas.py::TestAnalysisEvent::test_analysis_event_creation": true, "tests/event_bus/test_producer.py::TestBaseProducer::test_base_producer_context_manager": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_init": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_init_with_ssl": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_init_with_sasl": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_produce": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_produce_with_default_topic": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_flush": true, "tests/event_bus/test_producer.py::TestKafkaProducer::test_kafka_producer_close": true, "tests/event_bus/test_producer.py::TestMarketDataProducer::test_publish_ohlcv": true, "tests/event_bus/test_producer.py::TestMarketDataProducer::test_publish_tick": true, "tests/event_bus/test_producer.py::TestOrderProducer::test_publish_order": true, "tests/event_bus/test_producer.py::TestTradeProducer::test_publish_trade": true, "tests/event_bus/test_producer.py::TestAnalysisProducer::test_publish_analysis": true, "tests/test_aaa_sample.py::test_non_aaa_pattern": true, "tests/test_calculate_indicators.py::test_calculate_indicators_empty_df": true, "tests/test_combined_coverage.py::test_calculate_returns_invalid_data": true, "tests/test_combined_coverage.py::test_calculate_returns_insufficient_data": true, "tests/test_combined_coverage.py::test_fit_garch_model_insufficient_data": true, "tests/test_combined_coverage.py::test_forecast_volatility_valid_result": true, "tests/test_combined_coverage.py::test_forecast_volatility_invalid_result": true, "tests/test_combined_coverage.py::test_get_volatility_context_invalid_data": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_calculate_correlation_matrix_exception": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_calculate_correlation_matrix_success": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_fetch_price_data_empty_data": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_fetch_price_data_exception": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_fetch_price_data_success": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_get_correlation_strength": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_get_periods_from_window": true, "tests/test_correlation_matrix/test_calculator.py::TestCorrelationCalculator::test_get_timeframe_from_window": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_calculate_p_values_exception": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_calculate_p_values_no_scipy": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_calculate_p_values_success": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_create_correlation_matrix_exception": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_create_correlation_matrix_no_correlation_matrix": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_create_correlation_matrix_no_price_data": true, "tests/test_correlation_matrix/test_calculator_part2.py::TestCorrelationCalculatorPart2::test_create_correlation_matrix_success": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_calculate_correlation_trend_exception": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_calculate_correlation_trend_no_data": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_detect_correlation_changes_different_symbols": true, "tests/test_correlation_matrix/test_calculator_part3.py::TestCorrelationCalculatorPart3::test_detect_correlation_changes_different_time_window": true, "tests/test_correlation_matrix/test_client.py::TestCorrelationClient::test_get_correlation_alerts": true, "tests/test_correlation_matrix/test_client.py::TestCorrelationClient::test_get_correlation_matrix": true, "tests/test_correlation_matrix/test_client.py::TestCorrelationClient::test_get_correlation_trend": true, "tests/test_correlation_matrix/test_client.py::TestCorrelationClient::test_get_correlation_visualization": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_alert_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_matrix_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_matrix_to_dataframe": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_method_enum": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_pair_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_settings_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_strength_enum": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_trend_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_correlation_visualization_model": true, "tests/test_correlation_matrix/test_models.py::TestCorrelationModels::test_time_window_enum": true, "tests/test_correlation_matrix/test_models_part2.py::TestCorrelationModelsPart2::test_correlation_alert_model": true, "tests/test_correlation_matrix/test_models_part2.py::TestCorrelationModelsPart2::test_correlation_visualization_model": true, "tests/test_correlation_matrix/test_visualizer.py::TestCorrelationVisualizer::test_create_correlation_heatmap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_clustermap_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_clustermap_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_clustermap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_clustermap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_trend_chart_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_trend_chart_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_trend_chart_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_visualization_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_visualization_no_heatmap": true, "tests/test_correlation_matrix/test_visualizer_part2_functions.py::TestVisualizerPart2Functions::test_create_correlation_visualization_success": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_clustermap_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_clustermap_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_clustermap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_clustermap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_trend_chart_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_trend_chart_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_trend_chart_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_trend_chart_success": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_visualization_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_visualization_no_heatmap": true, "tests/test_correlation_matrix/test_visualizer_part2_mock_new.py::TestVisualizerPart2WithMocks::test_create_correlation_visualization_success": true, "tests/test_cot_reports/test_analyzer.py::test_analyze_positioning": true, "tests/test_cot_reports/test_analyzer.py::test_detect_extremes": true, "tests/test_cot_reports/test_analyzer.py::test_get_cot_context": true, "tests/test_cot_reports/test_analyzer.py::test_get_cot_context_invalid_symbol": true, "tests/test_cot_reports/test_analyzer.py::test_get_cot_context_analysis_error": true, "tests/test_cot_reports/test_client.py::test_fetch_latest_cot_report": true, "tests/test_cot_reports/test_client.py::test_fetch_historical_cot_data": true, "tests/test_cot_reports/test_client.py::test_parse_cot_report_for_currency": true, "tests/test_cot_reports/test_client.py::test_get_latest_cot_report_for_currency": true, "tests/test_cot_reports/test_client.py::test_get_historical_cot_data_for_currency": true, "tests/test_cvd/test_calculator.py::test_calculate_cvd": true, "tests/test_cvd/test_calculator.py::test_calculate_cvd_empty_df": true, "tests/test_cvd/test_calculator.py::test_calculate_cvd_missing_columns": true, "tests/test_cvd/test_calculator.py::test_detect_divergence": true, "tests/test_cvd/test_calculator.py::test_get_cvd_context": true, "tests/test_cvd/test_calculator.py::test_get_cvd_context_no_data": true, "tests/test_cvd/test_models.py::test_cvd_result_to_dataframe": true, "tests/test_cvd/test_models.py::test_cvd_divergence_properties": true, "tests/test_global_pmi/test_analyzer.py::test_analyze_pmi_trends": true, "tests/test_global_pmi/test_analyzer.py::test_analyze_pmi_trends_no_data": true, "tests/test_global_pmi/test_analyzer.py::test_detect_pmi_divergences": true, "tests/test_global_pmi/test_analyzer.py::test_get_pmi_context": true, "tests/test_global_pmi/test_analyzer.py::test_get_pmi_context_invalid_symbol": true, "tests/test_global_pmi/test_client.py::test_fetch_latest_pmi_data": true, "tests/test_global_pmi/test_client.py::test_fetch_latest_pmi_data_with_cache": true, "tests/test_global_pmi/test_client.py::test_fetch_latest_pmi_data_invalid_country": true, "tests/test_global_pmi/test_client.py::test_fetch_historical_pmi_data": true, "tests/test_global_pmi/test_client.py::test_get_mock_pmi_data": true, "tests/test_global_pmi/test_client.py::test_get_mock_pmi_data_invalid_country": true, "tests/test_global_pmi/test_client.py::test_get_mock_historical_pmi_data": true, "tests/test_global_pmi/test_client.py::test_ensure_cache_dir": true, "tests/test_global_pmi/test_client.py::test_get_cached_pmi_data": true, "tests/test_global_pmi/test_client.py::test_get_cached_pmi_data_no_cache": true, "tests/test_global_pmi/test_client.py::test_cache_pmi_data": true, "tests/test_global_pmi/test_models.py::test_pmi_data_creation": true, "tests/test_global_pmi/test_models.py::test_pmi_data_post_init": true, "tests/test_multilingual_news.py::test_fetch_news[forex-en-0]": true, "tests/test_multilingual_news.py::test_fetch_news[inflation-en-0]": true, "tests/test_multilingual_news.py::test_fetch_news[euro-fr-0]": true, "tests/test_multilingual_news.py::test_fetch_news_by_currency": true, "tests/test_multilingual_news.py::test_analyze_sentiment": true, "tests/test_multilingual_news.py::test_get_news_context": true, "tests/test_pattern_recognizer_uncovered.py::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_uncovered.py::test_calculate_pattern_confidence_key_error": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_ranging": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_empty": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_insufficient": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_nan": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_missing_close": true, "tests/test_trend_analyzer_direct.py::test_get_trend_status_error": true, "tests/test_trend_analyzer_direct.py::test_get_mta_context_both": true, "tests/test_trend_analyzer_direct.py::test_get_mta_context_h1_only": true, "tests/test_trend_analyzer_direct.py::test_get_mta_context_h4_only": true, "tests/test_trend_analyzer_direct.py::test_get_mta_context_neither": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_none_dataframe": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_empty_dataframe": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_missing_close_column": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_insufficient_data": true, "tests/test_trend_analyzer_minimal.py::test_get_mta_context_both_none": true, "tests/test_trend_analyzer_minimal.py::test_get_mta_context_calls_get_trend_status": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_ranging": true, "tests/test_trend_analyzer_minimal.py::test_get_trend_status_with_sma": true, "tests/test_volatility_indices/test_analyzer.py::test_analyze_vix_trends": true, "tests/test_volatility_indices/test_analyzer.py::test_analyze_move_trends": true, "tests/test_volatility_indices/test_analyzer.py::test_detect_volatility_regime": true, "tests/test_volatility_indices/test_analyzer.py::test_get_volatility_context": true, "tests/test_volatility_indices/test_analyzer.py::test_get_volatility_context_invalid_symbol": true, "tests/test_volatility_indices/test_client.py::test_fetch_latest_vix_data": true, "tests/test_volatility_indices/test_client.py::test_fetch_latest_move_data": true, "tests/test_volatility_indices/test_client.py::test_fetch_latest_vix_data_with_cache": true, "tests/test_volatility_indices/test_client.py::test_fetch_historical_vix_data": true, "tests/test_volatility_indices/test_client.py::test_fetch_historical_move_data": true, "tests/test_volatility_indices/test_client.py::test_get_mock_vix_data": true, "tests/test_volatility_indices/test_client.py::test_get_mock_move_data": true, "tests/test_volatility_indices/test_client.py::test_get_mock_historical_vix_data": true, "tests/test_volatility_indices/test_client.py::test_get_mock_historical_move_data": true, "tests/test_volatility_indices/test_client.py::test_ensure_cache_dir": true, "tests/test_volatility_indices/test_client.py::test_get_cached_volatility_data": true, "tests/test_volatility_indices/test_client.py::test_get_cached_volatility_data_no_cache": true, "tests/test_volatility_indices/test_client.py::test_cache_volatility_data": true, "tests/test_volatility_indices/test_models.py::test_volatility_data_post_init": true, "tests/test_volatility_indices/test_models.py::test_volatility_regime_properties": true, "tests/test_volume_profile/test_calculator.py::test_calculate_volume_profile": true, "tests/test_volume_profile/test_calculator.py::test_calculate_volume_profile_empty_df": true, "tests/test_volume_profile/test_calculator.py::test_calculate_volume_profile_missing_columns": true, "tests/test_volume_profile/test_calculator.py::test_find_poc": true, "tests/test_volume_profile/test_calculator.py::test_find_value_areas": true, "tests/test_volume_profile/test_calculator.py::test_get_volume_zones": true, "tests/test_volume_profile/test_calculator.py::test_get_volume_zones_no_data": true, "tests/test_volume_profile/test_models.py::test_volume_zone_properties": true, "tests/test_vwap/test_calculator.py::test_calculate_vwap": true, "tests/test_vwap/test_calculator.py::test_calculate_vwap_empty_df": true, "tests/test_vwap/test_calculator.py::test_calculate_vwap_missing_columns": true, "tests/test_vwap/test_calculator.py::test_calculate_anchored_vwap": true, "tests/test_vwap/test_calculator.py::test_calculate_anchored_vwap_string_time": true, "tests/test_vwap/test_calculator.py::test_calculate_anchored_vwap_future_time": true, "tests/test_vwap/test_calculator.py::test_detect_vwap_crossovers": true, "tests/test_vwap/test_calculator.py::test_get_vwap_context": true, "tests/test_vwap/test_calculator.py::test_get_vwap_context_with_anchor": true, "tests/test_vwap/test_calculator.py::test_get_vwap_context_no_data": true, "tests/test_vwap/test_client.py::test_vwap_client_init": true, "tests/test_vwap/test_client.py::test_get_vwap": true, "tests/test_vwap/test_client.py::test_get_anchored_vwap": true, "tests/test_vwap/test_client.py::test_get_vwap_crossovers": true, "tests/test_vwap/test_client.py::test_get_vwap_context": true, "tests/test_vwap/test_client.py::test_plot_vwap": true, "tests/test_vwap/test_client.py::test_clear_cache": true, "tests/test_vwap/test_client.py::test_get_vwap_client": true, "tests/test_vwap/test_models.py::test_vwap_result_to_dataframe": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_initialize_tracer": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_initialize_tracer_disabled": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_get_tracer": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_start_span": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_add_span_event": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_set_span_attribute": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_record_exception": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_tracing_middleware": true, "tests/metrics/test_otel_tracing.py::TestOpenTelemetryTracing::test_tracing_middleware_exception": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_get_metrics_registry": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_initialize_metrics": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_initialize_metrics_disabled": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_increment_counter": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_decrement_counter": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_set_gauge": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_observe_histogram": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_observe_summary": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_start_metrics_server": true, "tests/metrics/test_prometheus_metrics.py::TestPrometheusMetrics::test_stop_metrics_server": true, "tests/test_aaa_pattern_example.py::TestRunBotOnceAAA::test_run_bot_once_success": true, "tests/test_aaa_pattern_example.py::TestRunBotOnceAAA::test_run_bot_once_historical_data_error": true, "tests/test_aaa_pattern_example.py::TestLoadPerformanceDataAAA::test_load_performance_data_success": true, "tests/test_aaa_pattern_example.py::TestLoadPerformanceDataAAA::test_load_performance_data_file_not_found": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_mt5": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_gemini": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_knowledge_base": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_knowledge_base_not_available": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_knowledge_base_error": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_analysis_modules": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_all_components": true, "tests/test_bot_orchestrator_90_coverage_part1.py::TestInitialization::test_initialize_all_components_mt5_failure": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_mt5_connection": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_mt5_connection_not_connected": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_mt5_connection_error": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_market_hours": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_market_hours_closed": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_market_hours_error": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_system_status": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_system_status_mt5_not_connected": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_check_system_status_market_closed": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_handle_connection_errors": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_handle_connection_errors_failed": true, "tests/test_bot_orchestrator_90_coverage_part2.py::TestSystemMonitoring::test_handle_connection_errors_exception": true, "tests/test_bot_orchestrator_90_coverage_part3.py::TestSignalGeneration::test_run_analysis_modules": true, "tests/test_bot_orchestrator_90_coverage_part3.py::TestSignalGeneration::test_get_knowledge_base_context": true, "tests/test_bot_orchestrator_90_coverage_part3.py::TestSignalGeneration::test_prepare_analysis_context": true, "tests/test_bot_orchestrator_90_coverage_part3.py::TestSignalGeneration::test_generate_signal": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_close_existing_positions": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_close_existing_positions_no_positions": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_close_existing_positions_close_error": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_close_existing_positions_get_error": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_calculate_position_size": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_calculate_position_size_error": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_place_trade": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_place_trade_error": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_execute_signal_buy": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_execute_signal_sell": true, "tests/test_bot_orchestrator_90_coverage_part4.py::TestTradeExecution::test_execute_signal_hold": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBotOnce::test_run_bot_once_success": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBotOnce::test_run_bot_once_historical_data_error": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBotOnce::test_run_bot_once_hold_signal": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBot::test_run_bot_success": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBot::test_run_bot_initialization_failure": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBot::test_run_bot_connection_error_recovery": true, "tests/test_bot_orchestrator_90_coverage_part5.py::TestRunBot::test_run_bot_connection_error_failed_recovery": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_no_seaborn": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_clustermap_success": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_disabled": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_no_matplotlib": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_trend_chart_success": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_exception": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_no_heatmap": true, "tests/test_correlation_matrix/test_visualizer_part2_direct.py::TestCorrelationVisualizerPart2::test_create_correlation_visualization_success": true, "tests/test_correlation_matrix_event_bus_integration.py::TestCorrelationMatrixEventBusIntegration::test_publish_correlation_matrix": true, "tests/test_correlation_matrix_event_bus_integration.py::TestCorrelationMatrixEventBusIntegration::test_publish_correlation_trend": true, "tests/test_correlation_matrix_event_bus_integration.py::TestCorrelationMatrixEventBusIntegration::test_publish_correlation_alert": true, "tests/test_integration_end_to_end.py::TestEndToEndWorkflow::test_mt5_initialization_and_data_retrieval": true, "tests/test_integration_end_to_end.py::TestEndToEndWorkflow::test_gemini_initialization_and_response": true, "tests/test_integration_end_to_end.py::TestEndToEndWorkflow::test_prompt_building": true, "tests/test_integration_end_to_end.py::TestEndToEndWorkflow::test_end_to_end_trade_workflow": true, "tests/test_market_depth_event_bus_integration.py::TestMarketDepthEventBusIntegration::test_publish_market_depth_snapshot": true, "tests/test_market_depth_event_bus_integration.py::TestMarketDepthEventBusIntegration::test_consume_market_data_event": true, "tests/test_market_depth_event_bus_integration.py::TestMarketDepthEventBusIntegration::test_publish_trade_entry": true, "tests/test_market_depth_event_bus_integration.py::TestMarketDepthEventBusIntegration::test_consume_tick_data": true, "tests/test_market_depth_visualizer.py::TestMarketDepthVisualizer::test_client_add_snapshot": true, "tests/test_market_depth_visualizer.py::TestMarketDepthVisualizer::test_client_add_trade": true, "tests/test_market_depth_visualizer.py::TestMarketDepthVisualizer::test_client_convert_order_book_to_snapshot": true, "tests/test_market_depth_visualizer.py::TestMarketDepthVisualizer::test_create_depth_chart": true, "tests/test_market_depth_visualizer.py::TestMarketDepthVisualizer::test_get_color_scheme": true, "tests/test_metrics_dashboard_models.py::TestMetricsDashboardModels::test_chart_type_enum": true, "tests/test_metrics_dashboard_models.py::TestMetricsDashboardModels::test_metric_category_enum": true, "tests/test_metrics_dashboard_models.py::TestMetricsDashboardModels::test_time_frame_enum": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_deploy_garch_model": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_deploy_hmm_model": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_register_garch_model_type": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_register_hmm_model_type": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_train_garch_model": true, "tests/test_ml_registry/test_integrations.py::TestMLRegistryIntegrations::test_train_hmm_model": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_end_run": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_get_latest_model": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_get_model_stage": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_get_model_version": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_initialize_mlflow": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_list_experiments": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_list_model_versions": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_list_models": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_list_runs": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_load_model": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_artifact": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_metric": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_metrics": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_model": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_param": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_log_params": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_register_model": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_search_runs": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_set_model_stage": true, "tests/test_ml_registry/test_mlflow_registry.py::TestMLflowRegistry::test_start_run": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_get_model_config": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_mlflow_config_from_dict": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_mlflow_config_to_dict": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_from_dict": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_from_file_invalid_format": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_from_file_json": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_from_file_not_found": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_from_file_yaml": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_init": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_to_dict": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_to_file_json": true, "tests/test_ml_registry/test_model_config.py::TestModelConfig::test_model_config_to_file_yaml": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_deploy_model": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_deploy_model_failure": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_get_deployed_models": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_get_deployment_status": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_get_deployment_status_not_deployed": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_is_model_deployed": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_is_model_not_deployed": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_undeploy_model": true, "tests/test_ml_registry/test_model_deployment.py::TestModelDeployment::test_undeploy_model_failure": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_compare_model_performance": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_classifier": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_garch": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_hmm": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_model": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_models": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_evaluate_regressor": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_generate_classifier_report": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_generate_evaluation_report": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_generate_garch_report": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_generate_hmm_report": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_generate_regressor_report": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_get_model_type_classifier": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_get_model_type_garch": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_get_model_type_hmm": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_get_model_type_regressor": true, "tests/test_ml_registry/test_model_evaluation.py::TestModelEvaluation::test_get_model_type_unknown": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_deploy_model": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_get_model": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_get_model_info": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_get_model_manager": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_get_registered_model_types": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_load_model": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_predict": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_register_model_type": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_train_model_classifier": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_train_model_garch": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_train_model_hmm": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_train_model_regressor": true, "tests/test_ml_registry/test_model_manager.py::TestModelManager::test_undeploy_model": true, "tests/test_multilingual_news.py::TestNewsModels::test_news_article_creation": true, "tests/test_multilingual_news.py::TestNewsModels::test_news_article_age_hours": true, "tests/test_multilingual_news.py::TestNewsModels::test_sentiment_analysis_creation": true, "tests/test_multilingual_news.py::TestNewsModels::test_entity_creation": true, "tests/test_multilingual_news.py::TestNewsModels::test_news_summary_creation": true, "tests/test_multilingual_news.py::TestNewsModels::test_news_context_creation": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_analyze_sentiment_empty_text": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_analyze_sentiment_no_transformers": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_analyze_sentiment_with_exception": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_analyze_sentiment_with_transformers": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_analyze_sentiment_with_translation": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_ensure_cache_dir": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_extract_entities_empty_text": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_extract_entities_no_transformers": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_extract_entities_with_exception": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_extract_entities_with_financial_entities": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_extract_entities_with_transformers": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_get_cache_key": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_get_news_context": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_get_news_context_single_currency": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_get_news_context_with_exception": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_summarize_news_empty_articles": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_summarize_news_no_transformers": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_summarize_news_with_exception": true, "tests/test_multilingual_news_analyzer_90_coverage.py::TestNewsAnalyzer::test_summarize_news_with_transformers": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_cache_news": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_ensure_cache_dir": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_forex_factory_news_http_error": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_forex_factory_news_no_api_key": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_forex_factory_news_rate_limit": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_forex_factory_news_success": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_forex_factory_news_timeout": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_api_error": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_by_currency": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_by_currency_unknown": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_by_topic": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_connection_error": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_no_api_key": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_success": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_fetch_news_timeout": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_get_cache_key": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_get_cached_news": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_get_cached_news_expired": true, "tests/test_multilingual_news_client_90_coverage.py::TestNewsClient::test_get_cached_news_invalid": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsModels::test_news_article_age_hours_with_timezone": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsModels::test_news_summary_time_range_empty": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_cache_expiration": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_cache_functionality": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_fetch_forex_factory_news_success": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_fetch_news_api_error": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_fetch_news_by_currency": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsClient::test_fetch_news_success": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsAnalyzer::test_analyze_sentiment_with_multiple_texts": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsAnalyzer::test_extract_entities_with_transformers": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsAnalyzer::test_get_news_context": true, "tests/test_multilingual_news_comprehensive.py::TestMultilingualNewsAnalyzer::test_summarize_news_with_transformers": true, "tests/test_order_book.py::TestOrderBookModels::test_order_book_entry_validation": true, "tests/test_order_book.py::TestOrderBookModels::test_order_book_properties": true, "tests/test_order_book.py::TestOrderBookCache::test_cache_order_book": true, "tests/test_order_book.py::TestOrderBookCache::test_clear_cache": true, "tests/test_order_book.py::TestOrderBookClient::test_get_order_book": true, "tests/test_order_book.py::TestOrderBookClient::test_get_cached_order_book": true, "tests/test_order_book.py::TestOrderBookClient::test_get_order_book_imbalance": true, "tests/test_order_book.py::TestOrderBookClient::test_get_best_bid_ask": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_analyze_order_book_imbalance": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_calculate_imbalance_level": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_detect_large_orders": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_order_flow_client_get_multiple_order_books": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_order_flow_client_get_order_book": true, "tests/test_order_flow_analyzer.py::TestOrderFlowAnalyzer::test_order_flow_client_get_order_book_async": true, "tests/test_order_flow_analyzer_mock.py::TestOrderFlowAnalyzerMock::test_analyze_order_book_imbalance": true, "tests/test_order_flow_analyzer_mock.py::TestOrderFlowAnalyzerMock::test_calculate_imbalance_level": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_imbalance_level_enum": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_large_order": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_order_flow_context": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_order_flow_imbalance": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_order_flow_signal": true, "tests/test_order_flow_models.py::TestOrderFlowModels::test_support_resistance_level": true, "tests/test_pattern_recognizer_additional.py::TestConfigurationLoading::test_config_weights_normalization_warning": true, "tests/test_pattern_recognizer_additional.py::TestConfigurationLoading::test_config_loading_exception_handling": true, "tests/test_pattern_recognizer_additional.py::TestDetectPatternsErrorHandling::test_detect_patterns_exception_in_selection": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_slice_error": true, "tests/test_pattern_recognizer_additional.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_get_loc_boolean_array": true, "tests/test_pattern_recognizer_coverage.py::TestDetectPatternsEdgeCases::test_detect_patterns_empty_df": true, "tests/test_pattern_recognizer_coverage.py::TestDetectPatternsEdgeCases::test_detect_patterns_no_relevant_patterns": true, "tests/test_pattern_recognizer_coverage.py::TestCalculatePatternConfidenceEdgeCases::test_calculate_pattern_confidence_empty_series": true, "tests/test_pattern_recognizer_coverage.py::TestCalculatePatternConfidenceEdgeCases::test_calculate_pattern_confidence_no_detected_indices": true, "tests/test_pattern_recognizer_coverage.py::TestCalculatePatternConfidenceEdgeCases::test_calculate_pattern_confidence_missing_vol_sma": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceEdgeCases::test_get_patterns_with_confidence_empty_df": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceEdgeCases::test_get_patterns_with_confidence_insufficient_lookback": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceEdgeCases::test_get_patterns_with_confidence_no_high_confidence_patterns": true, "tests/test_pattern_recognizer_coverage.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_slice_error": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsMissingPatterns::test_detect_patterns_missing_patterns": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsErrorHandling::test_detect_patterns_selection_error": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsErrorHandling::test_detect_patterns_none_input": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsErrorHandling::test_detect_patterns_empty_input": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsErrorHandling::test_detect_patterns_no_relevant_patterns": true, "tests/test_pattern_recognizer_final.py::TestDetectPatternsErrorHandling::test_detect_patterns_no_patterns_found": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_index_mismatch_error": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_key_error_in_loop": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_type_error_in_loop": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_general_error_in_loop": true, "tests/test_pattern_recognizer_final.py::TestCalculatePatternConfidenceErrorHandling::test_calculate_pattern_confidence_reindex_error": true, "tests/test_pattern_recognizer_final.py::TestCalculateContextualsError::test_calculate_contextuals_general_error": true, "tests/test_pattern_recognizer_final.py::TestCalculateContextualsError::test_calculate_contextuals_not_enough_data": true, "tests/test_pattern_recognizer_final.py::TestCalculateContextualsError::test_calculate_contextuals_all_nan_centerline": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_slice_error": true, "tests/test_pattern_recognizer_final.py::TestGetPatternsWithConfidenceErrorHandling::test_get_patterns_with_confidence_bars_ago_calculation": true, "tests/test_pattern_recognizer_final_coverage.py::TestCalculateContextuals::test_calculate_contextuals_with_exception": true, "tests/test_pattern_recognizer_final_coverage.py::TestCalculateContextuals::test_calculate_contextuals_with_insufficient_data": true, "tests/test_pattern_recognizer_final_coverage.py::TestDetectPatterns::test_detect_patterns_with_empty_relevant_patterns": true, "tests/test_pattern_recognizer_final_coverage.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_with_exception_during_slicing": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_valid_input": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_none_input": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_empty_df": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_no_pattern_columns": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_no_relevant_patterns_configured": true, "tests/test_pattern_recognizer_recognizer.py::TestDetectPatterns::test_detect_patterns_no_matching_patterns": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculateContextuals::test_calculate_contextuals_valid_input": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculateContextuals::test_calculate_contextuals_none_input": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculateContextuals::test_calculate_contextuals_empty_df": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculateContextuals::test_calculate_contextuals_missing_columns": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculateContextuals::test_calculate_contextuals_insufficient_data": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_valid_input": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_no_detections": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_reversal_pattern": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_empty_contextuals": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_different_trend_statuses": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_atr_bands": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_high_volume": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_nan_values": true, "tests/test_pattern_recognizer_recognizer.py::TestCalculatePatternConfidence::test_calculate_pattern_confidence_with_mismatched_indices": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_valid_input": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_both_timeframes": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_no_data": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_empty_data": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_no_patterns": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_high_threshold": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_detect_patterns_returns_none": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_empty_contextuals": true, "tests/test_pattern_recognizer_recognizer.py::TestGetPatternsWithConfidence::test_get_patterns_with_confidence_insufficient_data": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_minimal_inputs": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_trend_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_pattern_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_sentiment_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_garch_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_hmm_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_ha_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_macro_info": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_kb_context": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_with_recent_pl": true, "tests/test_prompt_builder_build_prompt.py::TestBuildPrompt::test_build_prompt_all_inputs": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_none": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_not_dict": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_empty_dict": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_no_news": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_other_error": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_with_score": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_with_negative_score": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_without_score": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_with_invalid_score": true, "tests/test_prompt_builder_builder.py::TestFormatSentimentInfo::test_format_sentiment_info_without_label": true, "tests/test_prompt_builder_builder_additional.py::TestFormatMarketInfoAdditional::test_format_market_info_with_none_values": true, "tests/test_prompt_builder_builder_additional.py::TestFormatMarketInfoAdditional::test_format_market_info_with_other_keys": true, "tests/test_prompt_builder_builder_additional.py::TestFormatMarketInfoAdditional::test_format_market_info_with_limited_info": true, "tests/test_prompt_builder_builder_additional.py::TestFormatGarchInfoAdditional::test_format_garch_info_with_limited_info": true, "tests/test_prompt_builder_builder_additional.py::TestFormatHmmInfoAdditional::test_format_hmm_info_with_limited_info": true, "tests/test_prompt_builder_builder_additional.py::TestFormatMacroInfoAdditional::test_format_macro_info_with_limited_info": true, "tests/test_prompt_builder_builder_additional.py::TestFormatTrendInfoAdditional::test_format_trend_info_with_none_values": true, "tests/test_prompt_builder_builder_additional.py::TestFormatTrendInfoAdditional::test_format_trend_info_with_other_keys": true, "tests/test_prompt_builder_builder_additional.py::TestFormatTrendInfoAdditional::test_format_trend_info_with_limited_info": true, "tests/test_prompt_builder_builder_additional.py::TestBuildPromptAdditional::test_build_prompt_with_empty_inputs": true, "tests/test_prompt_builder_builder_additional.py::TestBuildPromptAdditional::test_build_prompt_with_long_kb_context": true, "tests/test_prompt_builder_builder_additional.py::TestGenerateForexPromptAdditional::test_generate_forex_prompt_with_very_long_strategy_context": true, "tests/test_prompt_builder_builder_additional.py::TestGenerateForexPromptAdditional::test_generate_forex_prompt_with_custom_bar_counts": true, "tests/test_prompt_builder_builder_additional.py::TestGenerateForexPromptAdditional::test_generate_forex_prompt_with_empty_inputs": true, "tests/test_prompt_builder_builder_additional.py::TestGenerateForexPromptAdditional::test_generate_forex_prompt_with_special_characters": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_no_relevant_news_error": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_score_and_label": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_label_only": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatSentimentInfoEdgeCases::test_format_sentiment_info_with_non_numeric_score": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMarketInfoEdgeCases::test_format_market_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMarketInfoEdgeCases::test_format_market_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatTrendInfoEdgeCases::test_format_trend_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatTrendInfoEdgeCases::test_format_trend_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatTrendInfoEdgeCases::test_format_trend_info_with_d1_trend": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatPatternInfoEdgeCases::test_format_pattern_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatPatternInfoEdgeCases::test_format_pattern_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatPatternInfoEdgeCases::test_format_pattern_info_with_empty_patterns_list": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatPatternInfoEdgeCases::test_format_pattern_info_with_non_dict_patterns": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatGarchInfoEdgeCases::test_format_garch_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatGarchInfoEdgeCases::test_format_garch_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHmmInfoEdgeCases::test_format_hmm_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHmmInfoEdgeCases::test_format_hmm_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHaInfoEdgeCases::test_format_ha_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHaInfoEdgeCases::test_format_ha_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatHaInfoEdgeCases::test_format_ha_info_with_non_numeric_strength": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMacroInfoEdgeCases::test_format_macro_info_with_none": true, "tests/test_prompt_builder_builder_final_90.py::TestFormatMacroInfoEdgeCases::test_format_macro_info_with_non_dict": true, "tests/test_prompt_builder_builder_final_90.py::TestGenerateForexPromptEdgeCases::test_generate_forex_prompt_with_long_strategy_context": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_none": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_not_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_empty_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_no_patterns": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_empty_patterns": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_invalid_patterns": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_valid_pattern": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_multiple_patterns": true, "tests/test_prompt_builder_format_functions.py::TestFormatPatternInfo::test_format_pattern_info_missing_fields": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_none": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_not_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_empty_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_with_forecast": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_with_status": true, "tests/test_prompt_builder_format_functions.py::TestFormatGarchInfo::test_format_garch_info_with_all": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_none": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_not_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_empty_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_with_state": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_with_description": true, "tests/test_prompt_builder_format_functions.py::TestFormatHmmInfo::test_format_hmm_info_with_all": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_none": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_not_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_empty_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_with_trend": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_with_color": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_with_strength": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_with_invalid_strength": true, "tests/test_prompt_builder_format_functions.py::TestFormatHaInfo::test_format_ha_info_with_all": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_none": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_not_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_empty_dict": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_with_interest_rate": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_with_inflation": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_with_gdp": true, "tests/test_prompt_builder_format_functions.py::TestFormatMacroInfo::test_format_macro_info_with_all": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestGenerateForexPrompt::test_generate_forex_prompt_basic": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestGenerateForexPrompt::test_generate_forex_prompt_without_config_attributes": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestGenerateForexPrompt::test_generate_forex_prompt_long_strategy_context": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_none": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_not_dict": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_empty_dict": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_with_price": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_with_spread": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_with_session": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatMarketInfo::test_format_market_info_with_all": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatTrendInfo::test_format_trend_info_none": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatTrendInfo::test_format_trend_info_not_dict": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatTrendInfo::test_format_trend_info_empty_dict": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatTrendInfo::test_format_trend_info_with_h1": true, "tests/test_prompt_builder_generate_forex_prompt.py::TestFormatTrendInfo::test_format_trend_info_with_all": true, "tests/test_signal_generator.py::TestSignalGenerator::test_run_analysis_modules": true, "tests/test_signal_generator.py::TestSignalGenerator::test_prepare_analysis_context": true, "tests/test_signal_generator.py::TestSignalGenerator::test_generate_signal_gemini_unavailable": true, "tests/test_signal_generator.py::TestSignalGenerator::test_generate_signal_error": true, "tests/test_signal_generator.py::TestModuleLevelFunctions::test_run_analysis_modules": true, "tests/test_signal_generator.py::TestModuleLevelFunctions::test_get_knowledge_base_context": true, "tests/test_signal_generator.py::TestModuleLevelFunctions::test_prepare_analysis_context": true, "tests/test_signal_generator.py::TestModuleLevelFunctions::test_generate_signal": true, "tests/test_signal_generator_coverage.py::TestSignalGeneratorCoverage::test_run_analysis_modules_with_disabled_features": true, "tests/test_signal_generator_coverage.py::TestSignalGeneratorCoverage::test_get_knowledge_base_context_with_qdrant_disabled": true, "tests/test_signal_generator_coverage.py::TestSignalGeneratorCoverage::test_prepare_analysis_context_with_null_h4": true, "tests/test_signal_generator_coverage.py::TestSignalGeneratorCoverage::test_generate_signal_with_custom_model": true, "tests/test_signal_generator_coverage.py::TestModuleLevelFunctionsCoverage::test_run_analysis_modules_with_null_macro_info": true, "tests/test_signal_generator_coverage.py::TestModuleLevelFunctionsCoverage::test_get_knowledge_base_context_with_custom_top_k": true, "tests/test_signal_generator_coverage.py::TestModuleLevelFunctionsCoverage::test_prepare_analysis_context_with_null_news_data": true, "tests/test_signal_generator_coverage.py::TestModuleLevelFunctionsCoverage::test_generate_signal_without_optional_params": true, "tests/test_signal_generator_coverage.py::TestEdgeCases::test_run_analysis_modules_with_trend_analysis_error": true, "tests/test_signal_generator_coverage.py::TestEdgeCases::test_get_knowledge_base_context_with_qdrant_error": true, "tests/test_signal_generator_coverage.py::TestEdgeCases::test_prepare_analysis_context_with_performance_summary_error": true, "tests/test_system_monitor.py::TestHandleConnectionErrors::test_handle_connection_errors_mt5_disconnect": true, "tests/test_system_monitor.py::TestHandleConnectionErrors::test_handle_connection_errors_no_details": true, "tests/test_trend_analyzer_additional.py::TestValidateConfig::test_validate_config_valid": true, "tests/test_trend_analyzer_additional.py::TestValidateConfig::test_validate_config_invalid_short_ma": true, "tests/test_trend_analyzer_additional.py::TestValidateConfig::test_validate_config_invalid_long_ma": true, "tests/test_trend_analyzer_additional.py::TestValidateConfig::test_validate_config_short_greater_than_long": true, "tests/test_trend_analyzer_additional.py::TestValidateConfig::test_validate_config_invalid_ma_type": true, "tests/test_trend_analyzer_additional.py::TestGetTrendStatusAdditional::test_get_trend_status_main_block": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_none_input": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_empty_df": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_missing_close_column": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_insufficient_data": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_ranging": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_sma_type": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_with_nan_values": true, "tests/test_trend_analyzer_analyzer.py::TestGetTrendStatus::test_get_trend_status_error_during_calculation": true, "tests/test_trend_analyzer_analyzer.py::TestGetMtaContext::test_get_mta_context_both_timeframes": true, "tests/test_trend_analyzer_analyzer.py::TestGetMtaContext::test_get_mta_context_h1_only": true, "tests/test_trend_analyzer_analyzer.py::TestGetMtaContext::test_get_mta_context_h4_only": true, "tests/test_trend_analyzer_analyzer.py::TestGetMtaContext::test_get_mta_context_no_data": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_ranging": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_insufficient_data": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_none_dataframe": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_empty_dataframe": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_missing_close_column": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_nan_values": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_custom_ma_periods": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_sma_type": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_ma_calculation_error": true, "tests/test_trend_analyzer_comprehensive.py::TestGetTrendStatus::test_get_trend_status_exception": true, "tests/test_trend_analyzer_comprehensive.py::TestGetMtaContext::test_get_mta_context_both_timeframes": true, "tests/test_trend_analyzer_comprehensive.py::TestGetMtaContext::test_get_mta_context_h1_only": true, "tests/test_trend_analyzer_comprehensive.py::TestGetMtaContext::test_get_mta_context_h4_only": true, "tests/test_trend_analyzer_comprehensive.py::TestGetMtaContext::test_get_mta_context_both_none": true, "tests/test_trend_analyzer_comprehensive.py::TestGetMtaContext::test_get_mta_context_calls_get_trend_status": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_none_dataframe": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_empty_dataframe": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_missing_close_column": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_insufficient_data": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_uptrend": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_downtrend": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_ma_calculation_error": true, "tests/test_trend_analyzer_simple.py::TestGetTrendStatus::test_get_trend_status_exception": true, "tests/test_trend_analyzer_simple.py::TestGetMtaContext::test_get_mta_context_both_none": true, "tests/test_trend_analyzer_simple.py::TestGetMtaContext::test_get_mta_context_calls_get_trend_status": true, "tests/test_trend_analyzer_simple.py::TestGetMtaContext::test_get_mta_context_h1_only": true, "tests/test_trend_analyzer_simple.py::TestGetMtaContext::test_get_mta_context_h4_only": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_to_dict": true, "tests/test_event_bus_config.py::TestTopicConfig::test_to_dict": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_to_dict": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_from_dict": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_from_json": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_from_file_no_event_bus_section": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_file_not_exists": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_file_error": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_initialization": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_initialization_with_ssl": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_initialization_with_sasl": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_invalid_security_protocol": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_invalid_sasl_mechanism": true, "tests/test_event_bus_config.py::TestKafkaConfig::test_model_dump": true, "tests/test_event_bus_config.py::TestTopicConfig::test_initialization": true, "tests/test_event_bus_config.py::TestTopicConfig::test_get_topic_by_event_type": true, "tests/test_event_bus_config.py::TestTopicConfig::test_get_topic_by_event_type_invalid": true, "tests/test_event_bus_config.py::TestTopicConfig::test_model_dump": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_initialization": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_initialization_with_defaults": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_invalid_provider": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_model_dump": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_model_validate": true, "tests/test_event_bus_config.py::TestEventBusConfig::test_model_validate_json": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_default": true, "tests/test_event_bus_config.py::TestGetEventBusConfig::test_get_event_bus_config_singleton": true, "tests/test_event_bus_config_standalone.py::TestKafkaConfig::test_initialization": true, "tests/test_event_bus_config_standalone.py::TestKafkaConfig::test_initialization_with_ssl": true, "tests/test_event_bus_config_standalone.py::TestKafkaConfig::test_invalid_security_protocol": true, "tests/test_event_bus_config_standalone.py::TestKafkaConfig::test_invalid_sasl_mechanism": true, "tests/test_event_bus_config_standalone.py::TestTopicConfig::test_initialization": true, "tests/test_event_bus_config_standalone.py::TestTopicConfig::test_get_topic_by_event_type": true, "tests/test_event_bus_config_standalone.py::TestTopicConfig::test_get_topic_by_event_type_invalid": true, "tests/test_event_bus_config_standalone.py::TestEventBusConfig::test_initialization": true, "tests/test_event_bus_config_standalone.py::TestEventBusConfig::test_invalid_provider": true, "tests/test_event_bus_config_standalone.py::TestGetEventBusConfig::test_get_event_bus_config_default": true, "tests/test_event_bus_config_standalone.py::TestGetEventBusConfig::test_get_event_bus_config_singleton": true, "tests/test_event_bus_config_validators.py::TestKafkaConfigValidators::test_valid_security_protocols": true, "tests/test_event_bus_config_validators.py::TestGetEventBusConfig::test_get_event_bus_config_with_env_vars": true, "tests/test_event_bus_config_validators.py::TestKafkaConfigBasic::test_initialization_with_sasl": true, "tests/integration/test_event_bus_config_integration.py": true, "direct_coverage_test.py": true, "isolated_config_test.py": true, "test_coverage.py": true, "tests/test_event_bus_config_coverage.py::TestKafkaConfig": true, "tests/test_event_bus_config_coverage.py::TestTopicConfig": true, "tests/test_event_bus_config_coverage.py::TestEventBusConfig": true, "tests/test_event_bus_config_coverage.py::TestGetEventBusConfig": true, "tests/test_event_bus_config_direct.py::TestKafkaConfig::test_initialization": true, "tests/test_event_bus_config_direct.py::TestKafkaConfig::test_initialization_with_custom_values": true, "tests/test_event_bus_config_direct.py::TestTopicConfig::test_get_topic_by_event_type": true, "tests/test_event_bus_config_direct.py::TestTopicConfig::test_initialization": true, "tests/test_event_bus_config_direct.py::TestTopicConfig::test_initialization_with_custom_values": true, "tests/test_event_bus_config_direct.py::TestEventBusConfig::test_initialization": true, "tests/test_event_bus_config_direct.py::TestEventBusConfig::test_initialization_with_custom_values": true, "tests/test_event_bus_config_pytest.py::TestKafkaConfig": true, "tests/test_event_bus_config_pytest.py::TestTopicConfig": true, "tests/test_event_bus_config_pytest.py::TestEventBusConfig": true, "tests/test_event_bus_config_pytest.py::TestGetEventBusConfig": true, "tests/test_ml_registry/test_integrations.py": true, "tests/event_bus/test_config_isolated.py": true, "tests/event_bus/test_config_direct.py": true, "tests/event_bus/test_config_final.py": true, "tests/test_event_bus_config_coverage.py": true, "tests/test_event_bus_config_focused.py": true, "tests/test_event_bus_config_pytest.py": true, "tests/test_event_bus_config_simple.py": true, "tests/test_event_bus_config_standalone.py": true, "tests/test_signal_generator.py::TestSignalGenerator::test_run_analysis_modules_success": true, "tests/test_signal_generator_additional.py::TestBackwardCompatibilityFunctions::test_run_analysis_modules_function": true, "tests/test_signal_generator_additional.py::TestBackwardCompatibilityFunctions::test_run_analysis_modules_function_with_macro_info": true, "tests/test_signal_generator_additional.py::TestSignalGeneratorErrorHandling::test_run_analysis_modules_with_analysis_exception": true, "tests/test_signal_generator_complete.py::TestSignalGeneratorCompleteCoverage::test_error_handling_in_analysis_modules": true, "tests/test_signal_generator_complete.py::TestSignalGeneratorCompleteCoverage::test_all_advanced_features_enabled": true, "tests/test_signal_generator_complete.py::TestSignalGeneratorCompleteCoverage::test_backward_compatibility_functions_with_edge_cases": true, "tests/test_signal_generator_final.py::TestSignalGeneratorFinalCoverage::test_specific_feature_flag_combinations": true, "tests/test_signal_generator_final.py::TestSignalGeneratorFinalCoverage::test_error_handling_in_specific_analysis_paths": true, "tests/test_signal_generator_final.py::TestSignalGeneratorFinalCoverage::test_backward_compatibility_functions_edge_cases": true, "tests/test_signal_generator_final.py::TestSignalGeneratorFinalCoverage::test_advanced_feature_error_handling": true, "tests/test_signal_generator_final.py::TestSignalGeneratorFinalCoverage::test_error_logging_coverage": true, "tests/test_signal_generator_90_percent.py::TestSignalGenerator90Percent::test_backward_compatibility_functions_specific_paths": true, "tests/test_signal_generator_90_percent.py::TestSignalGenerator90Percent::test_prepare_analysis_context_edge_cases": true, "tests/test_signal_generator_90_percent.py::TestSignalGenerator90Percent::test_error_handling_blocks_320_331": true, "tests/test_trade_executor_90_percent.py::TestTradeExecutor90Percent::test_calculate_position_size_successful_calculation": true, "tests/test_trade_executor_90_percent.py::TestTradeExecutor90Percent::test_calculate_position_size_zero_point_value": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_env_file_not_found_warning": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_name_error_exception_handling_module_level": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_project_marker_not_found_warning_module_level": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_config_name_error_exception_handling": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_config_project_marker_not_found_warning": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_config_project_marker_not_found_fallback": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_config_could_not_find_project_marker_warning": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_config_fallback_scenarios_comprehensive": true, "tests/test_config_loader_95_percent.py::TestConfigLoader95Percent::test_module_level_fallback_scenarios": true, "tests/test_config_loader_simple_95.py::TestConfigLoaderSimple95::test_config_calculate_paths_project_marker_not_found_loop_exhaustion": true, "tests/test_config_loader_simple_95.py::TestConfigLoaderSimple95::test_config_get_float_invalid_value": true, "tests/test_config_loader_simple_95.py::TestConfigLoaderSimple95::test_config_get_float_none_value": true, "tests/test_config_loader_simple_95.py::TestConfigLoaderSimple95::test_config_get_float_valid_value": true, "tests/test_config_loader_simple_95.py::TestConfigLoaderSimple95::test_config_various_path_scenarios": true, "tests/event_bus/test_config.py::TestKafkaConfigExtended::test_kafka_config_all_sasl_mechanisms": true, "tests/event_bus/test_config.py::TestKafkaConfigExtended::test_kafka_config_model_dump": true, "tests/event_bus/test_config.py::TestKafkaConfigExtended::test_kafka_config_edge_cases": true, "tests/event_bus/test_config.py::TestKafkaConfigExtended::test_kafka_config_sasl_mechanism_none": true, "tests/event_bus/test_config.py::TestKafkaConfigExtended::test_kafka_config_ssl_configuration": true, "tests/event_bus/test_config.py::TestTopicConfigExtended::test_topic_config_partial_custom": true, "tests/event_bus/test_config.py::TestTopicConfigExtended::test_topic_config_model_dump": true, "tests/event_bus/test_config.py::TestTopicConfigExtended::test_topic_config_special_chars": true, "tests/event_bus/test_config.py::TestTopicConfigExtended::test_get_topic_by_event_type_with_custom_topics": true, "tests/event_bus/test_config.py::TestEventBusConfigExtended::test_event_bus_config_pulsar_provider": true, "tests/event_bus/test_config.py::TestEventBusConfigExtended::test_event_bus_config_model_dump": true, "tests/event_bus/test_config.py::TestEventBusConfigExtended::test_event_bus_config_combinations": true, "tests/event_bus/test_config.py::TestEventBusConfigExtended::test_event_bus_config_disabled": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_all_env_vars": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_no_env_vars": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_true": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_one": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_yes": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_false": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_zero": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_no": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_boolean_parsing_invalid": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_pulsar_provider": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_with_different_app_config": true, "tests/event_bus/test_config.py::TestGetEventBusConfigExtended::test_get_event_bus_config_singleton_persistence": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_kafka_config_sasl_mechanism_validation_error": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_kafka_config_sasl_credentials_validation_error": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_topic_config_get_topic_by_event_type_all_branches": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_event_bus_config_provider_validation_error": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_full_env_coverage": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_boolean_false_parsing": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_boolean_zero_parsing": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_boolean_no_parsing": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_boolean_invalid_parsing": true, "tests/event_bus/test_config.py::TestMissingCoverageTargeted::test_get_event_bus_config_pulsar_provider_coverage": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_defaults": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_security_protocol_validation_invalid": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_sasl_mechanism_validation_invalid": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_sasl_credentials_missing": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_sasl_credentials_partial": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_sasl_valid_combinations": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_sasl_mechanism_none_allowed": true, "tests/event_bus/test_config_coverage.py::TestKafkaConfigCoverage::test_kafka_config_model_dump": true, "tests/event_bus/test_config_coverage.py::TestTopicConfigCoverage::test_topic_config_defaults": true, "tests/event_bus/test_config_coverage.py::TestTopicConfigCoverage::test_get_topic_by_event_type_all_types": true, "tests/event_bus/test_config_coverage.py::TestTopicConfigCoverage::test_get_topic_by_event_type_custom_topics": true, "tests/event_bus/test_config_coverage.py::TestTopicConfigCoverage::test_topic_config_model_dump": true, "tests/event_bus/test_config_coverage.py::TestEventBusConfigCoverage::test_event_bus_config_defaults": true, "tests/event_bus/test_config_coverage.py::TestEventBusConfigCoverage::test_event_bus_config_provider_validation_invalid": true, "tests/event_bus/test_config_coverage.py::TestEventBusConfigCoverage::test_event_bus_config_valid_providers": true, "tests/event_bus/test_config_coverage.py::TestEventBusConfigCoverage::test_event_bus_config_model_dump": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_defaults": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_from_env_basic": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_ssl_config": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_sasl_config": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_custom_topics": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_boolean_parsing": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_pulsar_provider": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_singleton": true, "tests/event_bus/test_config_coverage.py::TestGetEventBusConfigCoverage::test_get_event_bus_config_singleton_with_env_change": true, "tests/test_event_bus_pytest.py::test_event_type_enum": true, "tests/test_event_bus_pytest.py::test_time_frame_enum": true, "tests/test_event_bus_pytest.py::test_base_event": true, "tests/test_event_bus_pytest.py::test_ohlcv_data": true, "tests/test_event_bus_pytest.py::test_market_data_event": true, "tests/test_indicators_direct.py::test_calculate_indicators_empty_dataframe": true, "tests/test_qdrant_service_additional.py": true, "tests/test_qdrant_service_batch.py": true, "tests/test_qdrant_service_comprehensive.py": true, "tests/test_qdrant_service_edge_cases.py": true, "tests/test_event_bus_config_90_percent.py::TestEventBusConfig90Percent::test_kafka_config_validate_sasl_mechanism_valid": true, "tests/test_event_bus_config_90_percent.py::TestEventBusConfig90Percent::test_kafka_config_edge_cases": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_client_get_correlation_alerts": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_client_get_correlation_matrix": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_client_get_correlation_trend": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_alert": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_matrix": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_method_enum": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_pair": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_settings": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_strength_enum": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_trend": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_correlation_visualization": true, "tests/test_correlation_matrix_comprehensive.py::TestCorrelationMatrixComprehensive::test_time_window_enum": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_register_analysis_handler": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_filter_by_symbol": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_filter_by_analysis_type": true, "tests/test_event_bus_analysis_consumer.py::TestAnalysisConsumer::test_multiple_handlers": true, "tests/test_event_bus_basic.py::TestEventBusBasic::test_base_event": true, "tests/test_event_bus_basic.py::TestEventBusBasic::test_event_type_enum": true, "tests/test_event_bus_basic.py::TestEventBusBasic::test_market_data_event": true, "tests/test_event_bus_basic.py::TestEventBusBasic::test_ohlcv_data": true, "tests/test_event_bus_basic.py::TestEventBusBasic::test_time_frame_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_analysis_event": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_analysis_type_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_base_event": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_event_type_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_kafka_consumer": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_kafka_producer": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_market_data_event": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_order_event": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_order_status_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_order_type_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_time_frame_enum": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_trade_event": true, "tests/test_event_bus_comprehensive.py::TestEventBusComprehensive::test_trade_status_enum": true, "tests/test_event_bus_consumer.py::TestBaseConsumer::test_abstract_methods": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_register_handler": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_unregister_handler": true, "tests/test_event_bus_consumer.py::TestKafkaConsumer::test_dispatch_event": true, "tests/test_event_bus_consumer.py::TestMarketDataConsumer::test_register_ohlcv_handler": true, "tests/test_event_bus_consumer.py::TestMarketDataConsumer::test_register_tick_handler": true, "tests/test_event_bus_consumer_comprehensive.py::TestKafkaConsumerComprehensive::test_consume_loop_with_error": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_register_ohlcv_handler": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_register_tick_handler": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_filter_by_symbol": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_filter_by_timeframe": true, "tests/test_event_bus_consumer_comprehensive.py::TestMarketDataConsumerComprehensive::test_filter_by_data_type": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_initialization_with_group_id": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_initialization_with_auto_offset_reset": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_consume_loop_with_message": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_consume_loop_with_message_error": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_consume_loop_with_exception": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_parse_message_with_invalid_json": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_parse_message_with_invalid_event_type": true, "tests/test_event_bus_consumer_detailed.py::TestKafkaConsumerDetailed::test_parse_message_with_missing_fields": true, "tests/test_event_bus_consumer_extended.py::TestOrderConsumer::test_initialization": true, "tests/test_event_bus_consumer_extended.py::TestOrderConsumer::test_register_order_handler": true, "tests/test_event_bus_consumer_extended.py::TestOrderConsumer::test_filter_by_symbol": true, "tests/test_event_bus_consumer_extended.py::TestOrderConsumer::test_filter_by_order_type": true, "tests/test_event_bus_consumer_extended.py::TestTradeConsumer::test_initialization": true, "tests/test_event_bus_consumer_extended.py::TestTradeConsumer::test_register_trade_handler": true, "tests/test_event_bus_consumer_extended.py::TestTradeConsumer::test_filter_by_symbol": true, "tests/test_event_bus_consumer_extended.py::TestTradeConsumer::test_filter_by_trade_type": true, "tests/test_event_bus_consumer_extended.py::TestAnalysisConsumer::test_initialization": true, "tests/test_event_bus_consumer_extended.py::TestAnalysisConsumer::test_register_analysis_handler": true, "tests/test_event_bus_consumer_extended.py::TestAnalysisConsumer::test_filter_by_symbol": true, "tests/test_event_bus_consumer_extended.py::TestAnalysisConsumer::test_filter_by_analysis_type": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_dispatch_event": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_kafka_consumer_initialization": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_kafka_producer_initialization": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_market_data_consumer": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_market_data_producer": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_produce_event": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_register_handler": true, "tests/test_event_bus_integration.py::TestEventBusIntegration::test_subscribe_to_topics": true, "tests/test_event_bus_producer.py::TestBaseProducer::test_abstract_methods": true, "tests/test_event_bus_producer_comprehensive.py::TestMarketDataProducerComprehensive::test_initialization": true, "tests/test_event_bus_producer_comprehensive.py::TestOrderProducerComprehensive::test_initialization": true, "tests/test_event_bus_producer_comprehensive.py::TestTradeProducerComprehensive::test_initialization": true, "tests/test_event_bus_producer_comprehensive.py::TestAnalysisProducerComprehensive::test_initialization": true, "tests/test_event_bus_producer_extended.py::TestOrderProducer::test_publish_order": true, "tests/test_event_bus_producer_extended.py::TestTradeProducer::test_publish_trade": true, "tests/test_event_bus_producer_extended.py::TestAnalysisProducer::test_publish_analysis": true, "tests/test_event_bus_schemas.py::TestEventType::test_event_type_values": true, "tests/test_event_bus_schemas.py::TestTimeFrame::test_time_frame_values": true, "tests/test_event_bus_schemas.py::TestOrderType::test_order_type_values": true, "tests/test_event_bus_schemas.py::TestOrderStatus::test_order_status_values": true, "tests/test_event_bus_schemas.py::TestTradeStatus::test_trade_status_values": true, "tests/test_event_bus_schemas.py::TestAnalysisType::test_analysis_type_values": true, "tests/test_event_bus_schemas.py::TestBaseEvent::test_base_event_initialization": true, "tests/test_event_bus_schemas.py::TestBaseEvent::test_base_event_serialization": true, "tests/test_event_bus_schemas.py::TestOHLCVData::test_ohlcv_data_initialization": true, "tests/test_event_bus_schemas.py::TestMarketDataEvent::test_market_data_event_initialization": true, "tests/test_event_bus_schemas.py::TestMarketDataEvent::test_market_data_event_with_tick_data": true, "tests/test_event_bus_schemas.py::TestMarketDataEvent::test_market_data_event_with_invalid_data_type": true, "tests/test_event_bus_schemas.py::TestOrderEvent::test_order_event_initialization": true, "tests/test_event_bus_schemas.py::TestAnalysisEvent::test_analysis_event_initialization": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_initialization": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_register_ohlcv_handler": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_register_tick_handler": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_register_order_book_handler": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_filter_by_symbol": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_filter_by_timeframe": true, "tests/test_event_bus_specialized_consumers.py::TestMarketDataConsumer::test_filter_by_data_type": true, "tests/test_event_bus_specialized_consumers.py::TestOrderConsumer::test_initialization": true, "tests/test_event_bus_specialized_consumers.py::TestOrderConsumer::test_register_order_handler": true, "tests/test_event_bus_specialized_consumers.py::TestOrderConsumer::test_filter_by_symbol": true, "tests/test_event_bus_specialized_consumers.py::TestOrderConsumer::test_filter_by_order_type": true, "tests/test_event_bus_specialized_consumers.py::TestTradeConsumer::test_initialization": true, "tests/test_event_bus_specialized_consumers.py::TestTradeConsumer::test_register_trade_handler": true, "tests/test_event_bus_specialized_consumers.py::TestTradeConsumer::test_filter_by_symbol": true, "tests/test_event_bus_specialized_consumers.py::TestTradeConsumer::test_filter_by_trade_type": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_empty_dataframe": true, "tests/test_indicators_simple_new.py::TestCalculateIndicators::test_calculate_indicators_with_none_dataframe": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_client_add_snapshot": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_client_add_trade": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_client_clear_data": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_client_convert_order_book_to_snapshot": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_color_scheme_enum": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_market_depth_snapshot": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_trade_entry": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_visualization_settings": true, "tests/test_market_depth_visualizer_comprehensive.py::TestMarketDepthVisualizerComprehensive::test_visualization_type_enum": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_analyze_order_book_imbalance": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_calculate_imbalance_level": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_client_get_order_book": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_client_get_order_book_async": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_detect_large_orders": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_generate_order_flow_signals": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_get_order_flow_context": true, "tests/test_order_flow_analyzer_comprehensive.py::TestOrderFlowAnalyzerComprehensive::test_identify_support_resistance_levels": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_clear_cache": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_clear_cache_for_symbol": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_client_initialization": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_get_order_book": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_get_order_book_with_cache": true, "tests/test_order_flow_client.py::TestOrderFlowClient::test_get_order_book_with_force_refresh": true, "tests/test_order_flow_event_bus_integration.py::TestOrderFlowEventBusIntegration::test_get_order_flow_context_and_publish": true, "tests/test_order_flow_models_simple.py::TestOrderFlowModelsSimple::test_imbalance_level_enum": true, "tests/test_order_flow_models_simple.py::TestOrderFlowModelsSimple::test_order_flow_imbalance": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_numpy_import_error_handling": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_pandas_import_error_handling": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_pydantic_import_error_handling": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_metric_time_series_numpy_validation": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_metric_time_series_to_dataframe_pandas_error": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_row_bounds": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_column_bounds": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_row_span": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_col_span": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_negative_row": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_dashboard_layout_chart_validation_negative_column": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_metric_value_without_numpy_validation": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_metric_time_series_without_numpy_validation": true, "tests/test_metrics_dashboard_models_90_percent.py::TestMetricsDashboardModels90Percent::test_metric_time_series_to_dataframe_success": true, "tests/test_order_flow_analyzer/test_models.py": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationPair::test_correlation_pair_creation_valid": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationPair::test_correlation_pair_invalid_correlation_high": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationPair::test_correlation_pair_invalid_correlation_low": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationPair::test_correlation_pair_invalid_strength_high": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationPair::test_correlation_pair_invalid_strength_low": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationAlert::test_correlation_alert_creation_valid": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationAlert::test_correlation_alert_invalid_change_high": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationAlert::test_correlation_alert_invalid_change_low": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationVisualization::test_correlation_visualization_creation_valid": true, "tests/test_correlation_matrix_models_60_percent.py::TestCorrelationVisualization::test_correlation_visualization_invalid_image_data": true, "tests/test_market_depth_visualizer_models_70_percent.py": true, "tests/test_market_depth_visualizer_models_70_percent_corrected.py": true, "tests/test_volatility_indices_models_90_percent.py::test_volatility_trend_volatility_level_all_cases": true, "tests/test_multilingual_news_models_95_percent.py::test_news_context_trading_signal": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_validation_error_tick_not_dict": true, "tests/event_bus/test_event_schemas.py::TestMarketDataEvent::test_market_data_event_validation_error_ohlcv_wrong_type": true, "tests/test_config_loader_94_to_100_coverage.py::TestConfigLoaderMissingLines::test_env_file_not_found_warning_line_73": true, "tests/test_config_loader_94_to_100_coverage.py::TestConfigLoaderMissingLines::test_name_error_fallback_lines_100_102": true, "tests/test_config_loader_94_to_100_coverage.py::TestConfigLoaderMissingLines::test_project_marker_not_found_warning_line_113": true, "tests/test_config_loader_87_to_90_coverage.py::TestConfigLoaderMissingLines::test_dotenv_not_found_warning_line_73": true, "tests/test_config_loader_87_to_90_coverage.py::TestConfigLoaderMissingLines::test_fallback_script_dir_lines_100_102": true, "tests/test_config_loader_87_to_90_coverage.py::TestConfigLoaderMissingLines::test_symbols_parsing_exception_lines_281_282": true, "tests/test_config_loader_87_to_90_coverage.py::TestConfigLoaderMissingLines::test_get_env_bool_invalid_value_lines_334_335": true, "tests/test_config_loader_87_to_90_coverage.py::TestConfigLoaderMissingLines::test_load_env_variables_missing_credentials_lines_378_394": true, "tests/test_metrics_dashboard_models_100_percent.py::TestMetricsDashboardModels100Percent::test_metric_time_series_numpy_validation": true, "tests/test_metrics_dashboard_models_100_percent.py::TestMetricsDashboardModels100Percent::test_metric_time_series_to_dataframe_pandas_error": true, "tests/test_metrics_dashboard_models_100_percent.py::TestMetricsDashboardModels100Percent::test_successful_model_creation": true, "tests/test_metrics_dashboard_models_100_percent.py::TestMetricsDashboardModels100Percent::test_metric_time_series_to_dataframe_success": true, "tests/test_market_depth_visualizer_models_100_percent.py::TestMarketDepthVisualizerModels100Percent::test_import_error_handling_pandas": true, "tests/test_market_depth_visualizer_models_100_percent.py::TestMarketDepthVisualizerModels100Percent::test_market_depth_snapshot_volume_validation": true, "tests/test_order_book_models_100_percent.py::TestOrderBookModels100Percent::test_order_book_properties": true, "tests/test_order_book_models_100_percent.py::TestOrderBookModels100Percent::test_non_pydantic_fallback": true, "tests/test_correlation_matrix_models_enhanced.py::TestCorrelationMatrixModelsEnhanced::test_non_pydantic_correlation_pair_comprehensive": true, "tests/test_correlation_matrix_models_enhanced.py::TestCorrelationMatrixModelsEnhanced::test_non_pydantic_correlation_matrix_comprehensive": true, "tests/test_market_depth_visualizer_models_enhanced.py::TestMarketDepthVisualizerModelsEnhanced::test_import_error_handling_numpy": true, "tests/test_market_depth_visualizer_models_enhanced.py::TestMarketDepthVisualizerModelsEnhanced::test_market_depth_snapshot_properties": true, "tests/test_order_flow_analyzer_models_enhanced.py::TestOrderFlowAnalyzerModelsEnhanced::test_non_pydantic_order_flow_imbalance_comprehensive": true, "tests/test_order_flow_analyzer_models_enhanced.py::TestOrderFlowAnalyzerModelsEnhanced::test_non_pydantic_large_order_comprehensive": true, "tests/test_order_flow_analyzer_models_phase4.py::TestOrderFlowAnalyzerModelsPhase4::test_non_pydantic_order_flow_imbalance_basic": true, "tests/test_order_flow_analyzer_models_phase4.py::TestOrderFlowAnalyzerModelsPhase4::test_non_pydantic_large_order_basic": true, "tests/test_order_flow_analyzer_models_phase5.py": true, "tests/test_order_flow_analyzer_models_phase5_fixed.py": true, "tests/test_correlation_matrix_models_phase5.py": true, "tests/test_correlation_matrix_models_phase5_clean.py::TestCorrelationMatrixModelsPhase5Clean::test_comprehensive_model_validation": true, "tests/test_metrics_dashboard_models_phase5.py": true, "tests/test_metrics_dashboard_models_phase5_clean.py::TestMetricsDashboardModelsPhase5Clean::test_comprehensive_model_validation": true, "tests/test_metrics_dashboard_models_phase5_clean.py::TestMetricsDashboardModelsPhase5Clean::test_comprehensive_validation_errors": true, "tests/test_metrics_dashboard_models_phase5_clean.py::TestMetricsDashboardModelsPhase5Clean::test_optional_dependencies": true, "tests/test_cot_reports_models_phase5.py": true, "tests/test_cot_reports_models_phase5_clean.py": true, "tests/test_cot_reports_models_phase5_final.py": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_time_window_enum": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_method_enum": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_strength_enum": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_settings_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_settings_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_pair_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_pair_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_strength_mapping": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_matrix_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_matrix_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_matrix_to_dataframe": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_trend_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_trend_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_trend_to_dataframe": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_alert_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_alert_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_visualization_basic_functionality": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_correlation_visualization_validation": true, "tests/test_correlation_matrix_models_phase5m.py::TestCorrelationMatrixModelsPhase5M::test_edge_cases_and_boundary_conditions": true, "tests/test_metrics_dashboard_models_phase5x_90_coverage.py": true, "tests/test_metrics_dashboard_models_phase5x_simple.py::TestMetricsDashboardModelsPhase5XSimple::test_metric_time_series_validation": true, "tests/test_metrics_dashboard_models_phase5x_simple.py::TestMetricsDashboardModelsPhase5XSimple::test_to_dataframe_method": true, "tests/test_metrics_dashboard_models_phase5y_final.py::TestMetricsDashboardModelsPhase5YFinal::test_pandas_import_error_in_to_dataframe": true, "tests/test_metrics_dashboard_models_phase5y_final.py::TestMetricsDashboardModelsPhase5YFinal::test_market_metrics_sentiment_validation_edge_cases": true, "tests/test_market_depth_visualizer_models_phase5z_simple.py": true, "tests/test_market_depth_models_phase5aa_comprehensive.py": true, "tests/test_market_depth_models_phase5aa_simple.py": true, "tests/test_market_depth_phase5bb_comprehensive.py": true, "tests/test_market_depth_phase5bb_simple.py": true, "tests/test_market_depth_phase5cc.py": true, "tests/test_market_depth_phase5ee.py": true, "tests/test_market_depth_phase5ff.py::TestMarketDepthPhase5FF::test_comprehensive_import_fallbacks": true, "tests/test_correlation_matrix_models_phase6a.py": true, "tests/test_market_depth_phase6b.py": true, "tests/test_market_depth_phase6b_simple.py": true, "tests/test_market_depth_phase6c.py": true, "tests/test_market_depth_phase6c_simple.py::TestMarketDepthPhase6CSimple::test_comprehensive_fallback_classes": true, "tests/test_market_depth_phase6d.py": true, "tests/test_market_depth_phase6d_simple.py": true, "tests/test_market_depth_phase6e_massive.py": true, "tests/test_market_depth_phase6f_surgical.py::TestMarketDepthPhase6FSurgical::test_surgical_fallback_attack": true, "tests/test_market_depth_phase6f_surgical_fixed.py::TestMarketDepthPhase6FSurgicalFixed::test_surgical_fallback_attack_fixed": true, "tests/test_market_depth_phase6g_final.py": true, "tests/test_market_depth_visualizer_models_phase5z_final.py::TestMarketDepthVisualizerModelsPhase5ZFinal::test_enum_values_comprehensive": true, "tests/test_market_depth_visualizer_models_phase5z_final.py::TestMarketDepthVisualizerModelsPhase5ZFinal::test_depth_chart_settings_comprehensive": true, "tests/test_multilingual_news_models_comprehensive.py::TestNewsModels::test_news_article_age_hours_without_timezone": true, "tests/event_bus/test_event_schemas.py": true, "tests/test_multilingual_news_models_95_percent.py::test_news_summary_time_range_empty_articles": true, "tests/test_news_service_comprehensive.py::TestGetNewsContext::test_get_news_context_timeout": true, "tests/test_gemini_client_90_coverage.py::TestGetGeminiSignalEdgeCases::test_get_gemini_signal_response_parsing_error": true, "tests/test_macro_analyzer_analyzer.py": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_success": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_rate_limit": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_timeout": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_http_error": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_bad_format": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_general_error": true, "tests/test_news_service.py::TestGetNewsContext::test_get_news_context_use_config_api_key": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_success": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_no_events": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_error_string": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_no_relevant_news": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_high_impact": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_time_relevant": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_invalid_date": true, "tests/test_news_service.py::TestFilterNewsForSymbol::test_filter_news_for_symbol_non_dict_event": true, "tests/test_config_loader.py": true, "tests/test_news_service.py": true, "tests/test_config_loader_100_coverage.py": true, "tests/test_indicators.py": true, "tests/test_config_loader_90_coverage.py": true, "tests/test_event_bus_config_90_percent.py": true, "tests/test_gemini_client.py": true, "tests/test_config_loader_final_90_coverage.py::TestConfigPathCalculation::test_calculate_paths_project_root_found": true}