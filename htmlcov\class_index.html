<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 20:02 -0500
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3___init___py.html">src\forex_bot\__init__.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3___main___py.html">src\forex_bot\__main__.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3___main___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_analysis_constants_py.html">src\forex_bot\analysis_constants.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_analysis_constants_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1___init___py.html">src\forex_bot\backtester\__init__.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_backtester_service_py.html#t40">src\forex_bot\backtester\backtester_service.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_backtester_service_py.html#t40"><data value='BacktesterService'>BacktesterService</data></a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_backtester_service_py.html">src\forex_bot\backtester\backtester_service.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_backtester_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t16">src\forex_bot\backtester\config.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t16"><data value='SlippageModel'>SlippageModel</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t53">src\forex_bot\backtester\config.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t53"><data value='CircuitBreakerConfig'>CircuitBreakerConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t82">src\forex_bot\backtester\config.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html#t82"><data value='BacktesterConfig'>BacktesterConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html">src\forex_bot\backtester\config.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_order_simulator_py.html#t28">src\forex_bot\backtester\order_simulator.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_order_simulator_py.html#t28"><data value='OrderSimulator'>OrderSimulator</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_order_simulator_py.html">src\forex_bot\backtester\order_simulator.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_order_simulator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_performance_tracker_py.html#t26">src\forex_bot\backtester\performance_tracker.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_performance_tracker_py.html#t26"><data value='BacktesterPerformanceTracker'>BacktesterPerformanceTracker</data></a></td>
                <td>213</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_performance_tracker_py.html">src\forex_bot\backtester\performance_tracker.py</a></td>
                <td class="name left"><a href="z_b50040b513dcffe1_performance_tracker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_orchestrator_py.html">src\forex_bot\bot_orchestrator.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_orchestrator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>823</td>
                <td>769</td>
                <td>0</td>
                <td class="right" data-ratio="54 823">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_scheduler_py.html#t31">src\forex_bot\bot_scheduler.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_scheduler_py.html#t31"><data value='BotScheduler'>BotScheduler</data></a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_scheduler_py.html">src\forex_bot\bot_scheduler.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_scheduler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="26 58">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_loader_py.html#t141">src\forex_bot\config_loader.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_config_loader_py.html#t141"><data value='Config'>Config</data></a></td>
                <td>82</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="74 82">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_loader_py.html">src\forex_bot\config_loader.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>152</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="147 152">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_settings_py.html#t64">src\forex_bot\config_settings.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_config_settings_py.html#t64"><data value='Config'>Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_settings_py.html">src\forex_bot\config_settings.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_config_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>151</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="151 151">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d___init___py.html">src\forex_bot\correlation_matrix\__init__.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_py.html">src\forex_bot\correlation_matrix\calculator.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="22 69">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part2_py.html">src\forex_bot\correlation_matrix\calculator_part2.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="15 71">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part3_py.html">src\forex_bot\correlation_matrix\calculator_part3.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part3_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="12 62">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_client_py.html#t33">src\forex_bot\correlation_matrix\client.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_client_py.html#t33"><data value='CorrelationClient'>CorrelationClient</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_client_py.html">src\forex_bot\correlation_matrix\client.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t38">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t38"><data value='TimeWindow'>TimeWindow</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t46">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t46"><data value='CorrelationMethod'>CorrelationMethod</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t52">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t52"><data value='CorrelationStrength'>CorrelationStrength</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t62">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t62"><data value='CorrelationSettings'>CorrelationSettings</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t76">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t76"><data value='CorrelationPair'>CorrelationPair</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t113">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t113"><data value='CorrelationMatrix'>CorrelationMatrix</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t143">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t143"><data value='CorrelationTrend'>CorrelationTrend</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t172">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t172"><data value='CorrelationAlert'>CorrelationAlert</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t193">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html#t193"><data value='CorrelationVisualization'>CorrelationVisualization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html">src\forex_bot\correlation_matrix\models.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>182</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="91 182">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html#t16">src\forex_bot\correlation_matrix\models_part2.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html#t16"><data value='CorrelationAlert'>CorrelationAlert</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html#t38">src\forex_bot\correlation_matrix\models_part2.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html#t38"><data value='CorrelationVisualization'>CorrelationVisualization</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html">src\forex_bot\correlation_matrix\models_part2.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_py.html">src\forex_bot\correlation_matrix\visualizer.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="27 95">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_part2_py.html">src\forex_bot\correlation_matrix\visualizer_part2.py</a></td>
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>89</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="21 89">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a___init___py.html">src\forex_bot\cot_reports\__init__.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_analyzer_py.html">src\forex_bot\cot_reports\analyzer.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="17 93">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_client_py.html">src\forex_bot\cot_reports\client.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="30 134">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html#t12">src\forex_bot\cot_reports\models.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html#t12"><data value='COTReport'>COTReport</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html#t111">src\forex_bot\cot_reports\models.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html#t111"><data value='COTPositioning'>COTPositioning</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html">src\forex_bot\cot_reports\models.py</a></td>
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e___init___py.html">src\forex_bot\cvd\__init__.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_calculator_py.html">src\forex_bot\cvd\calculator.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>135</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="12 135">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_client_py.html#t30">src\forex_bot\cvd\client.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_client_py.html#t30"><data value='CVDClient'>CVDClient</data></a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_client_py.html">src\forex_bot\cvd\client.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html#t13">src\forex_bot\cvd\models.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html#t13"><data value='CVDResult'>CVDResult</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html#t59">src\forex_bot\cvd\models.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html#t59"><data value='CVDDivergence'>CVDDivergence</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html">src\forex_bot\cvd\models.py</a></td>
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357___init___py.html">src\forex_bot\event_bus\__init__.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t21">src\forex_bot\event_bus\config.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t21"><data value='KafkaConfig'>KafkaConfig</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t84">src\forex_bot\event_bus\config.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t84"><data value='TopicConfig'>TopicConfig</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t120">src\forex_bot\event_bus\config.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html#t120"><data value='EventBusConfig'>EventBusConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html">src\forex_bot\event_bus\config.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="37 41">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t41">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t41"><data value='BaseConsumer'>BaseConsumer</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t137">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t137"><data value='KafkaConsumer'>KafkaConsumer</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t350">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t350"><data value='MarketDataConsumer'>MarketDataConsumer</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t437">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t437"><data value='OrderConsumer'>OrderConsumer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t508">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t508"><data value='TradeConsumer'>TradeConsumer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t579">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html#t579"><data value='AnalysisConsumer'>AnalysisConsumer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html">src\forex_bot\event_bus\consumer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="54 55">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t14">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t14"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t23">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t23"><data value='BaseEvent'>BaseEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t31">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t31"><data value='Config'>BaseEvent.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t38">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t38"><data value='TimeFrame'>TimeFrame</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t51">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t51"><data value='OHLCVData'>OHLCVData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t61">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t61"><data value='MarketDataEvent'>MarketDataEvent</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t82">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t82"><data value='OrderType'>OrderType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t92">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t92"><data value='OrderStatus'>OrderStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t101">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t101"><data value='OrderEvent'>OrderEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t118">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t118"><data value='TradeStatus'>TradeStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t125">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t125"><data value='TradeEvent'>TradeEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t145">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t145"><data value='AnalysisType'>AnalysisType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t156">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html#t156"><data value='AnalysisEvent'>AnalysisEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="107 107">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t30">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t30"><data value='BaseProducer'>BaseProducer</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t77">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t77"><data value='KafkaProducer'>KafkaProducer</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t206">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t206"><data value='MarketDataProducer'>MarketDataProducer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t286">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t286"><data value='OrderProducer'>OrderProducer</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t355">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t355"><data value='TradeProducer'>TradeProducer</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t433">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html#t433"><data value='AnalysisProducer'>AnalysisProducer</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html">src\forex_bot\event_bus\producer.py</a></td>
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="41 42">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_gemini_client_py.html">src\forex_bot\gemini_client.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_gemini_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>121</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="23 121">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9___init___py.html">src\forex_bot\global_pmi\__init__.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_analyzer_py.html">src\forex_bot\global_pmi\analyzer.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>156</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="13 156">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_client_py.html">src\forex_bot\global_pmi\client.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>111</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="26 111">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t12">src\forex_bot\global_pmi\models.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t12"><data value='PMIData'>PMIData</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t44">src\forex_bot\global_pmi\models.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t44"><data value='PMITrend'>PMITrend</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t103">src\forex_bot\global_pmi\models.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html#t103"><data value='PMIDivergence'>PMIDivergence</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html">src\forex_bot\global_pmi\models.py</a></td>
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_health_endpoints_py.html#t48">src\forex_bot\health_endpoints.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_health_endpoints_py.html#t48"><data value='HealthRequestHandler'>HealthRequestHandler</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_health_endpoints_py.html">src\forex_bot\health_endpoints.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_health_endpoints_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>196</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c578cea568b4ed4d___init___py.html">src\forex_bot\heikin_ashi\__init__.py</a></td>
                <td class="name left"><a href="z_c578cea568b4ed4d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c578cea568b4ed4d_calculator_py.html">src\forex_bot\heikin_ashi\calculator.py</a></td>
                <td class="name left"><a href="z_c578cea568b4ed4d_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="17 95">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_indicators_py.html">src\forex_bot\indicators.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_indicators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="7 62">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialization_py.html#t23">src\forex_bot\initialization.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_initialization_py.html#t23"><data value='Initializer'>Initializer</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialization_py.html">src\forex_bot\initialization.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_initialization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="23 35">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialize_py.html#t46">src\forex_bot\initialize.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_initialize_py.html#t46"><data value='BotInitializer'>BotInitializer</data></a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialize_py.html">src\forex_bot\initialize.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_initialize_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_launcher_py.html">src\forex_bot\launcher.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_launcher_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>194</td>
                <td>194</td>
                <td>0</td>
                <td class="right" data-ratio="0 194">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_log_manager_py.html#t40">src\forex_bot\log_manager.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_log_manager_py.html#t40"><data value='LogManager'>LogManager</data></a></td>
                <td>173</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_log_manager_py.html">src\forex_bot\log_manager.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_log_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="31 57">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_log_uploader_py.html">src\forex_bot\log_uploader.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_log_uploader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="15 104">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01___init___py.html">src\forex_bot\macro_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_d2cad6892f133d01___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01_analyzer_py.html">src\forex_bot\macro_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_d2cad6892f133d01_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="22 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01_fetcher_py.html#t60">src\forex_bot\macro_analyzer\fetcher.py</a></td>
                <td class="name left"><a href="z_d2cad6892f133d01_fetcher_py.html#t60"><data value='VixCache'>VixCache</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01_fetcher_py.html">src\forex_bot\macro_analyzer\fetcher.py</a></td>
                <td class="name left"><a href="z_d2cad6892f133d01_fetcher_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>201</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="39 201">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc___init___py.html">src\forex_bot\market_depth_visualizer\__init__.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_client_py.html#t24">src\forex_bot\market_depth_visualizer\client.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_client_py.html#t24"><data value='MarketDepthClient'>MarketDepthClient</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_client_py.html">src\forex_bot\market_depth_visualizer\client.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t32">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t32"><data value='VisualizationType'>VisualizationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t41">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t41"><data value='ColorScheme'>ColorScheme</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t50">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t50"><data value='DepthChartSettings'>DepthChartSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t63">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t63"><data value='HeatmapSettings'>HeatmapSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t74">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t74"><data value='TimeAndSalesSettings'>TimeAndSalesSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t87">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t87"><data value='LiquidityMapSettings'>LiquidityMapSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t98">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t98"><data value='OrderFlowFootprintSettings'>OrderFlowFootprintSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t110">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t110"><data value='DashboardSettings'>DashboardSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t123">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t123"><data value='VisualizationSettings'>VisualizationSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t132">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t132"><data value='TradeEntry'>TradeEntry</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t146">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t146"><data value='MarketDepthSnapshot'>MarketDepthSnapshot</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t253">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t253"><data value='MarketDepthVisualization'>MarketDepthVisualization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t267">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html#t267"><data value='MarketDepthDashboard'>MarketDepthDashboard</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>220</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="145 220">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t21">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t21"><data value='DashboardSettings'>DashboardSettings</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t40">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t40"><data value='VisualizationSettings'>VisualizationSettings</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t60">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t60"><data value='TradeEntry'>TradeEntry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t74">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t74"><data value='MarketDepthSnapshot'>MarketDepthSnapshot</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t167">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t167"><data value='MarketDepthVisualization'>MarketDepthVisualization</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t179">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html#t179"><data value='MarketDepthDashboard'>MarketDepthDashboard</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="38 40">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_py.html">src\forex_bot\market_depth_visualizer\visualizer.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>106</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="31 106">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part2_py.html">src\forex_bot\market_depth_visualizer\visualizer_part2.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>176</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="27 176">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part3_py.html">src\forex_bot\market_depth_visualizer\visualizer_part3.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part3_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>174</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="27 174">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part4_py.html">src\forex_bot\market_depth_visualizer\visualizer_part4.py</a></td>
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part4_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="28 84">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc___init___py.html">src\forex_bot\market_hours\__init__.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_session_info_py.html#t59">src\forex_bot\market_hours\session_info.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc_session_info_py.html#t59"><data value='SessionConfig'>SessionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_session_info_py.html">src\forex_bot\market_hours\session_info.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc_session_info_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>154</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="33 154">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html#t8">src\forex_bot\market_hours\settings.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html#t8"><data value='SessionSettings'>SessionSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html#t19">src\forex_bot\market_hours\settings.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html#t19"><data value='Config'>SessionSettings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html">src\forex_bot\market_hours\settings.py</a></td>
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_metrics_py.html#t100">src\forex_bot\metrics.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_metrics_py.html#t100"><data value='MetricsRequestHandler'>MetricsRequestHandler</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_metrics_py.html">src\forex_bot\metrics.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>203</td>
                <td>203</td>
                <td>0</td>
                <td class="right" data-ratio="0 203">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53___init___py.html">src\forex_bot\metrics\__init__.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_backtester_metrics_py.html#t211">src\forex_bot\metrics\backtester_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_backtester_metrics_py.html#t211"><data value='BacktesterTracingMiddleware'>BacktesterTracingMiddleware</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_backtester_metrics_py.html">src\forex_bot\metrics\backtester_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_backtester_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_event_bus_metrics_py.html#t444">src\forex_bot\metrics\event_bus_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_event_bus_metrics_py.html#t444"><data value='EventBusMetricsMiddleware'>EventBusMetricsMiddleware</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_event_bus_metrics_py.html">src\forex_bot\metrics\event_bus_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_event_bus_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t16">src\forex_bot\metrics\metrics_config.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t16"><data value='PrometheusConfig'>PrometheusConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t52">src\forex_bot\metrics\metrics_config.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t52"><data value='OpenTelemetryConfig'>OpenTelemetryConfig</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t96">src\forex_bot\metrics\metrics_config.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t96"><data value='AlertingConfig'>AlertingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t125">src\forex_bot\metrics\metrics_config.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html#t125"><data value='MetricsConfig'>MetricsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html">src\forex_bot\metrics\metrics_config.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_mt5_metrics_py.html#t273">src\forex_bot\metrics\mt5_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_mt5_metrics_py.html#t273"><data value='MT5TracingMiddleware'>MT5TracingMiddleware</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_mt5_metrics_py.html">src\forex_bot\metrics\mt5_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_mt5_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_otel_tracing_py.html#t284">src\forex_bot\metrics\otel_tracing.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_otel_tracing_py.html#t284"><data value='TracingMiddleware'>TracingMiddleware</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_otel_tracing_py.html">src\forex_bot\metrics\otel_tracing.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_otel_tracing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>135</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="0 135">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_prometheus_metrics_py.html">src\forex_bot\metrics\prometheus_metrics.py</a></td>
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_prometheus_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>312</td>
                <td>312</td>
                <td>0</td>
                <td class="right" data-ratio="0 312">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8___init___py.html">src\forex_bot\metrics_dashboard\__init__.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_client_py.html#t35">src\forex_bot\metrics_dashboard\client.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_client_py.html#t35"><data value='MetricsDashboardClient'>MetricsDashboardClient</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_client_py.html">src\forex_bot\metrics_dashboard\client.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_py.html">src\forex_bot\metrics_dashboard\dashboard_generator.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="20 40">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part2_py.html">src\forex_bot\metrics_dashboard\dashboard_generator_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="18 62">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part3_py.html">src\forex_bot\metrics_dashboard\dashboard_generator_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part3_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="14 48">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_py.html">src\forex_bot\metrics_dashboard\metrics_calculator.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="21 110">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_part2_py.html">src\forex_bot\metrics_dashboard\metrics_calculator_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="15 69">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t38">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t38"><data value='TimeFrame'>TimeFrame</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t50">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t50"><data value='MetricCategory'>MetricCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t57">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t57"><data value='ChartType'>ChartType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t70">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t70"><data value='MetricValue'>MetricValue</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t85">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t85"><data value='MetricTimeSeries'>MetricTimeSeries</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t115">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t115"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t141">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t141"><data value='TradeMetrics'>TradeMetrics</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t165">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t165"><data value='MarketMetrics'>MarketMetrics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t194">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t194"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t216">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t216"><data value='ChartData'>ChartData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t227">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t227"><data value='DashboardLayout'>DashboardLayout</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t257">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html#t257"><data value='Dashboard'>Dashboard</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>155</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="143 155">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t26">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t26"><data value='MetricValue'>MetricValue</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t42">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t42"><data value='MetricTimeSeries'>MetricTimeSeries</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t73">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t73"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t102">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t102"><data value='TradeMetrics'>TradeMetrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t125">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t125"><data value='MarketMetrics'>MarketMetrics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t151">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html#t151"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t16">src\forex_bot\metrics_dashboard\models_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t16"><data value='ChartData'>ChartData</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t32">src\forex_bot\metrics_dashboard\models_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t32"><data value='DashboardLayout'>DashboardLayout</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t55">src\forex_bot\metrics_dashboard\models_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html#t55"><data value='Dashboard'>Dashboard</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html">src\forex_bot\metrics_dashboard\models_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_py.html">src\forex_bot\metrics_dashboard\visualizer.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="25 84">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part2_py.html">src\forex_bot\metrics_dashboard\visualizer_part2.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>106</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="21 106">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part3_py.html">src\forex_bot\metrics_dashboard\visualizer_part3.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part3_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>119</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="21 119">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part4_py.html">src\forex_bot\metrics_dashboard\visualizer_part4.py</a></td>
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part4_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="19 62">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b___init___py.html">src\forex_bot\ml_registry\__init__.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_dashboard_py.html">src\forex_bot\ml_registry\dashboard.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>217</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="0 217">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8___init___py.html">src\forex_bot\ml_registry\integrations\__init__.py</a></td>
                <td class="name left"><a href="z_5ea832e13b4aced8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8_regime_detector_integration_py.html">src\forex_bot\ml_registry\integrations\regime_detector_integration.py</a></td>
                <td class="name left"><a href="z_5ea832e13b4aced8_regime_detector_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8_volatility_forecaster_integration_py.html">src\forex_bot\ml_registry\integrations\volatility_forecaster_integration.py</a></td>
                <td class="name left"><a href="z_5ea832e13b4aced8_volatility_forecaster_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_mlflow_registry_py.html">src\forex_bot\ml_registry\mlflow_registry.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_mlflow_registry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>523</td>
                <td>523</td>
                <td>0</td>
                <td class="right" data-ratio="0 523">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_alerts_py.html#t25">src\forex_bot\ml_registry\model_alerts.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_alerts_py.html#t25"><data value='AlertManager'>AlertManager</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_alerts_py.html">src\forex_bot\ml_registry\model_alerts.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_alerts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t16">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t16"><data value='MLflowConfig'>MLflowConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t50">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t50"><data value='ModelRegistryConfig'>ModelRegistryConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t87">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t87"><data value='ModelEvaluationConfig'>ModelEvaluationConfig</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t130">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t130"><data value='ModelDeploymentConfig'>ModelDeploymentConfig</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t179">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html#t179"><data value='ModelConfig'>ModelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html">src\forex_bot\ml_registry\model_config.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_deployment_py.html">src\forex_bot\ml_registry\model_deployment.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_deployment_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_evaluation_py.html">src\forex_bot\ml_registry\model_evaluation.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_evaluation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>256</td>
                <td>256</td>
                <td>0</td>
                <td class="right" data-ratio="0 256">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_manager_py.html#t57">src\forex_bot\ml_registry\model_manager.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_manager_py.html#t57"><data value='ModelManager'>ModelManager</data></a></td>
                <td>197</td>
                <td>197</td>
                <td>0</td>
                <td class="right" data-ratio="0 197">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_manager_py.html">src\forex_bot\ml_registry\model_manager.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_monitoring_py.html#t34">src\forex_bot\ml_registry\model_monitoring.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_monitoring_py.html#t34"><data value='ModelMonitor'>ModelMonitor</data></a></td>
                <td>194</td>
                <td>194</td>
                <td>0</td>
                <td class="right" data-ratio="0 194">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_monitoring_py.html">src\forex_bot\ml_registry\model_monitoring.py</a></td>
                <td class="name left"><a href="z_6696b9cca54af58b_model_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_client_py.html">src\forex_bot\mt5_client.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>411</td>
                <td>379</td>
                <td>0</td>
                <td class="right" data-ratio="32 411">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_constants_py.html">src\forex_bot\mt5_constants.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_constants_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="103 103">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_event_producer_py.html#t36">src\forex_bot\mt5_event_producer.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_event_producer_py.html#t36"><data value='MT5EventProducer'>MT5EventProducer</data></a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_event_producer_py.html">src\forex_bot\mt5_event_producer.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_event_producer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db___init___py.html">src\forex_bot\multilingual_news\__init__.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_analyzer_py.html">src\forex_bot\multilingual_news\analyzer.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>292</td>
                <td>250</td>
                <td>0</td>
                <td class="right" data-ratio="42 292">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_client_py.html">src\forex_bot\multilingual_news\client.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>222</td>
                <td>193</td>
                <td>0</td>
                <td class="right" data-ratio="29 222">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t11">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t11"><data value='NewsSource'>NewsSource</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t24">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t24"><data value='NewsCategory'>NewsCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t37">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t37"><data value='NewsImpact'>NewsImpact</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t45">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t45"><data value='SentimentLabel'>SentimentLabel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t55">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t55"><data value='NewsArticle'>NewsArticle</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t124">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t124"><data value='SentimentAnalysis'>SentimentAnalysis</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t177">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t177"><data value='Entity'>Entity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t206">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t206"><data value='NewsSummary'>NewsSummary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t269">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html#t269"><data value='NewsContext'>NewsContext</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html">src\forex_bot\multilingual_news\models.py</a></td>
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>124</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="124 124">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a___init___py.html">src\forex_bot\news_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_entity_extractor_py.html">src\forex_bot\news_analyzer\entity_extractor.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_entity_extractor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_aggregator_py.html">src\forex_bot\news_analyzer\news_aggregator.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_aggregator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>173</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_fetcher_py.html">src\forex_bot\news_analyzer\news_fetcher.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_fetcher_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_impact_analyzer_py.html">src\forex_bot\news_analyzer\news_impact_analyzer.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_impact_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_processor_py.html">src\forex_bot\news_analyzer\news_processor.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_sentiment_analyzer_py.html">src\forex_bot\news_analyzer\sentiment_analyzer.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_sentiment_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_topic_modeler_py.html">src\forex_bot\news_analyzer\topic_modeler.py</a></td>
                <td class="name left"><a href="z_ba10c0e1a33d368a_topic_modeler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_news_service_py.html">src\forex_bot\news_service.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_news_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="10 88">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448___init___py.html">src\forex_bot\order_book\__init__.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_cache_py.html">src\forex_bot\order_book\cache.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="22 100">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_client_py.html#t106">src\forex_bot\order_book\client.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_client_py.html#t106"><data value='OrderBookFetcher'>OrderBookFetcher</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_client_py.html#t230">src\forex_bot\order_book\client.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_client_py.html#t230"><data value='OrderBookClient'>OrderBookClient</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_client_py.html">src\forex_bot\order_book\client.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="30 64">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_models_py.html#t23">src\forex_bot\order_book\models.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_models_py.html#t23"><data value='OrderBookEntry'>OrderBookEntry</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_models_py.html#t41">src\forex_bot\order_book\models.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_models_py.html#t41"><data value='OrderBook'>OrderBook</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_models_py.html">src\forex_bot\order_book\models.py</a></td>
                <td class="name left"><a href="z_07474e1e95032448_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>102</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="43 102">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30___init___py.html">src\forex_bot\order_flow_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="16 27">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_py.html">src\forex_bot\order_flow_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>136</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="20 136">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_part2_py.html">src\forex_bot\order_flow_analyzer\analyzer_part2.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="17 95">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_client_py.html#t41">src\forex_bot\order_flow_analyzer\client.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_client_py.html#t41"><data value='OrderFlowClient'>OrderFlowClient</data></a></td>
                <td>172</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_client_py.html">src\forex_bot\order_flow_analyzer\client.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t23">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t23"><data value='ImbalanceLevel'>ImbalanceLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t34">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t34"><data value='OrderFlowImbalance'>OrderFlowImbalance</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t52">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t52"><data value='LargeOrder'>LargeOrder</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t74">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t74"><data value='SupportResistanceLevel'>SupportResistanceLevel</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t96">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t96"><data value='OrderFlowSignal'>OrderFlowSignal</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t114">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html#t114"><data value='OrderFlowContext'>OrderFlowContext</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>152</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="74 152">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_py.html">src\forex_bot\order_flow_analyzer\visualizer.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="19 100">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_part2_py.html">src\forex_bot\order_flow_analyzer\visualizer_part2.py</a></td>
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_part2_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>131</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="19 131">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b7f97406eee4d94___init___py.html">src\forex_bot\pattern_recognizer\__init__.py</a></td>
                <td class="name left"><a href="z_6b7f97406eee4d94___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b7f97406eee4d94_recognizer_py.html">src\forex_bot\pattern_recognizer\recognizer.py</a></td>
                <td class="name left"><a href="z_6b7f97406eee4d94_recognizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>202</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="13 202">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071___init___py.html">src\forex_bot\performance_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html#t46">src\forex_bot\performance_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html#t46"><data value='AnalyzerConfig'>AnalyzerConfig</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html#t69">src\forex_bot\performance_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html#t69"><data value='PerformanceAnalyzer'>PerformanceAnalyzer</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html">src\forex_bot\performance_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="32 76">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_metrics_py.html">src\forex_bot\performance_analyzer\metrics.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>352</td>
                <td>321</td>
                <td>0</td>
                <td class="right" data-ratio="31 352">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_utils_py.html">src\forex_bot\performance_analyzer\utils.py</a></td>
                <td class="name left"><a href="z_e4f0260f9a7d8071_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>170</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="18 170">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_performance_tracker_py.html#t47">src\forex_bot\performance_tracker.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_performance_tracker_py.html#t47"><data value='PerformanceTracker'>PerformanceTracker</data></a></td>
                <td>199</td>
                <td>199</td>
                <td>0</td>
                <td class="right" data-ratio="0 199">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_performance_tracker_py.html">src\forex_bot\performance_tracker.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_performance_tracker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="32 46">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7979b9fa1f62b583___init___py.html">src\forex_bot\position_sizer\__init__.py</a></td>
                <td class="name left"><a href="z_7979b9fa1f62b583___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7979b9fa1f62b583_sizer_py.html">src\forex_bot\position_sizer\sizer.py</a></td>
                <td class="name left"><a href="z_7979b9fa1f62b583_sizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>212</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="24 212">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f___init___py.html">src\forex_bot\prompt_builder\__init__.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_correlation_py.html">src\forex_bot\prompt_builder\_format_correlation.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_correlation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="2 21">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_cot_py.html">src\forex_bot\prompt_builder\_format_cot.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_cot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="2 32">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_cvd_py.html">src\forex_bot\prompt_builder\_format_cvd.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_cvd_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="2 29">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_market_depth_py.html">src\forex_bot\prompt_builder\_format_market_depth.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_market_depth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_metrics_dashboard_py.html">src\forex_bot\prompt_builder\_format_metrics_dashboard.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_metrics_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="2 33">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_order_flow_py.html">src\forex_bot\prompt_builder\_format_order_flow.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_order_flow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="2 34">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_pmi_py.html">src\forex_bot\prompt_builder\_format_pmi.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_pmi_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="2 34">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_volatility_py.html">src\forex_bot\prompt_builder\_format_volatility.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_volatility_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="2 26">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_volume_profile_py.html">src\forex_bot\prompt_builder\_format_volume_profile.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_volume_profile_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="2 21">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_vwap_py.html">src\forex_bot\prompt_builder\_format_vwap.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f__format_vwap_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="2 30">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f_builder_py.html">src\forex_bot\prompt_builder\builder.py</a></td>
                <td class="name left"><a href="z_688d3285f67a444f_builder_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>177</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="27 177">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_qdrant_service_py.html">src\forex_bot\qdrant_service.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_qdrant_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>199</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="46 199">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f67347d77a2317d___init___py.html">src\forex_bot\regime_detector\__init__.py</a></td>
                <td class="name left"><a href="z_8f67347d77a2317d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f67347d77a2317d_hmm_model_py.html">src\forex_bot\regime_detector\hmm_model.py</a></td>
                <td class="name left"><a href="z_8f67347d77a2317d_hmm_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="29 143">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_secrets_manager_py.html">src\forex_bot\secrets_manager.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_secrets_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_834549f8a74d0f08___init___py.html">src\forex_bot\sentiment_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_834549f8a74d0f08___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_834549f8a74d0f08_analyzer_py.html">src\forex_bot\sentiment_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_834549f8a74d0f08_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>236</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="35 236">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_signal_generator_py.html#t81">src\forex_bot\signal_generator.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_signal_generator_py.html#t81"><data value='SignalGenerator'>SignalGenerator</data></a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_signal_generator_py.html">src\forex_bot\signal_generator.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_signal_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="57 75">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_system_monitor_py.html#t31">src\forex_bot\system_monitor.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_system_monitor_py.html#t31"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_system_monitor_py.html">src\forex_bot\system_monitor.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_system_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="38 56">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_trade_executor_py.html#t22">src\forex_bot\trade_executor.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_trade_executor_py.html#t22"><data value='TradeExecutor'>TradeExecutor</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_trade_executor_py.html">src\forex_bot\trade_executor.py</a></td>
                <td class="name left"><a href="z_21758df60ac2cdd3_trade_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="17 25">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97___init___py.html">src\forex_bot\trend_analyzer\__init__.py</a></td>
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97_analyzer_py.html">src\forex_bot\trend_analyzer\analyzer.py</a></td>
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="18 75">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ac753e7a34181b6___init___py.html">src\forex_bot\volatility_forecaster\__init__.py</a></td>
                <td class="name left"><a href="z_4ac753e7a34181b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ac753e7a34181b6_garch_model_py.html">src\forex_bot\volatility_forecaster\garch_model.py</a></td>
                <td class="name left"><a href="z_4ac753e7a34181b6_garch_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>111</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="16 111">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924___init___py.html">src\forex_bot\volatility_indices\__init__.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_analyzer_py.html">src\forex_bot\volatility_indices\analyzer.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>190</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="24 190">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_client_py.html">src\forex_bot\volatility_indices\client.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>177</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="30 177">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t13">src\forex_bot\volatility_indices\models.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t13"><data value='VolatilityData'>VolatilityData</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t40">src\forex_bot\volatility_indices\models.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t40"><data value='VolatilityTrend'>VolatilityTrend</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t119">src\forex_bot\volatility_indices\models.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html#t119"><data value='VolatilityRegime'>VolatilityRegime</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html">src\forex_bot\volatility_indices\models.py</a></td>
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="56 56">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b___init___py.html">src\forex_bot\volume_profile\__init__.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_calculator_py.html">src\forex_bot\volume_profile\calculator.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>158</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="12 158">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_client_py.html#t40">src\forex_bot\volume_profile\client.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_client_py.html#t40"><data value='VolumeProfileClient'>VolumeProfileClient</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_client_py.html">src\forex_bot\volume_profile\client.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html#t12">src\forex_bot\volume_profile\models.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html#t12"><data value='VolumeProfileResult'>VolumeProfileResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html#t57">src\forex_bot\volume_profile\models.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html#t57"><data value='VolumeZone'>VolumeZone</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html">src\forex_bot\volume_profile\models.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_visualizer_py.html">src\forex_bot\volume_profile\visualizer.py</a></td>
                <td class="name left"><a href="z_cd0688f78ad9707b_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="13 69">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314___init___py.html">src\forex_bot\vwap\__init__.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_calculator_py.html">src\forex_bot\vwap\calculator.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_calculator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>148</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="13 148">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_client_py.html#t30">src\forex_bot\vwap\client.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_client_py.html#t30"><data value='VWAPClient'>VWAPClient</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_client_py.html">src\forex_bot\vwap\client.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html#t13">src\forex_bot\vwap\models.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html#t13"><data value='VWAPResult'>VWAPResult</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html#t65">src\forex_bot\vwap\models.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html#t65"><data value='VWAPCrossover'>VWAPCrossover</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html">src\forex_bot\vwap\models.py</a></td>
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>19650</td>
                <td>16062</td>
                <td>0</td>
                <td class="right" data-ratio="3588 19650">18%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 20:02 -0500
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
