<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">22%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:11 -0500
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="check_all_modules_coverage_py.html">check_all_modules_coverage.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_final_coverage_py.html">check_final_coverage.py</a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_imports_py.html">check_imports.py</a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_individual_module_coverage_py.html">check_individual_module_coverage.py</a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_metrics_coverage_py.html">check_metrics_coverage.py</a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_module_coverage_py.html">check_module_coverage.py</a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_module_path_py.html">check_module_path.py</a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="check_sentiment_analyzer_coverage_py.html">check_sentiment_analyzer_coverage.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="conftest_py.html">conftest.py</a></td>
                <td>48</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="40 48">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="direct_coverage_test_py.html">direct_coverage_test.py</a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="direct_coverage_test_mocked_py.html">direct_coverage_test_mocked.py</a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exercise_models_py.html">exercise_models.py</a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_coverage_report_py.html">generate_coverage_report.py</a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="isolated_config_test_py.html">isolated_config_test.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="launcher_py.html">launcher.py</a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="print_module_content_py.html">print_module_content.py</a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_all_tests_no_cov_py.html">run_all_tests_no_cov.py</a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_combined_tests_py.html">run_combined_tests.py</a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_coverage_direct_py.html">run_coverage_direct.py</a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_coverage_for_modules_py.html">run_coverage_for_modules.py</a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_full_coverage_report_py.html">run_full_coverage_report.py</a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_gemini_client_tests_py.html">run_gemini_client_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_heikin_ashi_tests_py.html">run_heikin_ashi_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_individual_news_tests_py.html">run_individual_news_tests.py</a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_macro_analyzer_tests_py.html">run_macro_analyzer_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_macro_fetcher_tests_py.html">run_macro_fetcher_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_market_hours_tests_py.html">run_market_hours_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_metrics_tests_py.html">run_metrics_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_models_coverage_py.html">run_models_coverage.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_modular_coverage_py.html">run_modular_coverage.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_module_tests_py.html">run_module_tests.py</a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_multilingual_news_coverage_py.html">run_multilingual_news_coverage.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_multilingual_news_tests_py.html">run_multilingual_news_tests.py</a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_pattern_recognizer_tests_py.html">run_pattern_recognizer_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_position_sizer_tests_py.html">run_position_sizer_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_prompt_builder_tests_py.html">run_prompt_builder_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_pytest_py.html">run_pytest.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_pytest_coverage_py.html">run_pytest_coverage.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_sentiment_analyzer_tests_py.html">run_sentiment_analyzer_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_specific_module_tests_py.html">run_specific_module_tests.py</a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html">run_tests.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_trend_analyzer_tests_py.html">run_trend_analyzer_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_utils_tests_py.html">run_utils_tests.py</a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3___init___py.html">src\forex_bot\__init__.py</a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3___main___py.html">src\forex_bot\__main__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_analysis_constants_py.html">src\forex_bot\analysis_constants.py</a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1___init___py.html">src\forex_bot\backtester\__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_backtester_service_py.html">src\forex_bot\backtester\backtester_service.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_config_py.html">src\forex_bot\backtester\config.py</a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_order_simulator_py.html">src\forex_bot\backtester\order_simulator.py</a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b50040b513dcffe1_performance_tracker_py.html">src\forex_bot\backtester\performance_tracker.py</a></td>
                <td>232</td>
                <td>232</td>
                <td>0</td>
                <td class="right" data-ratio="0 232">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_orchestrator_py.html">src\forex_bot\bot_orchestrator.py</a></td>
                <td>823</td>
                <td>769</td>
                <td>0</td>
                <td class="right" data-ratio="54 823">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_bot_scheduler_py.html">src\forex_bot\bot_scheduler.py</a></td>
                <td>218</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="26 218">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_loader_py.html">src\forex_bot\config_loader.py</a></td>
                <td>234</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="203 234">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_config_settings_py.html">src\forex_bot\config_settings.py</a></td>
                <td>151</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="151 151">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d___init___py.html">src\forex_bot\correlation_matrix\__init__.py</a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_py.html">src\forex_bot\correlation_matrix\calculator.py</a></td>
                <td>69</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="22 69">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part2_py.html">src\forex_bot\correlation_matrix\calculator_part2.py</a></td>
                <td>71</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="15 71">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_calculator_part3_py.html">src\forex_bot\correlation_matrix\calculator_part3.py</a></td>
                <td>62</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="12 62">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_client_py.html">src\forex_bot\correlation_matrix\client.py</a></td>
                <td>97</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="24 97">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_py.html">src\forex_bot\correlation_matrix\models.py</a></td>
                <td>235</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="91 235">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_models_part2_py.html">src\forex_bot\correlation_matrix\models_part2.py</a></td>
                <td>31</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="10 31">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_py.html">src\forex_bot\correlation_matrix\visualizer.py</a></td>
                <td>95</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="27 95">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e4f74bb738659d_visualizer_part2_py.html">src\forex_bot\correlation_matrix\visualizer_part2.py</a></td>
                <td>89</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="21 89">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a___init___py.html">src\forex_bot\cot_reports\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_analyzer_py.html">src\forex_bot\cot_reports\analyzer.py</a></td>
                <td>93</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="17 93">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_client_py.html">src\forex_bot\cot_reports\client.py</a></td>
                <td>134</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="30 134">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c3d3dfe922b6ea2a_models_py.html">src\forex_bot\cot_reports\models.py</a></td>
                <td>78</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="51 78">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e___init___py.html">src\forex_bot\cvd\__init__.py</a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_calculator_py.html">src\forex_bot\cvd\calculator.py</a></td>
                <td>135</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="12 135">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_client_py.html">src\forex_bot\cvd\client.py</a></td>
                <td>126</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="21 126">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1cbbbd3bf875576e_models_py.html">src\forex_bot\cvd\models.py</a></td>
                <td>48</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="35 48">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357___init___py.html">src\forex_bot\event_bus\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_config_py.html">src\forex_bot\event_bus\config.py</a></td>
                <td>60</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="37 60">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_consumer_py.html">src\forex_bot\event_bus\consumer.py</a></td>
                <td>247</td>
                <td>193</td>
                <td>0</td>
                <td class="right" data-ratio="54 247">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_event_schemas_py.html">src\forex_bot\event_bus\event_schemas.py</a></td>
                <td>113</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="107 113">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8cfda85c8b792357_producer_py.html">src\forex_bot\event_bus\producer.py</a></td>
                <td>132</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="41 132">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_gemini_client_py.html">src\forex_bot\gemini_client.py</a></td>
                <td>121</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="23 121">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9___init___py.html">src\forex_bot\global_pmi\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_analyzer_py.html">src\forex_bot\global_pmi\analyzer.py</a></td>
                <td>156</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="13 156">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_client_py.html">src\forex_bot\global_pmi\client.py</a></td>
                <td>111</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="26 111">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_54e5a98f26b615b9_models_py.html">src\forex_bot\global_pmi\models.py</a></td>
                <td>81</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="59 81">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_health_endpoints_py.html">src\forex_bot\health_endpoints.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="0 225">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c578cea568b4ed4d___init___py.html">src\forex_bot\heikin_ashi\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c578cea568b4ed4d_calculator_py.html">src\forex_bot\heikin_ashi\calculator.py</a></td>
                <td>95</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="17 95">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_indicators_py.html">src\forex_bot\indicators.py</a></td>
                <td>62</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="7 62">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialization_py.html">src\forex_bot\initialization.py</a></td>
                <td>159</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="23 159">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_initialize_py.html">src\forex_bot\initialize.py</a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="0 154">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_launcher_py.html">src\forex_bot\launcher.py</a></td>
                <td>194</td>
                <td>194</td>
                <td>0</td>
                <td class="right" data-ratio="0 194">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_log_manager_py.html">src\forex_bot\log_manager.py</a></td>
                <td>230</td>
                <td>199</td>
                <td>0</td>
                <td class="right" data-ratio="31 230">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_log_uploader_py.html">src\forex_bot\log_uploader.py</a></td>
                <td>104</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="15 104">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01___init___py.html">src\forex_bot\macro_analyzer\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01_analyzer_py.html">src\forex_bot\macro_analyzer\analyzer.py</a></td>
                <td>134</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="22 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d2cad6892f133d01_fetcher_py.html">src\forex_bot\macro_analyzer\fetcher.py</a></td>
                <td>201</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="39 201">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc___init___py.html">src\forex_bot\market_depth_visualizer\__init__.py</a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_client_py.html">src\forex_bot\market_depth_visualizer\client.py</a></td>
                <td>80</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="23 80">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_py.html">src\forex_bot\market_depth_visualizer\models.py</a></td>
                <td>271</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="145 271">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_models_part2_py.html">src\forex_bot\market_depth_visualizer\models_part2.py</a></td>
                <td>123</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="38 123">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_py.html">src\forex_bot\market_depth_visualizer\visualizer.py</a></td>
                <td>106</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="31 106">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part2_py.html">src\forex_bot\market_depth_visualizer\visualizer_part2.py</a></td>
                <td>176</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="27 176">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part3_py.html">src\forex_bot\market_depth_visualizer\visualizer_part3.py</a></td>
                <td>174</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="27 174">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83f52e07fd9619cc_visualizer_part4_py.html">src\forex_bot\market_depth_visualizer\visualizer_part4.py</a></td>
                <td>84</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="28 84">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc___init___py.html">src\forex_bot\market_hours\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_session_info_py.html">src\forex_bot\market_hours\session_info.py</a></td>
                <td>154</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="33 154">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e313fad6449ccdbc_settings_py.html">src\forex_bot\market_hours\settings.py</a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_metrics_py.html">src\forex_bot\metrics.py</a></td>
                <td>213</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53___init___py.html">src\forex_bot\metrics\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_backtester_metrics_py.html">src\forex_bot\metrics\backtester_metrics.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_event_bus_metrics_py.html">src\forex_bot\metrics\event_bus_metrics.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="0 155">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_metrics_config_py.html">src\forex_bot\metrics\metrics_config.py</a></td>
                <td>52</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="38 52">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_mt5_metrics_py.html">src\forex_bot\metrics\mt5_metrics.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_otel_tracing_py.html">src\forex_bot\metrics\otel_tracing.py</a></td>
                <td>146</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="37 146">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b1ecb8b2ace5d53_prometheus_metrics_py.html">src\forex_bot\metrics\prometheus_metrics.py</a></td>
                <td>312</td>
                <td>273</td>
                <td>0</td>
                <td class="right" data-ratio="39 312">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8___init___py.html">src\forex_bot\metrics_dashboard\__init__.py</a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_client_py.html">src\forex_bot\metrics_dashboard\client.py</a></td>
                <td>92</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="19 92">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_py.html">src\forex_bot\metrics_dashboard\dashboard_generator.py</a></td>
                <td>40</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="20 40">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part2_py.html">src\forex_bot\metrics_dashboard\dashboard_generator_part2.py</a></td>
                <td>62</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="18 62">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_dashboard_generator_part3_py.html">src\forex_bot\metrics_dashboard\dashboard_generator_part3.py</a></td>
                <td>48</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="14 48">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_py.html">src\forex_bot\metrics_dashboard\metrics_calculator.py</a></td>
                <td>110</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="21 110">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_metrics_calculator_part2_py.html">src\forex_bot\metrics_dashboard\metrics_calculator_part2.py</a></td>
                <td>69</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="15 69">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_py.html">src\forex_bot\metrics_dashboard\models.py</a></td>
                <td>223</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="143 223">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part2_py.html">src\forex_bot\metrics_dashboard\models_part2.py</a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_models_part3_py.html">src\forex_bot\metrics_dashboard\models_part3.py</a></td>
                <td>52</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="13 52">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_py.html">src\forex_bot\metrics_dashboard\visualizer.py</a></td>
                <td>84</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="25 84">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part2_py.html">src\forex_bot\metrics_dashboard\visualizer_part2.py</a></td>
                <td>106</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="21 106">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part3_py.html">src\forex_bot\metrics_dashboard\visualizer_part3.py</a></td>
                <td>119</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="21 119">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1a17491f203aa3a8_visualizer_part4_py.html">src\forex_bot\metrics_dashboard\visualizer_part4.py</a></td>
                <td>62</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="19 62">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b___init___py.html">src\forex_bot\ml_registry\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_dashboard_py.html">src\forex_bot\ml_registry\dashboard.py</a></td>
                <td>217</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="0 217">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8___init___py.html">src\forex_bot\ml_registry\integrations\__init__.py</a></td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="1 3">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8_regime_detector_integration_py.html">src\forex_bot\ml_registry\integrations\regime_detector_integration.py</a></td>
                <td>71</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="8 71">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5ea832e13b4aced8_volatility_forecaster_integration_py.html">src\forex_bot\ml_registry\integrations\volatility_forecaster_integration.py</a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_mlflow_registry_py.html">src\forex_bot\ml_registry\mlflow_registry.py</a></td>
                <td>523</td>
                <td>460</td>
                <td>0</td>
                <td class="right" data-ratio="63 523">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_alerts_py.html">src\forex_bot\ml_registry\model_alerts.py</a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_config_py.html">src\forex_bot\ml_registry\model_config.py</a></td>
                <td>69</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="49 69">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_deployment_py.html">src\forex_bot\ml_registry\model_deployment.py</a></td>
                <td>46</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="18 46">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_evaluation_py.html">src\forex_bot\ml_registry\model_evaluation.py</a></td>
                <td>256</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="25 256">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_manager_py.html">src\forex_bot\ml_registry\model_manager.py</a></td>
                <td>235</td>
                <td>202</td>
                <td>0</td>
                <td class="right" data-ratio="33 235">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6696b9cca54af58b_model_monitoring_py.html">src\forex_bot\ml_registry\model_monitoring.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="0 225">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_910738de801f3d46_train_hmm_py.html">src\forex_bot\models\train_hmm.py</a></td>
                <td>192</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="25 192">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_client_py.html">src\forex_bot\mt5_client.py</a></td>
                <td>411</td>
                <td>379</td>
                <td>0</td>
                <td class="right" data-ratio="32 411">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_constants_py.html">src\forex_bot\mt5_constants.py</a></td>
                <td>103</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="103 103">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_mt5_event_producer_py.html">src\forex_bot\mt5_event_producer.py</a></td>
                <td>192</td>
                <td>182</td>
                <td>0</td>
                <td class="right" data-ratio="10 192">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db___init___py.html">src\forex_bot\multilingual_news\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_analyzer_py.html">src\forex_bot\multilingual_news\analyzer.py</a></td>
                <td>292</td>
                <td>250</td>
                <td>0</td>
                <td class="right" data-ratio="42 292">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_client_py.html">src\forex_bot\multilingual_news\client.py</a></td>
                <td>222</td>
                <td>193</td>
                <td>0</td>
                <td class="right" data-ratio="29 222">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1924e2f905e091db_models_py.html">src\forex_bot\multilingual_news\models.py</a></td>
                <td>174</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="124 174">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a___init___py.html">src\forex_bot\news_analyzer\__init__.py</a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_entity_extractor_py.html">src\forex_bot\news_analyzer\entity_extractor.py</a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_aggregator_py.html">src\forex_bot\news_analyzer\news_aggregator.py</a></td>
                <td>173</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_fetcher_py.html">src\forex_bot\news_analyzer\news_fetcher.py</a></td>
                <td>139</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="22 139">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_impact_analyzer_py.html">src\forex_bot\news_analyzer\news_impact_analyzer.py</a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_news_processor_py.html">src\forex_bot\news_analyzer\news_processor.py</a></td>
                <td>146</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="7 146">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_sentiment_analyzer_py.html">src\forex_bot\news_analyzer\sentiment_analyzer.py</a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba10c0e1a33d368a_topic_modeler_py.html">src\forex_bot\news_analyzer\topic_modeler.py</a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_news_service_py.html">src\forex_bot\news_service.py</a></td>
                <td>88</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="10 88">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448___init___py.html">src\forex_bot\order_book\__init__.py</a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_cache_py.html">src\forex_bot\order_book\cache.py</a></td>
                <td>100</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="22 100">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_client_py.html">src\forex_bot\order_book\client.py</a></td>
                <td>120</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="30 120">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07474e1e95032448_models_py.html">src\forex_bot\order_book\models.py</a></td>
                <td>142</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="43 142">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30___init___py.html">src\forex_bot\order_flow_analyzer\__init__.py</a></td>
                <td>27</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="16 27">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_py.html">src\forex_bot\order_flow_analyzer\analyzer.py</a></td>
                <td>136</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="20 136">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_analyzer_part2_py.html">src\forex_bot\order_flow_analyzer\analyzer_part2.py</a></td>
                <td>95</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="17 95">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_client_py.html">src\forex_bot\order_flow_analyzer\client.py</a></td>
                <td>203</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="31 203">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_models_py.html">src\forex_bot\order_flow_analyzer\models.py</a></td>
                <td>173</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="74 173">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_py.html">src\forex_bot\order_flow_analyzer\visualizer.py</a></td>
                <td>100</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="19 100">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8129e89f77e0ee30_visualizer_part2_py.html">src\forex_bot\order_flow_analyzer\visualizer_part2.py</a></td>
                <td>131</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="19 131">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b7f97406eee4d94___init___py.html">src\forex_bot\pattern_recognizer\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b7f97406eee4d94_recognizer_py.html">src\forex_bot\pattern_recognizer\recognizer.py</a></td>
                <td>202</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="13 202">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071___init___py.html">src\forex_bot\performance_analyzer\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_analyzer_py.html">src\forex_bot\performance_analyzer\analyzer.py</a></td>
                <td>225</td>
                <td>193</td>
                <td>0</td>
                <td class="right" data-ratio="32 225">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_metrics_py.html">src\forex_bot\performance_analyzer\metrics.py</a></td>
                <td>352</td>
                <td>321</td>
                <td>0</td>
                <td class="right" data-ratio="31 352">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e4f0260f9a7d8071_utils_py.html">src\forex_bot\performance_analyzer\utils.py</a></td>
                <td>170</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="18 170">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_performance_tracker_py.html">src\forex_bot\performance_tracker.py</a></td>
                <td>245</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="32 245">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7979b9fa1f62b583___init___py.html">src\forex_bot\position_sizer\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7979b9fa1f62b583_sizer_py.html">src\forex_bot\position_sizer\sizer.py</a></td>
                <td>212</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="24 212">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f___init___py.html">src\forex_bot\prompt_builder\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_correlation_py.html">src\forex_bot\prompt_builder\_format_correlation.py</a></td>
                <td>21</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="2 21">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_cot_py.html">src\forex_bot\prompt_builder\_format_cot.py</a></td>
                <td>32</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="2 32">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_cvd_py.html">src\forex_bot\prompt_builder\_format_cvd.py</a></td>
                <td>29</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="2 29">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_market_depth_py.html">src\forex_bot\prompt_builder\_format_market_depth.py</a></td>
                <td>24</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_metrics_dashboard_py.html">src\forex_bot\prompt_builder\_format_metrics_dashboard.py</a></td>
                <td>33</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="2 33">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_order_flow_py.html">src\forex_bot\prompt_builder\_format_order_flow.py</a></td>
                <td>34</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="2 34">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_pmi_py.html">src\forex_bot\prompt_builder\_format_pmi.py</a></td>
                <td>34</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="2 34">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_volatility_py.html">src\forex_bot\prompt_builder\_format_volatility.py</a></td>
                <td>26</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="2 26">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_volume_profile_py.html">src\forex_bot\prompt_builder\_format_volume_profile.py</a></td>
                <td>21</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="2 21">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f__format_vwap_py.html">src\forex_bot\prompt_builder\_format_vwap.py</a></td>
                <td>30</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="2 30">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_688d3285f67a444f_builder_py.html">src\forex_bot\prompt_builder\builder.py</a></td>
                <td>177</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="27 177">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_qdrant_service_py.html">src\forex_bot\qdrant_service.py</a></td>
                <td>199</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="46 199">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f67347d77a2317d___init___py.html">src\forex_bot\regime_detector\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f67347d77a2317d_hmm_model_py.html">src\forex_bot\regime_detector\hmm_model.py</a></td>
                <td>143</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="29 143">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_secrets_manager_py.html">src\forex_bot\secrets_manager.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_834549f8a74d0f08___init___py.html">src\forex_bot\sentiment_analyzer\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_834549f8a74d0f08_analyzer_py.html">src\forex_bot\sentiment_analyzer\analyzer.py</a></td>
                <td>236</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="35 236">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_signal_generator_py.html">src\forex_bot\signal_generator.py</a></td>
                <td>241</td>
                <td>184</td>
                <td>0</td>
                <td class="right" data-ratio="57 241">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_system_monitor_py.html">src\forex_bot\system_monitor.py</a></td>
                <td>206</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="38 206">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21758df60ac2cdd3_trade_executor_py.html">src\forex_bot\trade_executor.py</a></td>
                <td>82</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="17 82">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97___init___py.html">src\forex_bot\trend_analyzer\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fe2e3f5d4cdbfc97_analyzer_py.html">src\forex_bot\trend_analyzer\analyzer.py</a></td>
                <td>75</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="18 75">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ac753e7a34181b6___init___py.html">src\forex_bot\volatility_forecaster\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ac753e7a34181b6_garch_model_py.html">src\forex_bot\volatility_forecaster\garch_model.py</a></td>
                <td>111</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="16 111">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924___init___py.html">src\forex_bot\volatility_indices\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_analyzer_py.html">src\forex_bot\volatility_indices\analyzer.py</a></td>
                <td>190</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="24 190">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_client_py.html">src\forex_bot\volatility_indices\client.py</a></td>
                <td>177</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="30 177">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bca0834a3af36924_models_py.html">src\forex_bot\volatility_indices\models.py</a></td>
                <td>109</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="56 109">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b___init___py.html">src\forex_bot\volume_profile\__init__.py</a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_calculator_py.html">src\forex_bot\volume_profile\calculator.py</a></td>
                <td>158</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="12 158">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_client_py.html">src\forex_bot\volume_profile\client.py</a></td>
                <td>96</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="27 96">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_models_py.html">src\forex_bot\volume_profile\models.py</a></td>
                <td>37</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="33 37">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cd0688f78ad9707b_visualizer_py.html">src\forex_bot\volume_profile\visualizer.py</a></td>
                <td>69</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="13 69">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314___init___py.html">src\forex_bot\vwap\__init__.py</a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_calculator_py.html">src\forex_bot\vwap\calculator.py</a></td>
                <td>148</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="13 148">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_client_py.html">src\forex_bot\vwap\client.py</a></td>
                <td>160</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="21 160">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1c02a38ddd1d4314_models_py.html">src\forex_bot\vwap\models.py</a></td>
                <td>44</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="31 44">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="temp_config_py.html">temp_config.py</a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_coverage_py.html">test_coverage.py</a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_report_py.html">test_report.py</a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_32cd21b2522f1423___init___py.html">tests\aaa_framework\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_32cd21b2522f1423_base_test_py.html">tests\aaa_framework\base_test.py</a></td>
                <td>117</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="53 117">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_32cd21b2522f1423_pytest_plugin_py.html">tests\aaa_framework\pytest_plugin.py</a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_32cd21b2522f1423_reporting_py.html">tests\aaa_framework\reporting.py</a></td>
                <td>37</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="12 37">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6d1b4bcb76938033___init___py.html">tests\backtester\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6d1b4bcb76938033_test_backtester_service_py.html">tests\backtester\test_backtester_service.py</a></td>
                <td>160</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="5 160">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6d1b4bcb76938033_test_order_simulator_py.html">tests\backtester\test_order_simulator.py</a></td>
                <td>134</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="5 134">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html">tests\conftest.py</a></td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_decodetest_py.html">tests\decodetest.py</a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f___init___py.html">tests\event_bus\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_conftest_py.html">tests\event_bus\conftest.py</a></td>
                <td>56</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="33 56">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_config_py.html">tests\event_bus\test_config.py</a></td>
                <td>457</td>
                <td>359</td>
                <td>0</td>
                <td class="right" data-ratio="98 457">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_config_coverage_py.html">tests\event_bus\test_config_coverage.py</a></td>
                <td>157</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="40 157">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_config_direct_py.html">tests\event_bus\test_config_direct.py</a></td>
                <td>255</td>
                <td>229</td>
                <td>0</td>
                <td class="right" data-ratio="26 255">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_config_final_py.html">tests\event_bus\test_config_final.py</a></td>
                <td>172</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="4 172">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_config_isolated_py.html">tests\event_bus\test_config_isolated.py</a></td>
                <td>294</td>
                <td>283</td>
                <td>0</td>
                <td class="right" data-ratio="11 294">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_consumer_py.html">tests\event_bus\test_consumer.py</a></td>
                <td>197</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="54 197">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_event_schemas_py.html">tests\event_bus\test_event_schemas.py</a></td>
                <td>123</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="23 123">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5c97be05d9be59f_test_producer_py.html">tests\event_bus\test_producer.py</a></td>
                <td>190</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="45 190">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33b75668da6b6a11___init___py.html">tests\integration\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33b75668da6b6a11_test_event_bus_config_integration_py.html">tests\integration\test_event_bus_config_integration.py</a></td>
                <td>126</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="8 126">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_measure_coverage_py.html">tests\measure_coverage.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89bcb3827b927724___init___py.html">tests\metrics\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89bcb3827b927724_test_otel_tracing_py.html">tests\metrics\test_otel_tracing.py</a></td>
                <td>95</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="33 95">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89bcb3827b927724_test_prometheus_metrics_py.html">tests\metrics\test_prometheus_metrics.py</a></td>
                <td>92</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="36 92">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_mock_modules_py.html">tests\mock_modules.py</a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_temp_config_py.html">tests\temp_config.py</a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_aaa_pattern_example_py.html">tests\test_aaa_pattern_example.py</a></td>
                <td>91</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="30 91">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_aaa_sample_py.html">tests\test_aaa_sample.py</a></td>
                <td>66</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="34 66">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_py.html">tests\test_bot_orchestrator.py</a></td>
                <td>186</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="60 186">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_py.html">tests\test_bot_orchestrator_70_coverage.py</a></td>
                <td>178</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="50 178">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part2_py.html">tests\test_bot_orchestrator_70_coverage_part2.py</a></td>
                <td>131</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="39 131">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part3_py.html">tests\test_bot_orchestrator_70_coverage_part3.py</a></td>
                <td>323</td>
                <td>247</td>
                <td>0</td>
                <td class="right" data-ratio="76 323">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part4_py.html">tests\test_bot_orchestrator_70_coverage_part4.py</a></td>
                <td>382</td>
                <td>265</td>
                <td>0</td>
                <td class="right" data-ratio="117 382">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part5_py.html">tests\test_bot_orchestrator_70_coverage_part5.py</a></td>
                <td>439</td>
                <td>333</td>
                <td>0</td>
                <td class="right" data-ratio="106 439">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_80_coverage_py.html">tests\test_bot_orchestrator_80_coverage.py</a></td>
                <td>337</td>
                <td>234</td>
                <td>0</td>
                <td class="right" data-ratio="103 337">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_py.html">tests\test_bot_orchestrator_90_coverage.py</a></td>
                <td>317</td>
                <td>221</td>
                <td>0</td>
                <td class="right" data-ratio="96 317">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part1_py.html">tests\test_bot_orchestrator_90_coverage_part1.py</a></td>
                <td>99</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="43 99">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part2_py.html">tests\test_bot_orchestrator_90_coverage_part2.py</a></td>
                <td>105</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="43 105">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part3_py.html">tests\test_bot_orchestrator_90_coverage_part3.py</a></td>
                <td>121</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="42 121">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part4_py.html">tests\test_bot_orchestrator_90_coverage_part4.py</a></td>
                <td>150</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="53 150">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part5_py.html">tests\test_bot_orchestrator_90_coverage_part5.py</a></td>
                <td>163</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="71 163">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_py.html">tests\test_bot_orchestrator_check_closed_trades.py</a></td>
                <td>94</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="23 94">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_comprehensive_py.html">tests\test_bot_orchestrator_check_closed_trades_comprehensive.py</a></td>
                <td>239</td>
                <td>205</td>
                <td>0</td>
                <td class="right" data-ratio="34 239">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_edge_cases_py.html">tests\test_bot_orchestrator_check_closed_trades_edge_cases.py</a></td>
                <td>120</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="28 120">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_py.html">tests\test_bot_orchestrator_check_closed_trades_simple.py</a></td>
                <td>123</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="31 123">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_2_py.html">tests\test_bot_orchestrator_check_closed_trades_simple_2.py</a></td>
                <td>86</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="29 86">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_close_positions_py.html">tests\test_bot_orchestrator_close_positions.py</a></td>
                <td>110</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="21 110">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_close_positions_func_py.html">tests\test_bot_orchestrator_close_positions_func.py</a></td>
                <td>36</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="12 36">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_close_positions_simple_py.html">tests\test_bot_orchestrator_close_positions_simple.py</a></td>
                <td>41</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="15 41">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_close_positions_wrapper_py.html">tests\test_bot_orchestrator_close_positions_wrapper.py</a></td>
                <td>39</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="15 39">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_closed_trades_py.html">tests\test_bot_orchestrator_closed_trades.py</a></td>
                <td>103</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="20 103">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_combined_py.html">tests\test_bot_orchestrator_combined.py</a></td>
                <td>34</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="17 34">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_combined_coverage_py.html">tests\test_bot_orchestrator_combined_coverage.py</a></td>
                <td>32</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="18 32">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_comprehensive_py.html">tests\test_bot_orchestrator_comprehensive.py</a></td>
                <td>156</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="54 156">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_direct_py.html">tests\test_bot_orchestrator_direct.py</a></td>
                <td>41</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="17 41">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_edge_cases_py.html">tests\test_bot_orchestrator_edge_cases.py</a></td>
                <td>85</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="24 85">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_error_handling_py.html">tests\test_bot_orchestrator_error_handling.py</a></td>
                <td>158</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="71 158">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_functions_py.html">tests\test_bot_orchestrator_functions.py</a></td>
                <td>79</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="28 79">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_py.html">tests\test_bot_orchestrator_get_perf_summary.py</a></td>
                <td>66</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="28 66">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_simple_py.html">tests\test_bot_orchestrator_get_perf_summary_simple.py</a></td>
                <td>63</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="19 63">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_get_recent_performance_summary_py.html">tests\test_bot_orchestrator_get_recent_performance_summary.py</a></td>
                <td>75</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="22 75">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_helpers_py.html">tests\test_bot_orchestrator_helpers.py</a></td>
                <td>288</td>
                <td>221</td>
                <td>0</td>
                <td class="right" data-ratio="67 288">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_init_py.html">tests\test_bot_orchestrator_init.py</a></td>
                <td>112</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="31 112">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_isolated_py.html">tests\test_bot_orchestrator_isolated.py</a></td>
                <td>56</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="24 56">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_load_env_variables_py.html">tests\test_bot_orchestrator_load_env_variables.py</a></td>
                <td>121</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="21 121">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_load_perf_data_py.html">tests\test_bot_orchestrator_load_perf_data.py</a></td>
                <td>78</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="24 78">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_load_performance_data_simple_py.html">tests\test_bot_orchestrator_load_performance_data_simple.py</a></td>
                <td>64</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="26 64">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_main_py.html">tests\test_bot_orchestrator_main.py</a></td>
                <td>81</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="35 81">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_main_loop_py.html">tests\test_bot_orchestrator_main_loop.py</a></td>
                <td>170</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="61 170">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_mock_py.html">tests\test_bot_orchestrator_mock.py</a></td>
                <td>153</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="37 153">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_mocked_py.html">tests\test_bot_orchestrator_mocked.py</a></td>
                <td>68</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="19 68">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_optional_modules_py.html">tests\test_bot_orchestrator_optional_modules.py</a></td>
                <td>98</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="33 98">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_perf_summary_py.html">tests\test_bot_orchestrator_perf_summary.py</a></td>
                <td>70</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="26 70">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_performance_summary_py.html">tests\test_bot_orchestrator_performance_summary.py</a></td>
                <td>77</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="39 77">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_py.html">tests\test_bot_orchestrator_run_bot.py</a></td>
                <td>187</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="50 187">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_comprehensive_py.html">tests\test_bot_orchestrator_run_bot_comprehensive.py</a></td>
                <td>217</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="57 217">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_fixed_py.html">tests\test_bot_orchestrator_run_bot_fixed.py</a></td>
                <td>140</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="38 140">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_function_py.html">tests\test_bot_orchestrator_run_bot_function.py</a></td>
                <td>95</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="28 95">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_py.html">tests\test_bot_orchestrator_run_bot_once.py</a></td>
                <td>212</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="57 212">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_coverage_py.html">tests\test_bot_orchestrator_run_bot_once_coverage.py</a></td>
                <td>463</td>
                <td>356</td>
                <td>0</td>
                <td class="right" data-ratio="107 463">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_py.html">tests\test_bot_orchestrator_run_bot_once_edge_cases.py</a></td>
                <td>100</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="32 100">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_2_py.html">tests\test_bot_orchestrator_run_bot_once_edge_cases_2.py</a></td>
                <td>99</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="28 99">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_function_py.html">tests\test_bot_orchestrator_run_bot_once_function.py</a></td>
                <td>109</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="27 109">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_simple_py.html">tests\test_bot_orchestrator_run_bot_once_simple.py</a></td>
                <td>82</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="21 82">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_specific_py.html">tests\test_bot_orchestrator_run_bot_once_specific.py</a></td>
                <td>61</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="24 61">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_bot_simple_py.html">tests\test_bot_orchestrator_run_bot_simple.py</a></td>
                <td>128</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="32 128">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_run_once_py.html">tests\test_bot_orchestrator_run_once.py</a></td>
                <td>310</td>
                <td>232</td>
                <td>0</td>
                <td class="right" data-ratio="78 310">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_signal_execution_py.html">tests\test_bot_orchestrator_signal_execution.py</a></td>
                <td>171</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="51 171">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_signal_generation_py.html">tests\test_bot_orchestrator_signal_generation.py</a></td>
                <td>172</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="47 172">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_signals_py.html">tests\test_bot_orchestrator_signals.py</a></td>
                <td>299</td>
                <td>222</td>
                <td>0</td>
                <td class="right" data-ratio="77 299">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_simple_py.html">tests\test_bot_orchestrator_simple.py</a></td>
                <td>132</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="41 132">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_simple_70_py.html">tests\test_bot_orchestrator_simple_70.py</a></td>
                <td>244</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="67 244">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_simple_functions_py.html">tests\test_bot_orchestrator_simple_functions.py</a></td>
                <td>107</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="40 107">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_specific_functions_py.html">tests\test_bot_orchestrator_specific_functions.py</a></td>
                <td>115</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="41 115">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_trade_context_py.html">tests\test_bot_orchestrator_trade_context.py</a></td>
                <td>108</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="9 108">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_upload_log_py.html">tests\test_bot_orchestrator_upload_log.py</a></td>
                <td>44</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="14 44">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_upload_log_file_py.html">tests\test_bot_orchestrator_upload_log_file.py</a></td>
                <td>56</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="23 56">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_bot_orchestrator_upload_log_function_py.html">tests\test_bot_orchestrator_upload_log_function.py</a></td>
                <td>44</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="14 44">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_calculate_indicators_py.html">tests\test_calculate_indicators.py</a></td>
                <td>152</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="17 152">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_check_and_log_closed_trades_py.html">tests\test_check_and_log_closed_trades.py</a></td>
                <td>272</td>
                <td>266</td>
                <td>0</td>
                <td class="right" data-ratio="6 272">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_close_existing_positions_py.html">tests\test_close_existing_positions.py</a></td>
                <td>40</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="17 40">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_combined_analyzers_py.html">tests\test_combined_analyzers.py</a></td>
                <td>106</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="36 106">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_combined_coverage_py.html">tests\test_combined_coverage.py</a></td>
                <td>32</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="19 32">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_py.html">tests\test_config_loader.py</a></td>
                <td>166</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="40 166">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_100_coverage_py.html">tests\test_config_loader_100_coverage.py</a></td>
                <td>53</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="25 53">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_87_to_90_coverage_py.html">tests\test_config_loader_87_to_90_coverage.py</a></td>
                <td>87</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="49 87">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_90_coverage_py.html">tests\test_config_loader_90_coverage.py</a></td>
                <td>181</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="28 181">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_94_to_100_coverage_py.html">tests\test_config_loader_94_to_100_coverage.py</a></td>
                <td>73</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="15 73">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_95_percent_py.html">tests\test_config_loader_95_percent.py</a></td>
                <td>126</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="17 126">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_additional_py.html">tests\test_config_loader_additional.py</a></td>
                <td>95</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="26 95">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_batch3_90_coverage_py.html">tests\test_config_loader_batch3_90_coverage.py</a></td>
                <td>162</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="31 162">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_batch5_90_coverage_py.html">tests\test_config_loader_batch5_90_coverage.py</a></td>
                <td>221</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="33 221">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_batch6_90_coverage_py.html">tests\test_config_loader_batch6_90_coverage.py</a></td>
                <td>190</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="31 190">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_final_90_coverage_py.html">tests\test_config_loader_final_90_coverage.py</a></td>
                <td>243</td>
                <td>182</td>
                <td>0</td>
                <td class="right" data-ratio="61 243">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_final_coverage_py.html">tests\test_config_loader_final_coverage.py</a></td>
                <td>123</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="35 123">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_loader_simple_95_py.html">tests\test_config_loader_simple_95.py</a></td>
                <td>78</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="15 78">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_py.html">tests\test_correlation_matrix.py</a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506___init___py.html">tests\test_correlation_matrix\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_calculator_py.html">tests\test_correlation_matrix\test_calculator.py</a></td>
                <td>95</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="22 95">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_calculator_part2_py.html">tests\test_correlation_matrix\test_calculator_part2.py</a></td>
                <td>110</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="29 110">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_calculator_part3_py.html">tests\test_correlation_matrix\test_calculator_part3.py</a></td>
                <td>87</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="22 87">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_calculator_part3_copy_py.html">tests\test_correlation_matrix\test_calculator_part3_copy.py</a></td>
                <td>244</td>
                <td>193</td>
                <td>0</td>
                <td class="right" data-ratio="51 244">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_client_py.html">tests\test_correlation_matrix\test_client.py</a></td>
                <td>53</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="21 53">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_client_copy_py.html">tests\test_correlation_matrix\test_client_copy.py</a></td>
                <td>286</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="73 286">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_models_py.html">tests\test_correlation_matrix\test_models.py</a></td>
                <td>104</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="21 104">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_models_copy_py.html">tests\test_correlation_matrix\test_models_copy.py</a></td>
                <td>215</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="55 215">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_models_part2_py.html">tests\test_correlation_matrix\test_models_part2.py</a></td>
                <td>31</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="11 31">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_models_part2_copy_py.html">tests\test_correlation_matrix\test_models_part2_copy.py</a></td>
                <td>118</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="41 118">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_py.html">tests\test_correlation_matrix\test_visualizer.py</a></td>
                <td>132</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="61 132">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_py.html">tests\test_correlation_matrix\test_visualizer_part2.py</a></td>
                <td>130</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="51 130">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_copy_py.html">tests\test_correlation_matrix\test_visualizer_part2_copy.py</a></td>
                <td>313</td>
                <td>257</td>
                <td>0</td>
                <td class="right" data-ratio="56 313">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_direct_py.html">tests\test_correlation_matrix\test_visualizer_part2_direct.py</a></td>
                <td>207</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="106 207">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_functions_py.html">tests\test_correlation_matrix\test_visualizer_part2_functions.py</a></td>
                <td>99</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="41 99">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_isolated_py.html">tests\test_correlation_matrix\test_visualizer_part2_isolated.py</a></td>
                <td>313</td>
                <td>257</td>
                <td>0</td>
                <td class="right" data-ratio="56 313">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_mock_py.html">tests\test_correlation_matrix\test_visualizer_part2_mock.py</a></td>
                <td>57</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="16 57">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_mock_new_py.html">tests\test_correlation_matrix\test_visualizer_part2_mock_new.py</a></td>
                <td>145</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="58 145">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_part2_simple_py.html">tests\test_correlation_matrix\test_visualizer_part2_simple.py</a></td>
                <td>63</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="17 63">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_test_visualizer_simple_py.html">tests\test_correlation_matrix\test_visualizer_simple.py</a></td>
                <td>144</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="47 144">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0ebcc3a63e8c506_visualizer_part2_copy_py.html">tests\test_correlation_matrix\visualizer_part2_copy.py</a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_comprehensive_py.html">tests\test_correlation_matrix_comprehensive.py</a></td>
                <td>134</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="38 134">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_event_bus_integration_py.html">tests\test_correlation_matrix_event_bus_integration.py</a></td>
                <td>102</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="45 102">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_init_batch10_90_coverage_py.html">tests\test_correlation_matrix_init_batch10_90_coverage.py</a></td>
                <td>249</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="32 249">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_init_batch5_90_coverage_py.html">tests\test_correlation_matrix_init_batch5_90_coverage.py</a></td>
                <td>167</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="24 167">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_100_percent_py.html">tests\test_correlation_matrix_models_100_percent.py</a></td>
                <td>108</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="16 108">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_py.html">tests\test_correlation_matrix_models_60_percent.py</a></td>
                <td>103</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="27 103">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_corrected_py.html">tests\test_correlation_matrix_models_60_percent_corrected.py</a></td>
                <td>92</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="22 92">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_enhanced_py.html">tests\test_correlation_matrix_models_enhanced.py</a></td>
                <td>93</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="8 93">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_phase3_py.html">tests\test_correlation_matrix_models_phase3.py</a></td>
                <td>37</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="6 37">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_phase5_clean_py.html">tests\test_correlation_matrix_models_phase5_clean.py</a></td>
                <td>34</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="5 34">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_phase5_final_py.html">tests\test_correlation_matrix_models_phase5_final.py</a></td>
                <td>28</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="4 28">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_phase5m_py.html">tests\test_correlation_matrix_models_phase5m.py</a></td>
                <td>192</td>
                <td>169</td>
                <td>0</td>
                <td class="right" data-ratio="23 192">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_models_phase6a_simple_py.html">tests\test_correlation_matrix_models_phase6a_simple.py</a></td>
                <td>21</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="7 21">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_correlation_matrix_structure_py.html">tests\test_correlation_matrix_structure.py</a></td>
                <td>98</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="8 98">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac729262877445e___init___py.html">tests\test_cot_reports\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac729262877445e_test_analyzer_py.html">tests\test_cot_reports\test_analyzer.py</a></td>
                <td>93</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="23 93">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac729262877445e_test_client_py.html">tests\test_cot_reports\test_client.py</a></td>
                <td>103</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="29 103">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac729262877445e_test_models_py.html">tests\test_cot_reports\test_models.py</a></td>
                <td>57</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="7 57">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cot_reports_models_100_percent_py.html">tests\test_cot_reports_models_100_percent.py</a></td>
                <td>43</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="13 43">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cot_reports_models_batch9_90_coverage_py.html">tests\test_cot_reports_models_batch9_90_coverage.py</a></td>
                <td>189</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="30 189">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cot_reports_models_phase5_simple_py.html">tests\test_cot_reports_models_phase5_simple.py</a></td>
                <td>56</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="10 56">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ccb7ed9c01d1cfe0___init___py.html">tests\test_cvd\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ccb7ed9c01d1cfe0_test_calculator_py.html">tests\test_cvd\test_calculator.py</a></td>
                <td>92</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="20 92">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ccb7ed9c01d1cfe0_test_client_py.html">tests\test_cvd\test_client.py</a></td>
                <td>188</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="33 188">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ccb7ed9c01d1cfe0_test_models_py.html">tests\test_cvd\test_models.py</a></td>
                <td>69</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="10 69">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cvd_init_batch7_90_coverage_py.html">tests\test_cvd_init_batch7_90_coverage.py</a></td>
                <td>203</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="28 203">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cvd_models_100_percent_py.html">tests\test_cvd_models_100_percent.py</a></td>
                <td>40</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="9 40">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cvd_models_73_to_90_coverage_py.html">tests\test_cvd_models_73_to_90_coverage.py</a></td>
                <td>75</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="18 75">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cvd_models_batch4_90_coverage_py.html">tests\test_cvd_models_batch4_90_coverage.py</a></td>
                <td>163</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="29 163">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_cvd_models_phase5f_py.html">tests\test_cvd_models_phase5f.py</a></td>
                <td>117</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="14 117">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_direct_import_py.html">tests\test_direct_import.py</a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_enhanced_metrics_dashboard_py.html">tests\test_enhanced_metrics_dashboard.py</a></td>
                <td>230</td>
                <td>202</td>
                <td>0</td>
                <td class="right" data-ratio="28 230">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_analysis_consumer_py.html">tests\test_event_bus_analysis_consumer.py</a></td>
                <td>91</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="29 91">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_basic_py.html">tests\test_event_bus_basic.py</a></td>
                <td>57</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="14 57">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_comprehensive_py.html">tests\test_event_bus_comprehensive.py</a></td>
                <td>161</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="31 161">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_py.html">tests\test_event_bus_config.py</a></td>
                <td>166</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="35 166">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_90_percent_py.html">tests\test_event_bus_config_90_percent.py</a></td>
                <td>146</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="32 146">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_basic_py.html">tests\test_event_bus_config_basic.py</a></td>
                <td>191</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="37 191">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_coverage_py.html">tests\test_event_bus_config_coverage.py</a></td>
                <td>214</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="118 214">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_direct_py.html">tests\test_event_bus_config_direct.py</a></td>
                <td>190</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="138 190">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_focused_py.html">tests\test_event_bus_config_focused.py</a></td>
                <td>260</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="113 260">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_isolated_py.html">tests\test_event_bus_config_isolated.py</a></td>
                <td>278</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="148 278">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_minimal_py.html">tests\test_event_bus_config_minimal.py</a></td>
                <td>162</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="60 162">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_pytest_py.html">tests\test_event_bus_config_pytest.py</a></td>
                <td>237</td>
                <td>229</td>
                <td>0</td>
                <td class="right" data-ratio="8 237">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_real_py.html">tests\test_event_bus_config_real.py</a></td>
                <td>198</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="43 198">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_simple_py.html">tests\test_event_bus_config_simple.py</a></td>
                <td>137</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="17 137">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_standalone_py.html">tests\test_event_bus_config_standalone.py</a></td>
                <td>226</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="109 226">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_config_validators_py.html">tests\test_event_bus_config_validators.py</a></td>
                <td>232</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="37 232">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_consumer_py.html">tests\test_event_bus_consumer.py</a></td>
                <td>140</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="44 140">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_consumer_comprehensive_py.html">tests\test_event_bus_consumer_comprehensive.py</a></td>
                <td>167</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="50 167">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_consumer_detailed_py.html">tests\test_event_bus_consumer_detailed.py</a></td>
                <td>124</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="44 124">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_consumer_extended_py.html">tests\test_event_bus_consumer_extended.py</a></td>
                <td>106</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="41 106">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_integration_py.html">tests\test_event_bus_integration.py</a></td>
                <td>83</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="28 83">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_producer_py.html">tests\test_event_bus_producer.py</a></td>
                <td>116</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="37 116">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_producer_comprehensive_py.html">tests\test_event_bus_producer_comprehensive.py</a></td>
                <td>290</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="59 290">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_producer_extended_py.html">tests\test_event_bus_producer_extended.py</a></td>
                <td>142</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="33 142">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_pytest_py.html">tests\test_event_bus_pytest.py</a></td>
                <td>57</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="15 57">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_schemas_py.html">tests\test_event_bus_schemas.py</a></td>
                <td>192</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="33 192">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_specialized_consumers_py.html">tests\test_event_bus_specialized_consumers.py</a></td>
                <td>154</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="51 154">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_bus_structure_py.html">tests\test_event_bus_structure.py</a></td>
                <td>62</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="8 62">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_event_schemas_isolated_py.html">tests\test_event_schemas_isolated.py</a></td>
                <td>81</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="29 81">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_basic_py.html">tests\test_garch_model_basic.py</a></td>
                <td>129</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="33 129">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_copy_py.html">tests\test_garch_model_copy.py</a></td>
                <td>157</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="35 157">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_coverage_py.html">tests\test_garch_model_coverage.py</a></td>
                <td>184</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="68 184">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_direct_py.html">tests\test_garch_model_direct.py</a></td>
                <td>132</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="36 132">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_direct_copy_py.html">tests\test_garch_model_direct_copy.py</a></td>
                <td>166</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="45 166">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_isolated_py.html">tests\test_garch_model_isolated.py</a></td>
                <td>137</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="41 137">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_mocked_py.html">tests\test_garch_model_mocked.py</a></td>
                <td>109</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="39 109">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_simple_py.html">tests\test_garch_model_simple.py</a></td>
                <td>45</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="17 45">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_garch_model_standalone_py.html">tests\test_garch_model_standalone.py</a></td>
                <td>102</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="20 102">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_py.html">tests\test_gemini_client.py</a></td>
                <td>179</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="42 179">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_100_coverage_py.html">tests\test_gemini_client_100_coverage.py</a></td>
                <td>23</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="9 23">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_90_coverage_py.html">tests\test_gemini_client_90_coverage.py</a></td>
                <td>51</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="19 51">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_comprehensive_py.html">tests\test_gemini_client_comprehensive.py</a></td>
                <td>259</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="63 259">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_edge_cases_py.html">tests\test_gemini_client_edge_cases.py</a></td>
                <td>56</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="16 56">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_enhanced_py.html">tests\test_gemini_client_enhanced.py</a></td>
                <td>215</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="55 215">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_final_90_py.html">tests\test_gemini_client_final_90.py</a></td>
                <td>139</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="34 139">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_gemini_client_final_coverage_py.html">tests\test_gemini_client_final_coverage.py</a></td>
                <td>48</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="20 48">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_get_recent_performance_summary_py.html">tests\test_get_recent_performance_summary.py</a></td>
                <td>25</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="13 25">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db5bc7cc00fbdeae___init___py.html">tests\test_global_pmi\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db5bc7cc00fbdeae_test_analyzer_py.html">tests\test_global_pmi\test_analyzer.py</a></td>
                <td>77</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="20 77">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db5bc7cc00fbdeae_test_client_py.html">tests\test_global_pmi\test_client.py</a></td>
                <td>116</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="34 116">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_db5bc7cc00fbdeae_test_models_py.html">tests\test_global_pmi\test_models.py</a></td>
                <td>80</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="9 80">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_global_pmi_models_100_percent_py.html">tests\test_global_pmi_models_100_percent.py</a></td>
                <td>40</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="14 40">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_global_pmi_models_73_to_90_coverage_py.html">tests\test_global_pmi_models_73_to_90_coverage.py</a></td>
                <td>68</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="27 68">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_global_pmi_models_batch4_90_coverage_py.html">tests\test_global_pmi_models_batch4_90_coverage.py</a></td>
                <td>151</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="31 151">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_global_pmi_models_batch6_90_coverage_py.html">tests\test_global_pmi_models_batch6_90_coverage.py</a></td>
                <td>142</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="25 142">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_global_pmi_models_phase5g_py.html">tests\test_global_pmi_models_phase5g.py</a></td>
                <td>85</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="12 85">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_calculator_py.html">tests\test_heikin_ashi_calculator.py</a></td>
                <td>128</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="42 128">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_calculator_additional_py.html">tests\test_heikin_ashi_calculator_additional.py</a></td>
                <td>199</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="31 199">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_calculator_comprehensive_py.html">tests\test_heikin_ashi_calculator_comprehensive.py</a></td>
                <td>118</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="29 118">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_calculator_direct_py.html">tests\test_heikin_ashi_calculator_direct.py</a></td>
                <td>141</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="27 141">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_calculator_final_90_py.html">tests\test_heikin_ashi_calculator_final_90.py</a></td>
                <td>127</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="27 127">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_heikin_ashi_combined_py.html">tests\test_heikin_ashi_combined.py</a></td>
                <td>27</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="15 27">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_helpers_py.html">tests\test_helpers.py</a></td>
                <td>30</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="26 30">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_py.html">tests\test_indicators.py</a></td>
                <td>186</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="22 186">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_additional_py.html">tests\test_indicators_additional.py</a></td>
                <td>116</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="15 116">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_comprehensive_py.html">tests\test_indicators_comprehensive.py</a></td>
                <td>141</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="25 141">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_direct_py.html">tests\test_indicators_direct.py</a></td>
                <td>92</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="30 92">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_fixed_py.html">tests\test_indicators_fixed.py</a></td>
                <td>74</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="13 74">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_new_py.html">tests\test_indicators_new.py</a></td>
                <td>103</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="12 103">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_indicators_simple_new_py.html">tests\test_indicators_simple_new.py</a></td>
                <td>128</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="29 128">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_data_flow_py.html">tests\test_integration_data_flow.py</a></td>
                <td>137</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="31 137">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_end_to_end_py.html">tests\test_integration_end_to_end.py</a></td>
                <td>148</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="32 148">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_integration_signal_generation_py.html">tests\test_integration_signal_generation.py</a></td>
                <td>110</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="36 110">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_performance_data_py.html">tests\test_load_performance_data.py</a></td>
                <td>111</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="40 111">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_performance_data_simple_py.html">tests\test_load_performance_data_simple.py</a></td>
                <td>80</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="33 80">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_manager_py.html">tests\test_log_manager.py</a></td>
                <td>264</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="51 264">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_manager_isolated_py.html">tests\test_log_manager_isolated.py</a></td>
                <td>274</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="61 274">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_manager_simple_py.html">tests\test_log_manager_simple.py</a></td>
                <td>182</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="32 182">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_uploader_py.html">tests\test_log_uploader.py</a></td>
                <td>158</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="53 158">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_uploader_90_coverage_py.html">tests\test_log_uploader_90_coverage.py</a></td>
                <td>133</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="41 133">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_uploader_additional_py.html">tests\test_log_uploader_additional.py</a></td>
                <td>115</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="32 115">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_uploader_batch2_90_coverage_py.html">tests\test_log_uploader_batch2_90_coverage.py</a></td>
                <td>186</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="30 186">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_log_uploader_comprehensive_py.html">tests\test_log_uploader_comprehensive.py</a></td>
                <td>156</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="54 156">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_additional_py.html">tests\test_macro_analyzer_additional.py</a></td>
                <td>214</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="68 214">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_analyzer_py.html">tests\test_macro_analyzer_analyzer.py</a></td>
                <td>207</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="51 207">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_analyzer_comprehensive_py.html">tests\test_macro_analyzer_analyzer_comprehensive.py</a></td>
                <td>168</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="30 168">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_analyzer_final_90_py.html">tests\test_macro_analyzer_analyzer_final_90.py</a></td>
                <td>240</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="39 240">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_calendar_py.html">tests\test_macro_analyzer_calendar.py</a></td>
                <td>70</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="31 70">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_classify_py.html">tests\test_macro_analyzer_classify.py</a></td>
                <td>110</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="15 110">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_combined_py.html">tests\test_macro_analyzer_combined.py</a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_context_py.html">tests\test_macro_analyzer_context.py</a></td>
                <td>67</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="15 67">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_fetcher_py.html">tests\test_macro_analyzer_fetcher.py</a></td>
                <td>193</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="72 193">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_fetcher_comprehensive_py.html">tests\test_macro_analyzer_fetcher_comprehensive.py</a></td>
                <td>238</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="84 238">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_fetcher_final_90_py.html">tests\test_macro_analyzer_fetcher_final_90.py</a></td>
                <td>153</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="30 153">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_fetcher_simple_py.html">tests\test_macro_analyzer_fetcher_simple.py</a></td>
                <td>107</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="37 107">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_macro_analyzer_simple_py.html">tests\test_macro_analyzer_simple.py</a></td>
                <td>72</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="12 72">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_main_py.html">tests\test_main.py</a></td>
                <td>11</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="4 11">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_event_bus_integration_py.html">tests\test_market_depth_event_bus_integration.py</a></td>
                <td>103</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="43 103">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_models_py.html">tests\test_market_depth_models.py</a></td>
                <td>105</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="23 105">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_models_phase5z_py.html">tests\test_market_depth_models_phase5z.py</a></td>
                <td>29</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="9 29">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5aa_py.html">tests\test_market_depth_phase5aa.py</a></td>
                <td>29</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="10 29">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5bb_py.html">tests\test_market_depth_phase5bb.py</a></td>
                <td>38</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="9 38">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5cc_simple_py.html">tests\test_market_depth_phase5cc_simple.py</a></td>
                <td>22</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5dd_py.html">tests\test_market_depth_phase5dd.py</a></td>
                <td>52</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="7 52">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5ee_simple_py.html">tests\test_market_depth_phase5ee_simple.py</a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5ff_py.html">tests\test_market_depth_phase5ff.py</a></td>
                <td>64</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="6 64">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase5ff_simple_py.html">tests\test_market_depth_phase5ff_simple.py</a></td>
                <td>26</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="7 26">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase6b_clean_py.html">tests\test_market_depth_phase6b_clean.py</a></td>
                <td>17</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="6 17">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase6c_simple_py.html">tests\test_market_depth_phase6c_simple.py</a></td>
                <td>23</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="6 23">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase6f_surgical_py.html">tests\test_market_depth_phase6f_surgical.py</a></td>
                <td>83</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="6 83">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_phase6f_surgical_fixed_py.html">tests\test_market_depth_phase6f_surgical_fixed.py</a></td>
                <td>83</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="6 83">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_structure_py.html">tests\test_market_depth_structure.py</a></td>
                <td>86</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="8 86">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_py.html">tests\test_market_depth_visualizer.py</a></td>
                <td>72</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="32 72">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_220c46feb8f69054_test_client_py.html">tests\test_market_depth_visualizer\test_client.py</a></td>
                <td>287</td>
                <td>224</td>
                <td>0</td>
                <td class="right" data-ratio="63 287">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_220c46feb8f69054_test_models_py.html">tests\test_market_depth_visualizer\test_models.py</a></td>
                <td>131</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="23 131">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_comprehensive_py.html">tests\test_market_depth_visualizer_comprehensive.py</a></td>
                <td>145</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="31 145">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_init_batch8_90_coverage_py.html">tests\test_market_depth_visualizer_init_batch8_90_coverage.py</a></td>
                <td>249</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="32 249">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_100_percent_py.html">tests\test_market_depth_visualizer_models_100_percent.py</a></td>
                <td>23</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="9 23">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_enhanced_py.html">tests\test_market_depth_visualizer_models_enhanced.py</a></td>
                <td>42</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="7 42">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_phase2_py.html">tests\test_market_depth_visualizer_models_phase2.py</a></td>
                <td>34</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="6 34">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5_py.html">tests\test_market_depth_visualizer_models_phase5.py</a></td>
                <td>110</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="9 110">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5n_py.html">tests\test_market_depth_visualizer_models_phase5n.py</a></td>
                <td>264</td>
                <td>240</td>
                <td>0</td>
                <td class="right" data-ratio="24 264">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5z_final_py.html">tests\test_market_depth_visualizer_models_phase5z_final.py</a></td>
                <td>20</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="7 20">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_hours_session_info_py.html">tests\test_market_hours_session_info.py</a></td>
                <td>99</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="25 99">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_hours_session_info_additional_py.html">tests\test_market_hours_session_info_additional.py</a></td>
                <td>95</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="21 95">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_hours_session_info_final_100_py.html">tests\test_market_hours_session_info_final_100.py</a></td>
                <td>113</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="27 113">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_market_hours_settings_py.html">tests\test_market_hours_settings.py</a></td>
                <td>35</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="12 35">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metatrader_py.html">tests\test_metatrader.py</a></td>
                <td>2040</td>
                <td>2017</td>
                <td>0</td>
                <td class="right" data-ratio="23 2040">1%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metatrader_sleep_py.html">tests\test_metatrader_sleep.py</a></td>
                <td>69</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="9 69">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metatrader_uploads_py.html">tests\test_metatrader_uploads.py</a></td>
                <td>57</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="9 57">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_config_phase5p_py.html">tests\test_metrics_config_phase5p.py</a></td>
                <td>249</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="24 249">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_client_py.html">tests\test_metrics_dashboard_client.py</a></td>
                <td>26</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="4 26">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_enums_py.html">tests\test_metrics_dashboard_enums.py</a></td>
                <td>45</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="7 45">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_event_bus_integration_py.html">tests\test_metrics_dashboard_event_bus_integration.py</a></td>
                <td>102</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="26 102">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_init_batch9_90_coverage_py.html">tests\test_metrics_dashboard_init_batch9_90_coverage.py</a></td>
                <td>249</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="32 249">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_py.html">tests\test_metrics_dashboard_models.py</a></td>
                <td>245</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="47 245">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_100_percent_py.html">tests\test_metrics_dashboard_models_100_percent.py</a></td>
                <td>181</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="31 181">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_90_percent_py.html">tests\test_metrics_dashboard_models_90_percent.py</a></td>
                <td>138</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="23 138">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_clean_py.html">tests\test_metrics_dashboard_models_phase5_clean.py</a></td>
                <td>91</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="7 91">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_final_py.html">tests\test_metrics_dashboard_models_phase5_final.py</a></td>
                <td>81</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="7 81">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_90_coverage_py.html">tests\test_metrics_dashboard_models_phase5x_90_coverage.py</a></td>
                <td>30</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="7 30">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_simple_py.html">tests\test_metrics_dashboard_models_phase5x_simple.py</a></td>
                <td>30</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="10 30">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_models_phase5y_final_py.html">tests\test_metrics_dashboard_models_phase5y_final.py</a></td>
                <td>19</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="6 19">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_metrics_dashboard_structure_py.html">tests\test_metrics_dashboard_structure.py</a></td>
                <td>91</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="8 91">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7___init___py.html">tests\test_ml_registry\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_integrations_py.html">tests\test_ml_registry\test_integrations.py</a></td>
                <td>100</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="10 100">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_mlflow_registry_py.html">tests\test_ml_registry\test_mlflow_registry.py</a></td>
                <td>262</td>
                <td>227</td>
                <td>0</td>
                <td class="right" data-ratio="35 262">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_model_config_py.html">tests\test_ml_registry\test_model_config.py</a></td>
                <td>161</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="27 161">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_model_deployment_py.html">tests\test_ml_registry\test_model_deployment.py</a></td>
                <td>76</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="22 76">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_model_evaluation_py.html">tests\test_ml_registry\test_model_evaluation.py</a></td>
                <td>265</td>
                <td>234</td>
                <td>0</td>
                <td class="right" data-ratio="31 265">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8d9fd453d1a7f5f7_test_model_manager_py.html">tests\test_ml_registry\test_model_manager.py</a></td>
                <td>146</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="28 146">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_ml_registry_model_config_phase5p_py.html">tests\test_ml_registry_model_config_phase5p.py</a></td>
                <td>326</td>
                <td>297</td>
                <td>0</td>
                <td class="right" data-ratio="29 326">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_models_train_hmm_py.html">tests\test_models_train_hmm.py</a></td>
                <td>97</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="37 97">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_py.html">tests\test_mt5_client.py</a></td>
                <td>241</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="62 241">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_90_coverage_py.html">tests\test_mt5_client_90_coverage.py</a></td>
                <td>240</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="50 240">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_90_plus_py.html">tests\test_mt5_client_90_plus.py</a></td>
                <td>117</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="45 117">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_additional_py.html">tests\test_mt5_client_additional.py</a></td>
                <td>110</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="40 110">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_comprehensive_py.html">tests\test_mt5_client_comprehensive.py</a></td>
                <td>498</td>
                <td>396</td>
                <td>0</td>
                <td class="right" data-ratio="102 498">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_coverage_boost_py.html">tests\test_mt5_client_coverage_boost.py</a></td>
                <td>99</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="32 99">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_edge_cases_py.html">tests\test_mt5_client_edge_cases.py</a></td>
                <td>97</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="23 97">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_event_bus_py.html">tests\test_mt5_client_event_bus.py</a></td>
                <td>160</td>
                <td>142</td>
                <td>0</td>
                <td class="right" data-ratio="18 160">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_py.html">tests\test_mt5_client_final.py</a></td>
                <td>268</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="54 268">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_py.html">tests\test_mt5_client_final_90.py</a></td>
                <td>80</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="22 80">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_complete_py.html">tests\test_mt5_client_final_90_complete.py</a></td>
                <td>64</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="15 64">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_coverage_py.html">tests\test_mt5_client_final_90_coverage.py</a></td>
                <td>57</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="24 57">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_plus_py.html">tests\test_mt5_client_final_90_plus.py</a></td>
                <td>83</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="19 83">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_py.html">tests\test_mt5_client_final_90_plus_plus.py</a></td>
                <td>57</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="16 57">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_plus_py.html">tests\test_mt5_client_final_90_plus_plus_plus.py</a></td>
                <td>86</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="29 86">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_coverage_py.html">tests\test_mt5_client_final_coverage.py</a></td>
                <td>138</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="36 138">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_final_coverage_complete_py.html">tests\test_mt5_client_final_coverage_complete.py</a></td>
                <td>76</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="25 76">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_integration_py.html">tests\test_mt5_client_integration.py</a></td>
                <td>94</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="21 94">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_client_remaining_py.html">tests\test_mt5_client_remaining.py</a></td>
                <td>230</td>
                <td>183</td>
                <td>0</td>
                <td class="right" data-ratio="47 230">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_mt5_event_producer_py.html">tests\test_mt5_event_producer.py</a></td>
                <td>173</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="6 173">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_py.html">tests\test_multilingual_news.py</a></td>
                <td>113</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="22 113">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_90_coverage_py.html">tests\test_multilingual_news_90_coverage.py</a></td>
                <td>140</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="28 140">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_analyzer_90_coverage_py.html">tests\test_multilingual_news_analyzer_90_coverage.py</a></td>
                <td>183</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="36 183">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_analyzer_copy_py.html">tests\test_multilingual_news_analyzer_copy.py</a></td>
                <td>428</td>
                <td>352</td>
                <td>0</td>
                <td class="right" data-ratio="76 428">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_client_90_coverage_py.html">tests\test_multilingual_news_client_90_coverage.py</a></td>
                <td>190</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="45 190">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_client_copy_py.html">tests\test_multilingual_news_client_copy.py</a></td>
                <td>270</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="53 270">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_comprehensive_py.html">tests\test_multilingual_news_comprehensive.py</a></td>
                <td>186</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="45 186">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_direct_py.html">tests\test_multilingual_news_direct.py</a></td>
                <td>136</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="11 136">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_py.html">tests\test_multilingual_news_models.py</a></td>
                <td>187</td>
                <td>157</td>
                <td>0</td>
                <td class="right" data-ratio="30 187">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_90_coverage_py.html">tests\test_multilingual_news_models_90_coverage.py</a></td>
                <td>121</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="22 121">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_95_percent_py.html">tests\test_multilingual_news_models_95_percent.py</a></td>
                <td>93</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="15 93">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_comprehensive_py.html">tests\test_multilingual_news_models_comprehensive.py</a></td>
                <td>90</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="21 90">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_mock_py.html">tests\test_multilingual_news_models_mock.py</a></td>
                <td>356</td>
                <td>262</td>
                <td>0</td>
                <td class="right" data-ratio="94 356">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_models_phase5k_py.html">tests\test_multilingual_news_models_phase5k.py</a></td>
                <td>198</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="23 198">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_multilingual_news_simple_py.html">tests\test_multilingual_news_simple.py</a></td>
                <td>23</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="12 23">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_py.html">tests\test_news_analyzer.py</a></td>
                <td>115</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="8 115">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_90_coverage_py.html">tests\test_news_analyzer_90_coverage.py</a></td>
                <td>174</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="10 174">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_90_coverage_part2_py.html">tests\test_news_analyzer_90_coverage_part2.py</a></td>
                <td>154</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="10 154">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_direct_py.html">tests\test_news_analyzer_direct.py</a></td>
                <td>118</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="10 118">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_isolated_py.html">tests\test_news_analyzer_isolated.py</a></td>
                <td>27</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="6 27">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_simple_py.html">tests\test_news_analyzer_simple.py</a></td>
                <td>62</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="7 62">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_analyzer_standalone_py.html">tests\test_news_analyzer_standalone.py</a></td>
                <td>118</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="15 118">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_service_py.html">tests\test_news_service.py</a></td>
                <td>113</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="30 113">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_news_service_comprehensive_py.html">tests\test_news_service_comprehensive.py</a></td>
                <td>140</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="37 140">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_book_py.html">tests\test_order_book.py</a></td>
                <td>120</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="28 120">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_book_init_batch7_90_coverage_py.html">tests\test_order_book_init_batch7_90_coverage.py</a></td>
                <td>228</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="30 228">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_book_models_100_percent_py.html">tests\test_order_book_models_100_percent.py</a></td>
                <td>89</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="10 89">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_book_models_100_percent_fixed_py.html">tests\test_order_book_models_100_percent_fixed.py</a></td>
                <td>27</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="6 27">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_book_models_phase5o_py.html">tests\test_order_book_models_phase5o.py</a></td>
                <td>195</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="24 195">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_py.html">tests\test_order_flow_analyzer.py</a></td>
                <td>78</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="23 78">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_comprehensive_py.html">tests\test_order_flow_analyzer_comprehensive.py</a></td>
                <td>128</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="28 128">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_init_batch9_90_coverage_py.html">tests\test_order_flow_analyzer_init_batch9_90_coverage.py</a></td>
                <td>249</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="32 249">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_mock_py.html">tests\test_order_flow_analyzer_mock.py</a></td>
                <td>39</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="16 39">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_100_percent_py.html">tests\test_order_flow_analyzer_models_100_percent.py</a></td>
                <td>17</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="6 17">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_70_percent_py.html">tests\test_order_flow_analyzer_models_70_percent.py</a></td>
                <td>87</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="24 87">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_enhanced_py.html">tests\test_order_flow_analyzer_models_enhanced.py</a></td>
                <td>69</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="7 69">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_phase4_py.html">tests\test_order_flow_analyzer_models_phase4.py</a></td>
                <td>10</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="4 10">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5_clean_py.html">tests\test_order_flow_analyzer_models_phase5_clean.py</a></td>
                <td>35</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="4 35">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5l_py.html">tests\test_order_flow_analyzer_models_phase5l.py</a></td>
                <td>163</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="17 163">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_client_py.html">tests\test_order_flow_client.py</a></td>
                <td>75</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="17 75">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_event_bus_integration_py.html">tests\test_order_flow_event_bus_integration.py</a></td>
                <td>70</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="30 70">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_models_py.html">tests\test_order_flow_models.py</a></td>
                <td>63</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="11 63">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_order_flow_models_simple_py.html">tests\test_order_flow_models_simple.py</a></td>
                <td>24</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="7 24">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_90_coverage_py.html">tests\test_pattern_recognizer_90_coverage.py</a></td>
                <td>73</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="25 73">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_additional_py.html">tests\test_pattern_recognizer_additional.py</a></td>
                <td>208</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="29 208">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_config_py.html">tests\test_pattern_recognizer_config.py</a></td>
                <td>36</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="11 36">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_coverage_py.html">tests\test_pattern_recognizer_coverage.py</a></td>
                <td>77</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="19 77">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_direct_py.html">tests\test_pattern_recognizer_direct.py</a></td>
                <td>167</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="21 167">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_edge_cases_py.html">tests\test_pattern_recognizer_edge_cases.py</a></td>
                <td>160</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="23 160">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_final_py.html">tests\test_pattern_recognizer_final.py</a></td>
                <td>164</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="30 164">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_final_coverage_py.html">tests\test_pattern_recognizer_final_coverage.py</a></td>
                <td>108</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="26 108">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_recognizer_py.html">tests\test_pattern_recognizer_recognizer.py</a></td>
                <td>341</td>
                <td>289</td>
                <td>0</td>
                <td class="right" data-ratio="52 341">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_pattern_recognizer_uncovered_py.html">tests\test_pattern_recognizer_uncovered.py</a></td>
                <td>121</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="18 121">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_analyzer_py.html">tests\test_performance_analyzer_analyzer.py</a></td>
                <td>102</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="19 102">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_analyzer_90_coverage_py.html">tests\test_performance_analyzer_analyzer_90_coverage.py</a></td>
                <td>140</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="44 140">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_analyzer_comprehensive_py.html">tests\test_performance_analyzer_analyzer_comprehensive.py</a></td>
                <td>150</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="36 150">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_analyzer_final_90_py.html">tests\test_performance_analyzer_analyzer_final_90.py</a></td>
                <td>88</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="26 88">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_benchmark_metrics_py.html">tests\test_performance_analyzer_benchmark_metrics.py</a></td>
                <td>70</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="20 70">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_combined_py.html">tests\test_performance_analyzer_combined.py</a></td>
                <td>24</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="17 24">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_get_summary_py.html">tests\test_performance_analyzer_get_summary.py</a></td>
                <td>95</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="37 95">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_py.html">tests\test_performance_analyzer_metrics.py</a></td>
                <td>300</td>
                <td>247</td>
                <td>0</td>
                <td class="right" data-ratio="53 300">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_py.html">tests\test_performance_analyzer_metrics_90_coverage.py</a></td>
                <td>330</td>
                <td>221</td>
                <td>0</td>
                <td class="right" data-ratio="109 330">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_part2_py.html">tests\test_performance_analyzer_metrics_90_coverage_part2.py</a></td>
                <td>105</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="41 105">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_py.html">tests\test_performance_analyzer_metrics_final_90.py</a></td>
                <td>89</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="26 89">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_part2_py.html">tests\test_performance_analyzer_metrics_final_90_part2.py</a></td>
                <td>77</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="22 77">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_final_coverage_py.html">tests\test_performance_analyzer_metrics_final_coverage.py</a></td>
                <td>77</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="27 77">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_metrics_simple_py.html">tests\test_performance_analyzer_metrics_simple.py</a></td>
                <td>68</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="22 68">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_recovery_metrics_py.html">tests\test_performance_analyzer_recovery_metrics.py</a></td>
                <td>49</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="10 49">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_summary_py.html">tests\test_performance_analyzer_summary.py</a></td>
                <td>115</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="33 115">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_utils_py.html">tests\test_performance_analyzer_utils.py</a></td>
                <td>178</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="38 178">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_utils_90_coverage_py.html">tests\test_performance_analyzer_utils_90_coverage.py</a></td>
                <td>74</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="35 74">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_utils_final_90_py.html">tests\test_performance_analyzer_utils_final_90.py</a></td>
                <td>78</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="36 78">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_utils_final_coverage_py.html">tests\test_performance_analyzer_utils_final_coverage.py</a></td>
                <td>84</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="35 84">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_analyzer_utils_final_simple_py.html">tests\test_performance_analyzer_utils_final_simple.py</a></td>
                <td>91</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="39 91">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_tracker_py.html">tests\test_performance_tracker.py</a></td>
                <td>1225</td>
                <td>1123</td>
                <td>0</td>
                <td class="right" data-ratio="102 1225">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_tracker_new_py.html">tests\test_performance_tracker_new.py</a></td>
                <td>950</td>
                <td>858</td>
                <td>0</td>
                <td class="right" data-ratio="92 950">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_tracker_simple_py.html">tests\test_performance_tracker_simple.py</a></td>
                <td>167</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="48 167">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_phase3_integration_py.html">tests\test_phase3_integration.py</a></td>
                <td>87</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="20 87">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_additional_py.html">tests\test_position_sizer_additional.py</a></td>
                <td>66</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="19 66">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_combined_py.html">tests\test_position_sizer_combined.py</a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_comprehensive_py.html">tests\test_position_sizer_comprehensive.py</a></td>
                <td>303</td>
                <td>242</td>
                <td>0</td>
                <td class="right" data-ratio="61 303">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_final_90_coverage_py.html">tests\test_position_sizer_final_90_coverage.py</a></td>
                <td>130</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="27 130">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_sizer_py.html">tests\test_position_sizer_sizer.py</a></td>
                <td>226</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="30 226">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_sizer_90_coverage_py.html">tests\test_position_sizer_sizer_90_coverage.py</a></td>
                <td>147</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="24 147">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_sizer_90_plus_py.html">tests\test_position_sizer_sizer_90_plus.py</a></td>
                <td>169</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="46 169">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_position_sizer_sizer_final_py.html">tests\test_position_sizer_sizer_final.py</a></td>
                <td>114</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="25 114">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_build_prompt_py.html">tests\test_prompt_builder_build_prompt.py</a></td>
                <td>108</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="15 108">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_builder_py.html">tests\test_prompt_builder_builder.py</a></td>
                <td>40</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="13 40">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_builder_additional_py.html">tests\test_prompt_builder_builder_additional.py</a></td>
                <td>130</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="29 130">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_builder_final_90_py.html">tests\test_prompt_builder_builder_final_90.py</a></td>
                <td>127</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="45 127">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_format_functions_py.html">tests\test_prompt_builder_format_functions.py</a></td>
                <td>133</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="43 133">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_prompt_builder_generate_forex_prompt_py.html">tests\test_prompt_builder_generate_forex_prompt.py</a></td>
                <td>80</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="24 80">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_py.html">tests\test_qdrant_service.py</a></td>
                <td>260</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="104 260">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_70_coverage_py.html">tests\test_qdrant_service_70_coverage.py</a></td>
                <td>151</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="30 151">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_additional_py.html">tests\test_qdrant_service_additional.py</a></td>
                <td>117</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="6 117">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_advanced_py.html">tests\test_qdrant_service_advanced.py</a></td>
                <td>138</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="25 138">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_batch_py.html">tests\test_qdrant_service_batch.py</a></td>
                <td>81</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="5 81">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_comprehensive_py.html">tests\test_qdrant_service_comprehensive.py</a></td>
                <td>182</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="7 182">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_edge_cases_py.html">tests\test_qdrant_service_edge_cases.py</a></td>
                <td>128</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="8 128">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_init_py.html">tests\test_qdrant_service_init.py</a></td>
                <td>137</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="29 137">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_qdrant_service_simple_py.html">tests\test_qdrant_service_simple.py</a></td>
                <td>31</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="18 31">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_regime_detector_hmm_model_py.html">tests\test_regime_detector_hmm_model.py</a></td>
                <td>224</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="75 224">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_regime_detector_hmm_model_90_coverage_py.html">tests\test_regime_detector_hmm_model_90_coverage.py</a></td>
                <td>101</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="35 101">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_regime_detector_hmm_model_comprehensive_py.html">tests\test_regime_detector_hmm_model_comprehensive.py</a></td>
                <td>157</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="48 157">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_run_bot_py.html">tests\test_run_bot.py</a></td>
                <td>251</td>
                <td>211</td>
                <td>0</td>
                <td class="right" data-ratio="40 251">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_run_bot_comprehensive_py.html">tests\test_run_bot_comprehensive.py</a></td>
                <td>209</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="89 209">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_run_bot_once_signals_py.html">tests\test_run_bot_once_signals.py</a></td>
                <td>35</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="15 35">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_90_coverage_py.html">tests\test_sentiment_analyzer_90_coverage.py</a></td>
                <td>138</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="54 138">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_additional_py.html">tests\test_sentiment_analyzer_additional.py</a></td>
                <td>163</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="62 163">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_py.html">tests\test_sentiment_analyzer_analyzer.py</a></td>
                <td>216</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="82 216">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_90_coverage_py.html">tests\test_sentiment_analyzer_analyzer_90_coverage.py</a></td>
                <td>193</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="78 193">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_full_py.html">tests\test_sentiment_analyzer_analyzer_full.py</a></td>
                <td>97</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="37 97">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_combined_py.html">tests\test_sentiment_analyzer_combined.py</a></td>
                <td>29</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="15 29">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_comprehensive_py.html">tests\test_sentiment_analyzer_comprehensive.py</a></td>
                <td>237</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="72 237">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_direct_py.html">tests\test_sentiment_analyzer_direct.py</a></td>
                <td>157</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="61 157">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_final_90_coverage_py.html">tests\test_sentiment_analyzer_final_90_coverage.py</a></td>
                <td>110</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="49 110">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_py.html">tests\test_sentiment_analyzer_final_coverage.py</a></td>
                <td>266</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="105 266">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_90_py.html">tests\test_sentiment_analyzer_final_coverage_90.py</a></td>
                <td>129</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="37 129">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_py.html">tests\test_signal_generator.py</a></td>
                <td>163</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="42 163">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_90_percent_py.html">tests\test_signal_generator_90_percent.py</a></td>
                <td>222</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="26 222">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_additional_py.html">tests\test_signal_generator_additional.py</a></td>
                <td>234</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="61 234">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_batch2_90_coverage_py.html">tests\test_signal_generator_batch2_90_coverage.py</a></td>
                <td>151</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="23 151">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_batch7_90_coverage_py.html">tests\test_signal_generator_batch7_90_coverage.py</a></td>
                <td>236</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="41 236">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_complete_py.html">tests\test_signal_generator_complete.py</a></td>
                <td>288</td>
                <td>242</td>
                <td>0</td>
                <td class="right" data-ratio="46 288">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_coverage_py.html">tests\test_signal_generator_coverage.py</a></td>
                <td>231</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="58 231">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_final_py.html">tests\test_signal_generator_final.py</a></td>
                <td>165</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="26 165">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_final_coverage_py.html">tests\test_signal_generator_final_coverage.py</a></td>
                <td>103</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="31 103">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_final_simple_py.html">tests\test_signal_generator_final_simple.py</a></td>
                <td>206</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="39 206">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_signal_generator_simple_py.html">tests\test_signal_generator_simple.py</a></td>
                <td>241</td>
                <td>186</td>
                <td>0</td>
                <td class="right" data-ratio="55 241">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_simple_py.html">tests\test_simple.py</a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_symbol_processing_py.html">tests\test_symbol_processing.py</a></td>
                <td>238</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="53 238">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_monitor_py.html">tests\test_system_monitor.py</a></td>
                <td>226</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="53 226">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_monitor_batch2_90_coverage_py.html">tests\test_system_monitor_batch2_90_coverage.py</a></td>
                <td>211</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="37 211">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_monitor_batch6_90_coverage_py.html">tests\test_system_monitor_batch6_90_coverage.py</a></td>
                <td>216</td>
                <td>183</td>
                <td>0</td>
                <td class="right" data-ratio="33 216">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_system_monitor_simple_py.html">tests\test_system_monitor_simple.py</a></td>
                <td>236</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="49 236">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trade_executor_py.html">tests\test_trade_executor.py</a></td>
                <td>348</td>
                <td>290</td>
                <td>0</td>
                <td class="right" data-ratio="58 348">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trade_executor_90_percent_py.html">tests\test_trade_executor_90_percent.py</a></td>
                <td>171</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="40 171">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trade_executor_simple_py.html">tests\test_trade_executor_simple.py</a></td>
                <td>205</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="41 205">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_90_coverage_py.html">tests\test_trend_analyzer_90_coverage.py</a></td>
                <td>125</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="32 125">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_additional_py.html">tests\test_trend_analyzer_additional.py</a></td>
                <td>51</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="26 51">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_analyzer_py.html">tests\test_trend_analyzer_analyzer.py</a></td>
                <td>81</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="25 81">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_analyzer_90_coverage_py.html">tests\test_trend_analyzer_analyzer_90_coverage.py</a></td>
                <td>45</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="16 45">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_comprehensive_py.html">tests\test_trend_analyzer_comprehensive.py</a></td>
                <td>129</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="28 129">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_config_validation_py.html">tests\test_trend_analyzer_config_validation.py</a></td>
                <td>22</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_direct_py.html">tests\test_trend_analyzer_direct.py</a></td>
                <td>76</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="29 76">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_isolated_py.html">tests\test_trend_analyzer_isolated.py</a></td>
                <td>200</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="49 200">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_minimal_py.html">tests\test_trend_analyzer_minimal.py</a></td>
                <td>114</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="20 114">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_trend_analyzer_simple_py.html">tests\test_trend_analyzer_simple.py</a></td>
                <td>72</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="24 72">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_comprehensive_py.html">tests\test_volatility_forecaster_comprehensive.py</a></td>
                <td>248</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="56 248">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_config_validation_py.html">tests\test_volatility_forecaster_config_validation.py</a></td>
                <td>27</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="9 27">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_py.html">tests\test_volatility_forecaster_garch_model.py</a></td>
                <td>88</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="21 88">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_coverage_py.html">tests\test_volatility_forecaster_garch_model_90_coverage.py</a></td>
                <td>88</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="26 88">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_plus_py.html">tests\test_volatility_forecaster_garch_model_90_plus.py</a></td>
                <td>164</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="34 164">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_direct_py.html">tests\test_volatility_forecaster_garch_model_direct.py</a></td>
                <td>166</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="45 166">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_final_90_py.html">tests\test_volatility_forecaster_garch_model_final_90.py</a></td>
                <td>45</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="15 45">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_forecaster_garch_model_full_py.html">tests\test_volatility_forecaster_garch_model_full.py</a></td>
                <td>78</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="17 78">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b0b94eca26fbf402___init___py.html">tests\test_volatility_indices\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b0b94eca26fbf402_test_analyzer_py.html">tests\test_volatility_indices\test_analyzer.py</a></td>
                <td>151</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="25 151">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b0b94eca26fbf402_test_client_py.html">tests\test_volatility_indices\test_client.py</a></td>
                <td>144</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="38 144">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b0b94eca26fbf402_test_models_py.html">tests\test_volatility_indices\test_models.py</a></td>
                <td>80</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="9 80">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_indices_analyzer_batch3_90_coverage_py.html">tests\test_volatility_indices_analyzer_batch3_90_coverage.py</a></td>
                <td>164</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="32 164">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_indices_models_90_percent_py.html">tests\test_volatility_indices_models_90_percent.py</a></td>
                <td>65</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="16 65">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volatility_indices_models_phase5h_py.html">tests\test_volatility_indices_models_phase5h.py</a></td>
                <td>83</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="12 83">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_296e2f3da3f4e87a___init___py.html">tests\test_volume_profile\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_296e2f3da3f4e87a_test_calculator_py.html">tests\test_volume_profile\test_calculator.py</a></td>
                <td>92</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="21 92">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_296e2f3da3f4e87a_test_client_py.html">tests\test_volume_profile\test_client.py</a></td>
                <td>175</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="35 175">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_296e2f3da3f4e87a_test_models_py.html">tests\test_volume_profile\test_models.py</a></td>
                <td>52</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="9 52">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_init_batch8_90_coverage_py.html">tests\test_volume_profile_init_batch8_90_coverage.py</a></td>
                <td>249</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="32 249">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_models_100_percent_py.html">tests\test_volume_profile_models_100_percent.py</a></td>
                <td>83</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="13 83">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_models_90_py.html">tests\test_volume_profile_models_90.py</a></td>
                <td>26</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="7 26">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_models_batch3_90_coverage_py.html">tests\test_volume_profile_models_batch3_90_coverage.py</a></td>
                <td>133</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="29 133">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_models_batch5_90_coverage_py.html">tests\test_volume_profile_models_batch5_90_coverage.py</a></td>
                <td>169</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="33 169">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_volume_profile_models_phase5i_py.html">tests\test_volume_profile_models_phase5i.py</a></td>
                <td>91</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="15 91">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_75fcbe42f7da93f2___init___py.html">tests\test_vwap\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_75fcbe42f7da93f2_test_calculator_py.html">tests\test_vwap\test_calculator.py</a></td>
                <td>140</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="25 140">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_75fcbe42f7da93f2_test_client_py.html">tests\test_vwap\test_client.py</a></td>
                <td>143</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="36 143">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_75fcbe42f7da93f2_test_models_py.html">tests\test_vwap\test_models.py</a></td>
                <td>71</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="11 71">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_vwap_init_batch8_90_coverage_py.html">tests\test_vwap_init_batch8_90_coverage.py</a></td>
                <td>246</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="32 246">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_vwap_models_100_percent_py.html">tests\test_vwap_models_100_percent.py</a></td>
                <td>32</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="6 32">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_vwap_models_batch4_90_coverage_py.html">tests\test_vwap_models_batch4_90_coverage.py</a></td>
                <td>205</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="34 205">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_vwap_models_phase5j_py.html">tests\test_vwap_models_phase5j.py</a></td>
                <td>124</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="16 124">13%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>93400</td>
                <td>73152</td>
                <td>0</td>
                <td class="right" data-ratio="20248 93400">22%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:11 -0500
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_a44f0ac069e85531_test_vwap_models_phase5j_py.html"></a>
        <a id="nextFileLink" class="nav" href="check_all_modules_coverage_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
