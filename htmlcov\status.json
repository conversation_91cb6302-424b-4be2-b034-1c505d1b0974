{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.1", "globals": "959adf409de5094af6f7141aec8498c5", "files": {"check_all_modules_coverage_py": {"hash": "1c2baa9c9281d8e1ebaadc2ebc46877a", "index": {"url": "check_all_modules_coverage_py.html", "file": "check_all_modules_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_final_coverage_py": {"hash": "82b0f4e8e2ef5911c5149182efc2eb3b", "index": {"url": "check_final_coverage_py.html", "file": "check_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_imports_py": {"hash": "ea3ae8e3fd7e386de54c1b2ecaec6e18", "index": {"url": "check_imports_py.html", "file": "check_imports.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_individual_module_coverage_py": {"hash": "c9d3a6f81ddcdfc7d75dba67b5e17985", "index": {"url": "check_individual_module_coverage_py.html", "file": "check_individual_module_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_metrics_coverage_py": {"hash": "80cd313e8c5511c117916093ee8d6229", "index": {"url": "check_metrics_coverage_py.html", "file": "check_metrics_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_module_coverage_py": {"hash": "49e8858348d21a37a07d1c18baa7342e", "index": {"url": "check_module_coverage_py.html", "file": "check_module_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_module_path_py": {"hash": "26ebc4407772784cf32779389f97b494", "index": {"url": "check_module_path_py.html", "file": "check_module_path.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "check_sentiment_analyzer_coverage_py": {"hash": "f4444bd179a9d0324ded0492b48d43b7", "index": {"url": "check_sentiment_analyzer_coverage_py.html", "file": "check_sentiment_analyzer_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "conftest_py": {"hash": "ab1f851acf07293bd3b4b5fe96438b96", "index": {"url": "conftest_py.html", "file": "conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "direct_coverage_test_py": {"hash": "e7919ebb30e87f4c75ad3eb00be2df98", "index": {"url": "direct_coverage_test_py.html", "file": "direct_coverage_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "direct_coverage_test_mocked_py": {"hash": "1c560e09f0e39bd128ee98ff886cf974", "index": {"url": "direct_coverage_test_mocked_py.html", "file": "direct_coverage_test_mocked.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "exercise_models_py": {"hash": "21a0600cc4608886426d9291c9072f12", "index": {"url": "exercise_models_py.html", "file": "exercise_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "generate_coverage_report_py": {"hash": "089b423e8e41ba8af43d5a4e1d5baae2", "index": {"url": "generate_coverage_report_py.html", "file": "generate_coverage_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "isolated_config_test_py": {"hash": "17f7cbaca326d5aa134e4637877c1e0d", "index": {"url": "isolated_config_test_py.html", "file": "isolated_config_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "launcher_py": {"hash": "73d74bd77581c0542dcf3c16501ab8c6", "index": {"url": "launcher_py.html", "file": "launcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 110, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "print_module_content_py": {"hash": "38a084a49d8b23154d1291db15eaaf00", "index": {"url": "print_module_content_py.html", "file": "print_module_content.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_all_tests_no_cov_py": {"hash": "404bceee9dff6dc16c6c2b286b9b615c", "index": {"url": "run_all_tests_no_cov_py.html", "file": "run_all_tests_no_cov.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_combined_tests_py": {"hash": "0167f1e4b0c679a2e98025c68c1cf85b", "index": {"url": "run_combined_tests_py.html", "file": "run_combined_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_coverage_direct_py": {"hash": "47dfa48f32b1fd1f838c9e65f99f769e", "index": {"url": "run_coverage_direct_py.html", "file": "run_coverage_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_coverage_for_modules_py": {"hash": "75784edb89add2a2988731e8e9790662", "index": {"url": "run_coverage_for_modules_py.html", "file": "run_coverage_for_modules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_full_coverage_report_py": {"hash": "4533215010f593c063e603852d80c6c4", "index": {"url": "run_full_coverage_report_py.html", "file": "run_full_coverage_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_gemini_client_tests_py": {"hash": "42b18fcf1b60fa83bf4e197e440c1c36", "index": {"url": "run_gemini_client_tests_py.html", "file": "run_gemini_client_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_heikin_ashi_tests_py": {"hash": "6f55a53059145d105f07bdf5beb4b540", "index": {"url": "run_heikin_ashi_tests_py.html", "file": "run_heikin_ashi_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_individual_news_tests_py": {"hash": "1bb2f6a6e703603686cea906924cb3a9", "index": {"url": "run_individual_news_tests_py.html", "file": "run_individual_news_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_macro_analyzer_tests_py": {"hash": "c2d13578e872622239e8b104ebe76b80", "index": {"url": "run_macro_analyzer_tests_py.html", "file": "run_macro_analyzer_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_macro_fetcher_tests_py": {"hash": "075e676760e37a10abeb92ae4799b4fc", "index": {"url": "run_macro_fetcher_tests_py.html", "file": "run_macro_fetcher_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_market_hours_tests_py": {"hash": "a417d37a4c5a03ad9bdd3fc37dd27854", "index": {"url": "run_market_hours_tests_py.html", "file": "run_market_hours_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_metrics_tests_py": {"hash": "ee59c7a3c277d7828038a1d462e48ddc", "index": {"url": "run_metrics_tests_py.html", "file": "run_metrics_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_models_coverage_py": {"hash": "4b79ea8b3201ac1d5809739b8a63e793", "index": {"url": "run_models_coverage_py.html", "file": "run_models_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_modular_coverage_py": {"hash": "5549e43af10782a0ece1708512e9f534", "index": {"url": "run_modular_coverage_py.html", "file": "run_modular_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_module_tests_py": {"hash": "3c465741c93e44065c303d80b14497fc", "index": {"url": "run_module_tests_py.html", "file": "run_module_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_multilingual_news_coverage_py": {"hash": "a7e09e93b1f561d43d9f1b2ad318ba97", "index": {"url": "run_multilingual_news_coverage_py.html", "file": "run_multilingual_news_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_multilingual_news_tests_py": {"hash": "19f35d4cf1551336cfbb59c2eb2e69bc", "index": {"url": "run_multilingual_news_tests_py.html", "file": "run_multilingual_news_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_pattern_recognizer_tests_py": {"hash": "402bb489565602841d9e15c1845c713c", "index": {"url": "run_pattern_recognizer_tests_py.html", "file": "run_pattern_recognizer_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_position_sizer_tests_py": {"hash": "abee18e93acfb061abaf94f5d603c11c", "index": {"url": "run_position_sizer_tests_py.html", "file": "run_position_sizer_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_prompt_builder_tests_py": {"hash": "0232dfb7b40f7c538fa35ce9d941c8c8", "index": {"url": "run_prompt_builder_tests_py.html", "file": "run_prompt_builder_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_pytest_py": {"hash": "85bd72daff430ce4d37cbc0946565f6a", "index": {"url": "run_pytest_py.html", "file": "run_pytest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_pytest_coverage_py": {"hash": "84bfbc363cfc67d1ec58dd371fc41181", "index": {"url": "run_pytest_coverage_py.html", "file": "run_pytest_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_sentiment_analyzer_tests_py": {"hash": "1f253f2453fc2a3568e9158ebc0460b0", "index": {"url": "run_sentiment_analyzer_tests_py.html", "file": "run_sentiment_analyzer_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_specific_module_tests_py": {"hash": "9fe6965aff7f74042b4a110e970652cb", "index": {"url": "run_specific_module_tests_py.html", "file": "run_specific_module_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_tests_py": {"hash": "347ba8f71d183858018c29c56e444c6e", "index": {"url": "run_tests_py.html", "file": "run_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_trend_analyzer_tests_py": {"hash": "0fbc7d846726728601d18363bd75cff0", "index": {"url": "run_trend_analyzer_tests_py.html", "file": "run_trend_analyzer_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_utils_tests_py": {"hash": "3fb46bb5fae3c54f7347c386e6d6caad", "index": {"url": "run_utils_tests_py.html", "file": "run_utils_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3___init___py": {"hash": "7e8ce2b9c9dce17d46616230c155972b", "index": {"url": "z_21758df60ac2cdd3___init___py.html", "file": "src\\forex_bot\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3___main___py": {"hash": "4ec1208f0ff8c628aa3da59b2458895c", "index": {"url": "z_21758df60ac2cdd3___main___py.html", "file": "src\\forex_bot\\__main__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_analysis_constants_py": {"hash": "efd47f1ea88d09ec33fd0a1ac33cbe3d", "index": {"url": "z_21758df60ac2cdd3_analysis_constants_py.html", "file": "src\\forex_bot\\analysis_constants.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b50040b513dcffe1___init___py": {"hash": "e985b372d0225a23028fca431fabcd21", "index": {"url": "z_b50040b513dcffe1___init___py.html", "file": "src\\forex_bot\\backtester\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b50040b513dcffe1_backtester_service_py": {"hash": "9abac4cab30ca31be4024c3a68ed338b", "index": {"url": "z_b50040b513dcffe1_backtester_service_py.html", "file": "src\\forex_bot\\backtester\\backtester_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b50040b513dcffe1_config_py": {"hash": "325c4f3fb43c8611cb6b805008a2798f", "index": {"url": "z_b50040b513dcffe1_config_py.html", "file": "src\\forex_bot\\backtester\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b50040b513dcffe1_order_simulator_py": {"hash": "7e656514a76986d705ca76f90ef55f80", "index": {"url": "z_b50040b513dcffe1_order_simulator_py.html", "file": "src\\forex_bot\\backtester\\order_simulator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b50040b513dcffe1_performance_tracker_py": {"hash": "0f8dd7c5effb58edea6abde59ddaf303", "index": {"url": "z_b50040b513dcffe1_performance_tracker_py.html", "file": "src\\forex_bot\\backtester\\performance_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 0, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_bot_orchestrator_py": {"hash": "8dbf046a47794a77c9469bdf71332d41", "index": {"url": "z_21758df60ac2cdd3_bot_orchestrator_py.html", "file": "src\\forex_bot\\bot_orchestrator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 823, "n_excluded": 0, "n_missing": 769, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_bot_scheduler_py": {"hash": "17f83550b6cade4ba9eea82922b965cf", "index": {"url": "z_21758df60ac2cdd3_bot_scheduler_py.html", "file": "src\\forex_bot\\bot_scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 218, "n_excluded": 0, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_config_loader_py": {"hash": "1ce82ab08d27c5c35f363dd47b93a942", "index": {"url": "z_21758df60ac2cdd3_config_loader_py.html", "file": "src\\forex_bot\\config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 234, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_config_settings_py": {"hash": "a6092574f353e6f666a4c905b1e49893", "index": {"url": "z_21758df60ac2cdd3_config_settings_py.html", "file": "src\\forex_bot\\config_settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d___init___py": {"hash": "e6f739bdf9c441996ebd6b94f6f23a30", "index": {"url": "z_08e4f74bb738659d___init___py.html", "file": "src\\forex_bot\\correlation_matrix\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_calculator_py": {"hash": "07718b5800968754efdf25627b7bd0aa", "index": {"url": "z_08e4f74bb738659d_calculator_py.html", "file": "src\\forex_bot\\correlation_matrix\\calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_calculator_part2_py": {"hash": "f297d93ae912d0462489ffe26999f092", "index": {"url": "z_08e4f74bb738659d_calculator_part2_py.html", "file": "src\\forex_bot\\correlation_matrix\\calculator_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_calculator_part3_py": {"hash": "ddf1d88be211e578888decfe28c0b013", "index": {"url": "z_08e4f74bb738659d_calculator_part3_py.html", "file": "src\\forex_bot\\correlation_matrix\\calculator_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_client_py": {"hash": "d40349729f827806a246f62663e4f4a9", "index": {"url": "z_08e4f74bb738659d_client_py.html", "file": "src\\forex_bot\\correlation_matrix\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_models_py": {"hash": "866ac93f9e1a4949952cbb9a327da5bb", "index": {"url": "z_08e4f74bb738659d_models_py.html", "file": "src\\forex_bot\\correlation_matrix\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 235, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_models_part2_py": {"hash": "4acafa831cad8e0357ade89c6c54adfc", "index": {"url": "z_08e4f74bb738659d_models_part2_py.html", "file": "src\\forex_bot\\correlation_matrix\\models_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_visualizer_py": {"hash": "fc19b6415e7dcb82d4f31d408e56c365", "index": {"url": "z_08e4f74bb738659d_visualizer_py.html", "file": "src\\forex_bot\\correlation_matrix\\visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 68, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e4f74bb738659d_visualizer_part2_py": {"hash": "77265099a5531a320ef951705dbba4e9", "index": {"url": "z_08e4f74bb738659d_visualizer_part2_py.html", "file": "src\\forex_bot\\correlation_matrix\\visualizer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 68, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c3d3dfe922b6ea2a___init___py": {"hash": "3b67937e45919c57f24d985844521435", "index": {"url": "z_c3d3dfe922b6ea2a___init___py.html", "file": "src\\forex_bot\\cot_reports\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c3d3dfe922b6ea2a_analyzer_py": {"hash": "240f9e525446dca7cc850706afe3ca60", "index": {"url": "z_c3d3dfe922b6ea2a_analyzer_py.html", "file": "src\\forex_bot\\cot_reports\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c3d3dfe922b6ea2a_client_py": {"hash": "52ca1add0353d76e5164b3093048cd61", "index": {"url": "z_c3d3dfe922b6ea2a_client_py.html", "file": "src\\forex_bot\\cot_reports\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c3d3dfe922b6ea2a_models_py": {"hash": "5c43d0676b2c1194826a79a0c34cfb21", "index": {"url": "z_c3d3dfe922b6ea2a_models_py.html", "file": "src\\forex_bot\\cot_reports\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1cbbbd3bf875576e___init___py": {"hash": "e30ae8d7dbf261f9a08e406a74ef8269", "index": {"url": "z_1cbbbd3bf875576e___init___py.html", "file": "src\\forex_bot\\cvd\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1cbbbd3bf875576e_calculator_py": {"hash": "bd8a1968e0976f2e50ee72dface1db31", "index": {"url": "z_1cbbbd3bf875576e_calculator_py.html", "file": "src\\forex_bot\\cvd\\calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1cbbbd3bf875576e_client_py": {"hash": "5a7d1599d4b161c107ea590cd341eed1", "index": {"url": "z_1cbbbd3bf875576e_client_py.html", "file": "src\\forex_bot\\cvd\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1cbbbd3bf875576e_models_py": {"hash": "0fead26070f0e9133c4a2a2db6d6dc4e", "index": {"url": "z_1cbbbd3bf875576e_models_py.html", "file": "src\\forex_bot\\cvd\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8cfda85c8b792357___init___py": {"hash": "7a8682a0e071e80472b60152f9aec0bf", "index": {"url": "z_8cfda85c8b792357___init___py.html", "file": "src\\forex_bot\\event_bus\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8cfda85c8b792357_config_py": {"hash": "5b5d932fd04d3d5f580e678a950b2e17", "index": {"url": "z_8cfda85c8b792357_config_py.html", "file": "src\\forex_bot\\event_bus\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8cfda85c8b792357_consumer_py": {"hash": "c5792c101f323ccf6d42fcebe3df286e", "index": {"url": "z_8cfda85c8b792357_consumer_py.html", "file": "src\\forex_bot\\event_bus\\consumer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 247, "n_excluded": 0, "n_missing": 193, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8cfda85c8b792357_event_schemas_py": {"hash": "062c3e6ea5c3c8c2c908ea81bb362b02", "index": {"url": "z_8cfda85c8b792357_event_schemas_py.html", "file": "src\\forex_bot\\event_bus\\event_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8cfda85c8b792357_producer_py": {"hash": "4aafa42c8521a3201f9c6a95fc62210e", "index": {"url": "z_8cfda85c8b792357_producer_py.html", "file": "src\\forex_bot\\event_bus\\producer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_gemini_client_py": {"hash": "ce338f1e89b034d4f55c78f2d7e212d4", "index": {"url": "z_21758df60ac2cdd3_gemini_client_py.html", "file": "src\\forex_bot\\gemini_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_54e5a98f26b615b9___init___py": {"hash": "42e103c091f9649df24575d247a0a8a6", "index": {"url": "z_54e5a98f26b615b9___init___py.html", "file": "src\\forex_bot\\global_pmi\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_54e5a98f26b615b9_analyzer_py": {"hash": "0103cad46597f7267ce46a005f9037fa", "index": {"url": "z_54e5a98f26b615b9_analyzer_py.html", "file": "src\\forex_bot\\global_pmi\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_54e5a98f26b615b9_client_py": {"hash": "272a00cc4b3a992e930d3f5b01143426", "index": {"url": "z_54e5a98f26b615b9_client_py.html", "file": "src\\forex_bot\\global_pmi\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_54e5a98f26b615b9_models_py": {"hash": "c10f28d463cf6852701193c3bb24b617", "index": {"url": "z_54e5a98f26b615b9_models_py.html", "file": "src\\forex_bot\\global_pmi\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_health_endpoints_py": {"hash": "86fb95063145d7bde730ff3de9db1a1e", "index": {"url": "z_21758df60ac2cdd3_health_endpoints_py.html", "file": "src\\forex_bot\\health_endpoints.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c578cea568b4ed4d___init___py": {"hash": "3c315e05f95d7a7eb274ed66f45571f2", "index": {"url": "z_c578cea568b4ed4d___init___py.html", "file": "src\\forex_bot\\heikin_ashi\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c578cea568b4ed4d_calculator_py": {"hash": "547852264896851fa754c8d94367bab6", "index": {"url": "z_c578cea568b4ed4d_calculator_py.html", "file": "src\\forex_bot\\heikin_ashi\\calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_indicators_py": {"hash": "8fd09db440828db72e82dac74ab4e520", "index": {"url": "z_21758df60ac2cdd3_indicators_py.html", "file": "src\\forex_bot\\indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_initialization_py": {"hash": "85d28cc8d8ccc1be04a11f59ebc5ff3b", "index": {"url": "z_21758df60ac2cdd3_initialization_py.html", "file": "src\\forex_bot\\initialization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_initialize_py": {"hash": "e8ff26bfaa6c852731d325e8cd8452ab", "index": {"url": "z_21758df60ac2cdd3_initialize_py.html", "file": "src\\forex_bot\\initialize.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_launcher_py": {"hash": "1134e8d95992c4a09b7f37990b50bce7", "index": {"url": "z_21758df60ac2cdd3_launcher_py.html", "file": "src\\forex_bot\\launcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 0, "n_missing": 194, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_log_manager_py": {"hash": "632c4c84a74ed02a9cc7fd9cc2929952", "index": {"url": "z_21758df60ac2cdd3_log_manager_py.html", "file": "src\\forex_bot\\log_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 0, "n_missing": 199, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_log_uploader_py": {"hash": "524c4bfefa8a58e5a6f19d44ab5dc9fa", "index": {"url": "z_21758df60ac2cdd3_log_uploader_py.html", "file": "src\\forex_bot\\log_uploader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d2cad6892f133d01___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_d2cad6892f133d01___init___py.html", "file": "src\\forex_bot\\macro_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d2cad6892f133d01_analyzer_py": {"hash": "d8a3c5cee9d5c5103899894ad1af792e", "index": {"url": "z_d2cad6892f133d01_analyzer_py.html", "file": "src\\forex_bot\\macro_analyzer\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d2cad6892f133d01_fetcher_py": {"hash": "eff23da8db65549e805bae367330560b", "index": {"url": "z_d2cad6892f133d01_fetcher_py.html", "file": "src\\forex_bot\\macro_analyzer\\fetcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 201, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc___init___py": {"hash": "08b03c8f7a29bb3c47282c4d8f6c56c2", "index": {"url": "z_83f52e07fd9619cc___init___py.html", "file": "src\\forex_bot\\market_depth_visualizer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_client_py": {"hash": "316531809e45ad306a8fe5e3b7bbefba", "index": {"url": "z_83f52e07fd9619cc_client_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_models_py": {"hash": "1d19a35b50a7b35ed6f40366703d1b0a", "index": {"url": "z_83f52e07fd9619cc_models_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 271, "n_excluded": 0, "n_missing": 126, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_models_part2_py": {"hash": "5706924e45fb9802e2a4e8ac7ef1e698", "index": {"url": "z_83f52e07fd9619cc_models_part2_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\models_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_visualizer_py": {"hash": "7998b9e394a43fa1946103450cba0a25", "index": {"url": "z_83f52e07fd9619cc_visualizer_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_visualizer_part2_py": {"hash": "eb973c8b8cf2e94d18d7d62625fa1ad1", "index": {"url": "z_83f52e07fd9619cc_visualizer_part2_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\visualizer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 149, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_visualizer_part3_py": {"hash": "5daffb5d35762d1c477f382c7ab936a3", "index": {"url": "z_83f52e07fd9619cc_visualizer_part3_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\visualizer_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83f52e07fd9619cc_visualizer_part4_py": {"hash": "94975c4a1a2d85623e01e5d33a1e2b51", "index": {"url": "z_83f52e07fd9619cc_visualizer_part4_py.html", "file": "src\\forex_bot\\market_depth_visualizer\\visualizer_part4.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e313fad6449ccdbc___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_e313fad6449ccdbc___init___py.html", "file": "src\\forex_bot\\market_hours\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e313fad6449ccdbc_session_info_py": {"hash": "400b309b7890d9700f921788ebae4492", "index": {"url": "z_e313fad6449ccdbc_session_info_py.html", "file": "src\\forex_bot\\market_hours\\session_info.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e313fad6449ccdbc_settings_py": {"hash": "4e40e206ed28f909845e95fdf07051f9", "index": {"url": "z_e313fad6449ccdbc_settings_py.html", "file": "src\\forex_bot\\market_hours\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_metrics_py": {"hash": "aa499518e4843814351562c5d50d6faf", "index": {"url": "z_21758df60ac2cdd3_metrics_py.html", "file": "src\\forex_bot\\metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53___init___py": {"hash": "4bc613379d83dcc160e1de35909bac7f", "index": {"url": "z_1b1ecb8b2ace5d53___init___py.html", "file": "src\\forex_bot\\metrics\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_backtester_metrics_py": {"hash": "cc8e83e24b14f62550e99dc7ccc4fd2d", "index": {"url": "z_1b1ecb8b2ace5d53_backtester_metrics_py.html", "file": "src\\forex_bot\\metrics\\backtester_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_event_bus_metrics_py": {"hash": "e1d8840a2ab9af83a1acf4d38e5e80aa", "index": {"url": "z_1b1ecb8b2ace5d53_event_bus_metrics_py.html", "file": "src\\forex_bot\\metrics\\event_bus_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_metrics_config_py": {"hash": "5da3907afa2f866a39436fd54547fbd8", "index": {"url": "z_1b1ecb8b2ace5d53_metrics_config_py.html", "file": "src\\forex_bot\\metrics\\metrics_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_mt5_metrics_py": {"hash": "a6c4e6194b824954b1fd78a3c661fe8b", "index": {"url": "z_1b1ecb8b2ace5d53_mt5_metrics_py.html", "file": "src\\forex_bot\\metrics\\mt5_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_otel_tracing_py": {"hash": "d12af7688a5bb3a415b7bcd0fee39e9c", "index": {"url": "z_1b1ecb8b2ace5d53_otel_tracing_py.html", "file": "src\\forex_bot\\metrics\\otel_tracing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b1ecb8b2ace5d53_prometheus_metrics_py": {"hash": "dd7d29d641648b001701b7013d4ac4c8", "index": {"url": "z_1b1ecb8b2ace5d53_prometheus_metrics_py.html", "file": "src\\forex_bot\\metrics\\prometheus_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 312, "n_excluded": 0, "n_missing": 273, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8___init___py": {"hash": "1f04e87a743102ffe3a182b3e631fa5d", "index": {"url": "z_1a17491f203aa3a8___init___py.html", "file": "src\\forex_bot\\metrics_dashboard\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_client_py": {"hash": "a039dcad48c666f25b8d92bb032bf1fd", "index": {"url": "z_1a17491f203aa3a8_client_py.html", "file": "src\\forex_bot\\metrics_dashboard\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_dashboard_generator_py": {"hash": "9f2563fcff22bd9701e112b50d252518", "index": {"url": "z_1a17491f203aa3a8_dashboard_generator_py.html", "file": "src\\forex_bot\\metrics_dashboard\\dashboard_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_dashboard_generator_part2_py": {"hash": "b60dd49f0b7d0499d998b4b54e20cb2f", "index": {"url": "z_1a17491f203aa3a8_dashboard_generator_part2_py.html", "file": "src\\forex_bot\\metrics_dashboard\\dashboard_generator_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_dashboard_generator_part3_py": {"hash": "9b3832ed6ad0564edda07bdb3847ea09", "index": {"url": "z_1a17491f203aa3a8_dashboard_generator_part3_py.html", "file": "src\\forex_bot\\metrics_dashboard\\dashboard_generator_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_metrics_calculator_py": {"hash": "75e6dbfd98c531c5368364ac970cf683", "index": {"url": "z_1a17491f203aa3a8_metrics_calculator_py.html", "file": "src\\forex_bot\\metrics_dashboard\\metrics_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_metrics_calculator_part2_py": {"hash": "96c56ab363039214111193b9c6ac9229", "index": {"url": "z_1a17491f203aa3a8_metrics_calculator_part2_py.html", "file": "src\\forex_bot\\metrics_dashboard\\metrics_calculator_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_models_py": {"hash": "456544cd9e32f8363d3fcf25000e794f", "index": {"url": "z_1a17491f203aa3a8_models_py.html", "file": "src\\forex_bot\\metrics_dashboard\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 223, "n_excluded": 0, "n_missing": 80, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_models_part2_py": {"hash": "25add1ef83be6e29478dcaaede9a9edb", "index": {"url": "z_1a17491f203aa3a8_models_part2_py.html", "file": "src\\forex_bot\\metrics_dashboard\\models_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_models_part3_py": {"hash": "a94986508dc21c73a6b66c3b5c2a0644", "index": {"url": "z_1a17491f203aa3a8_models_part3_py.html", "file": "src\\forex_bot\\metrics_dashboard\\models_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_visualizer_py": {"hash": "4512e6b62b52562d6dc8c624bfe48821", "index": {"url": "z_1a17491f203aa3a8_visualizer_py.html", "file": "src\\forex_bot\\metrics_dashboard\\visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_visualizer_part2_py": {"hash": "ce1d75f86b3274068dd704ad8250feb8", "index": {"url": "z_1a17491f203aa3a8_visualizer_part2_py.html", "file": "src\\forex_bot\\metrics_dashboard\\visualizer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_visualizer_part3_py": {"hash": "50880c638a3b18eb8642c3dac3951e89", "index": {"url": "z_1a17491f203aa3a8_visualizer_part3_py.html", "file": "src\\forex_bot\\metrics_dashboard\\visualizer_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 119, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1a17491f203aa3a8_visualizer_part4_py": {"hash": "73c270519889efedd798cc2c1b0f3bdd", "index": {"url": "z_1a17491f203aa3a8_visualizer_part4_py.html", "file": "src\\forex_bot\\metrics_dashboard\\visualizer_part4.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b___init___py": {"hash": "8b832fe6e98b5b0da3858d17dcd51d93", "index": {"url": "z_6696b9cca54af58b___init___py.html", "file": "src\\forex_bot\\ml_registry\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_dashboard_py": {"hash": "ed2285225e55bb638d5c961ef651de99", "index": {"url": "z_6696b9cca54af58b_dashboard_py.html", "file": "src\\forex_bot\\ml_registry\\dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5ea832e13b4aced8___init___py": {"hash": "8371f2d5904faf1654e9bfd2015518f6", "index": {"url": "z_5ea832e13b4aced8___init___py.html", "file": "src\\forex_bot\\ml_registry\\integrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5ea832e13b4aced8_regime_detector_integration_py": {"hash": "80a3ce85395dcbdfb16e2e2cedf0e911", "index": {"url": "z_5ea832e13b4aced8_regime_detector_integration_py.html", "file": "src\\forex_bot\\ml_registry\\integrations\\regime_detector_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5ea832e13b4aced8_volatility_forecaster_integration_py": {"hash": "5c326021524d63edcc3393f2d3d2bbfc", "index": {"url": "z_5ea832e13b4aced8_volatility_forecaster_integration_py.html", "file": "src\\forex_bot\\ml_registry\\integrations\\volatility_forecaster_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_mlflow_registry_py": {"hash": "7534eea9e36573bc5b3ad0496f5080ae", "index": {"url": "z_6696b9cca54af58b_mlflow_registry_py.html", "file": "src\\forex_bot\\ml_registry\\mlflow_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 523, "n_excluded": 0, "n_missing": 460, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_alerts_py": {"hash": "dc66d278505f2e1e7368e1746568720b", "index": {"url": "z_6696b9cca54af58b_model_alerts_py.html", "file": "src\\forex_bot\\ml_registry\\model_alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_config_py": {"hash": "77faf79332eed3a556b10b9bfd108fae", "index": {"url": "z_6696b9cca54af58b_model_config_py.html", "file": "src\\forex_bot\\ml_registry\\model_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_deployment_py": {"hash": "6f10e7bc7e09aec93ae5a622fac5d4d5", "index": {"url": "z_6696b9cca54af58b_model_deployment_py.html", "file": "src\\forex_bot\\ml_registry\\model_deployment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_evaluation_py": {"hash": "b59fb3d869a19062f7073ecc525ead03", "index": {"url": "z_6696b9cca54af58b_model_evaluation_py.html", "file": "src\\forex_bot\\ml_registry\\model_evaluation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_manager_py": {"hash": "82fd582128c1d5db35a8a40baf086fea", "index": {"url": "z_6696b9cca54af58b_model_manager_py.html", "file": "src\\forex_bot\\ml_registry\\model_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 235, "n_excluded": 0, "n_missing": 202, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6696b9cca54af58b_model_monitoring_py": {"hash": "acce4111ba6ed19325a45a1c216a2f0d", "index": {"url": "z_6696b9cca54af58b_model_monitoring_py.html", "file": "src\\forex_bot\\ml_registry\\model_monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_910738de801f3d46_train_hmm_py": {"hash": "7620c2443a6542fcb13a808b22c7920e", "index": {"url": "z_910738de801f3d46_train_hmm_py.html", "file": "src\\forex_bot\\models\\train_hmm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_mt5_client_py": {"hash": "e4ba0bbedbe2d9cc055736961f9feb06", "index": {"url": "z_21758df60ac2cdd3_mt5_client_py.html", "file": "src\\forex_bot\\mt5_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 411, "n_excluded": 0, "n_missing": 379, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_mt5_constants_py": {"hash": "c83b3961f794de8bb6311db839d8692b", "index": {"url": "z_21758df60ac2cdd3_mt5_constants_py.html", "file": "src\\forex_bot\\mt5_constants.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_mt5_event_producer_py": {"hash": "bda36652fbcbd596aa1c4ab88f025e03", "index": {"url": "z_21758df60ac2cdd3_mt5_event_producer_py.html", "file": "src\\forex_bot\\mt5_event_producer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 0, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1924e2f905e091db___init___py": {"hash": "01e7ae080f907e62cfa40220ed57812d", "index": {"url": "z_1924e2f905e091db___init___py.html", "file": "src\\forex_bot\\multilingual_news\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1924e2f905e091db_analyzer_py": {"hash": "bc81aa0a2d6d8850ba999d377903ddbd", "index": {"url": "z_1924e2f905e091db_analyzer_py.html", "file": "src\\forex_bot\\multilingual_news\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 292, "n_excluded": 0, "n_missing": 250, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1924e2f905e091db_client_py": {"hash": "8dbdab2c1561ab4f7d24d50dea124f1a", "index": {"url": "z_1924e2f905e091db_client_py.html", "file": "src\\forex_bot\\multilingual_news\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 193, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1924e2f905e091db_models_py": {"hash": "fec062e9172dd98b08dbd5c300310071", "index": {"url": "z_1924e2f905e091db_models_py.html", "file": "src\\forex_bot\\multilingual_news\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a___init___py": {"hash": "80ee7b45f33a1afaaaab212eaec64eb9", "index": {"url": "z_ba10c0e1a33d368a___init___py.html", "file": "src\\forex_bot\\news_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_entity_extractor_py": {"hash": "4105f38786495ac3fbb3cae93bf3a142", "index": {"url": "z_ba10c0e1a33d368a_entity_extractor_py.html", "file": "src\\forex_bot\\news_analyzer\\entity_extractor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_news_aggregator_py": {"hash": "f01fdd4aac347417292029bb1779dd1b", "index": {"url": "z_ba10c0e1a33d368a_news_aggregator_py.html", "file": "src\\forex_bot\\news_analyzer\\news_aggregator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_news_fetcher_py": {"hash": "c92a9dffea9cb0fc143d3c7cc431b18e", "index": {"url": "z_ba10c0e1a33d368a_news_fetcher_py.html", "file": "src\\forex_bot\\news_analyzer\\news_fetcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_news_impact_analyzer_py": {"hash": "6bab53ab2d7cc3457fde73f2e4844a1b", "index": {"url": "z_ba10c0e1a33d368a_news_impact_analyzer_py.html", "file": "src\\forex_bot\\news_analyzer\\news_impact_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_news_processor_py": {"hash": "98660eacca4deba7bd758103d630f59e", "index": {"url": "z_ba10c0e1a33d368a_news_processor_py.html", "file": "src\\forex_bot\\news_analyzer\\news_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_sentiment_analyzer_py": {"hash": "e111dc7b783dd638edd7ffba7dae155d", "index": {"url": "z_ba10c0e1a33d368a_sentiment_analyzer_py.html", "file": "src\\forex_bot\\news_analyzer\\sentiment_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba10c0e1a33d368a_topic_modeler_py": {"hash": "1a3987358c0ce73fa7ec154e3da28c11", "index": {"url": "z_ba10c0e1a33d368a_topic_modeler_py.html", "file": "src\\forex_bot\\news_analyzer\\topic_modeler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_news_service_py": {"hash": "bd599b288e9e64b508cb7efab0f0dac3", "index": {"url": "z_21758df60ac2cdd3_news_service_py.html", "file": "src\\forex_bot\\news_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_07474e1e95032448___init___py": {"hash": "447f40c5db83a25126557559894b6a46", "index": {"url": "z_07474e1e95032448___init___py.html", "file": "src\\forex_bot\\order_book\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_07474e1e95032448_cache_py": {"hash": "af43f9203c5bf741897eca75dace88b0", "index": {"url": "z_07474e1e95032448_cache_py.html", "file": "src\\forex_bot\\order_book\\cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_07474e1e95032448_client_py": {"hash": "2df845afc9100d3c17a82d49c4ff6b59", "index": {"url": "z_07474e1e95032448_client_py.html", "file": "src\\forex_bot\\order_book\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_07474e1e95032448_models_py": {"hash": "abfa63c36c8b68a1b5a9ba99ae48b233", "index": {"url": "z_07474e1e95032448_models_py.html", "file": "src\\forex_bot\\order_book\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30___init___py": {"hash": "a996b1c6cdee3faac076820385f74eba", "index": {"url": "z_8129e89f77e0ee30___init___py.html", "file": "src\\forex_bot\\order_flow_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_analyzer_py": {"hash": "971a9e7beed2e410d2d86db775bf3a26", "index": {"url": "z_8129e89f77e0ee30_analyzer_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_analyzer_part2_py": {"hash": "bcdd3210403ea3db7dffd8bf8f03c6a1", "index": {"url": "z_8129e89f77e0ee30_analyzer_part2_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\analyzer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_client_py": {"hash": "7c8b0cc4167f5b12c38123da6d2b15c7", "index": {"url": "z_8129e89f77e0ee30_client_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 203, "n_excluded": 0, "n_missing": 172, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_models_py": {"hash": "14493926d5e303752869f11e4de74343", "index": {"url": "z_8129e89f77e0ee30_models_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_visualizer_py": {"hash": "0cd7f5e5caf751cdb5e4556663ff2a80", "index": {"url": "z_8129e89f77e0ee30_visualizer_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8129e89f77e0ee30_visualizer_part2_py": {"hash": "e79293cf9406c2c4264ece7aaf93cf7a", "index": {"url": "z_8129e89f77e0ee30_visualizer_part2_py.html", "file": "src\\forex_bot\\order_flow_analyzer\\visualizer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b7f97406eee4d94___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_6b7f97406eee4d94___init___py.html", "file": "src\\forex_bot\\pattern_recognizer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b7f97406eee4d94_recognizer_py": {"hash": "298af50a766f2cda153e552a2dc2b8a3", "index": {"url": "z_6b7f97406eee4d94_recognizer_py.html", "file": "src\\forex_bot\\pattern_recognizer\\recognizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4f0260f9a7d8071___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_e4f0260f9a7d8071___init___py.html", "file": "src\\forex_bot\\performance_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4f0260f9a7d8071_analyzer_py": {"hash": "80d31fab226b83d69f54922b94735136", "index": {"url": "z_e4f0260f9a7d8071_analyzer_py.html", "file": "src\\forex_bot\\performance_analyzer\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 193, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4f0260f9a7d8071_metrics_py": {"hash": "341d526ec70a9910d6883c172b956510", "index": {"url": "z_e4f0260f9a7d8071_metrics_py.html", "file": "src\\forex_bot\\performance_analyzer\\metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 352, "n_excluded": 0, "n_missing": 321, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e4f0260f9a7d8071_utils_py": {"hash": "23a8d4fc14efd0656db2f438a5348ebd", "index": {"url": "z_e4f0260f9a7d8071_utils_py.html", "file": "src\\forex_bot\\performance_analyzer\\utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 170, "n_excluded": 0, "n_missing": 152, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_performance_tracker_py": {"hash": "7bc59bb4a90919aaf3901b8cd1820f8c", "index": {"url": "z_21758df60ac2cdd3_performance_tracker_py.html", "file": "src\\forex_bot\\performance_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 245, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7979b9fa1f62b583___init___py": {"hash": "0a010b1b576464ae022b215aea918103", "index": {"url": "z_7979b9fa1f62b583___init___py.html", "file": "src\\forex_bot\\position_sizer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7979b9fa1f62b583_sizer_py": {"hash": "84ef98cc2917a8c947db441b76b511da", "index": {"url": "z_7979b9fa1f62b583_sizer_py.html", "file": "src\\forex_bot\\position_sizer\\sizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 188, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f___init___py": {"hash": "9c8a85d166e09ff567745b71bd8e9a7e", "index": {"url": "z_688d3285f67a444f___init___py.html", "file": "src\\forex_bot\\prompt_builder\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_correlation_py": {"hash": "597a3e19f6b375a86ae0a783ab00d734", "index": {"url": "z_688d3285f67a444f__format_correlation_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_correlation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_cot_py": {"hash": "e3d620de8309c2bba932639bb5b4c427", "index": {"url": "z_688d3285f67a444f__format_cot_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_cot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_cvd_py": {"hash": "1749f84cdb9ea20cb1f0ea6411903499", "index": {"url": "z_688d3285f67a444f__format_cvd_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_cvd.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_market_depth_py": {"hash": "56f6af379edc09bbedcccca71a86734b", "index": {"url": "z_688d3285f67a444f__format_market_depth_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_market_depth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_metrics_dashboard_py": {"hash": "9c62233786e67d25abd9fc417d2d2d53", "index": {"url": "z_688d3285f67a444f__format_metrics_dashboard_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_metrics_dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_order_flow_py": {"hash": "2f2da81a4c4c536289ff02221acc1d88", "index": {"url": "z_688d3285f67a444f__format_order_flow_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_order_flow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_pmi_py": {"hash": "9275620f8094a88ff1a18a486f5bc662", "index": {"url": "z_688d3285f67a444f__format_pmi_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_pmi.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_volatility_py": {"hash": "4fd8cfdbb71c28476c09edb7acd0f195", "index": {"url": "z_688d3285f67a444f__format_volatility_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_volatility.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_volume_profile_py": {"hash": "d14fb4e2029895acd7840a82c63353b6", "index": {"url": "z_688d3285f67a444f__format_volume_profile_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_volume_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f__format_vwap_py": {"hash": "0e4e6a6ed65b9077562539e0d6e1c4b9", "index": {"url": "z_688d3285f67a444f__format_vwap_py.html", "file": "src\\forex_bot\\prompt_builder\\_format_vwap.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_688d3285f67a444f_builder_py": {"hash": "9ce4bf2d9afd4ff7f0d76977949c1c0f", "index": {"url": "z_688d3285f67a444f_builder_py.html", "file": "src\\forex_bot\\prompt_builder\\builder.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_qdrant_service_py": {"hash": "53f0458886a2f1c6639b7f5c52f02e98", "index": {"url": "z_21758df60ac2cdd3_qdrant_service_py.html", "file": "src\\forex_bot\\qdrant_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 199, "n_excluded": 0, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f67347d77a2317d___init___py": {"hash": "2d0178186742e3753703666dd548b302", "index": {"url": "z_8f67347d77a2317d___init___py.html", "file": "src\\forex_bot\\regime_detector\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f67347d77a2317d_hmm_model_py": {"hash": "31aca417e140d4e86b3dccaea693c955", "index": {"url": "z_8f67347d77a2317d_hmm_model_py.html", "file": "src\\forex_bot\\regime_detector\\hmm_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 143, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_secrets_manager_py": {"hash": "7ff2d05cfced79d27302270ef23b92be", "index": {"url": "z_21758df60ac2cdd3_secrets_manager_py.html", "file": "src\\forex_bot\\secrets_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_834549f8a74d0f08___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_834549f8a74d0f08___init___py.html", "file": "src\\forex_bot\\sentiment_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_834549f8a74d0f08_analyzer_py": {"hash": "cf03ca6a7369e55d8fc95c37e05c14e7", "index": {"url": "z_834549f8a74d0f08_analyzer_py.html", "file": "src\\forex_bot\\sentiment_analyzer\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 236, "n_excluded": 0, "n_missing": 201, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_signal_generator_py": {"hash": "5b4c42814f776c445c64705c56146c90", "index": {"url": "z_21758df60ac2cdd3_signal_generator_py.html", "file": "src\\forex_bot\\signal_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 184, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_system_monitor_py": {"hash": "55338fada7b0f6e24dbf805c04d62934", "index": {"url": "z_21758df60ac2cdd3_system_monitor_py.html", "file": "src\\forex_bot\\system_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 206, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21758df60ac2cdd3_trade_executor_py": {"hash": "9b7a8ebe4bfe64f28e1ecefab4221782", "index": {"url": "z_21758df60ac2cdd3_trade_executor_py.html", "file": "src\\forex_bot\\trade_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fe2e3f5d4cdbfc97___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_fe2e3f5d4cdbfc97___init___py.html", "file": "src\\forex_bot\\trend_analyzer\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fe2e3f5d4cdbfc97_analyzer_py": {"hash": "f9c77d0d82ca8612fd04720f437850a4", "index": {"url": "z_fe2e3f5d4cdbfc97_analyzer_py.html", "file": "src\\forex_bot\\trend_analyzer\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ac753e7a34181b6___init___py": {"hash": "5260f3d5015e4f62c04848076b8eee30", "index": {"url": "z_4ac753e7a34181b6___init___py.html", "file": "src\\forex_bot\\volatility_forecaster\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ac753e7a34181b6_garch_model_py": {"hash": "16e778367ef6511eafb3cd445d0a678d", "index": {"url": "z_4ac753e7a34181b6_garch_model_py.html", "file": "src\\forex_bot\\volatility_forecaster\\garch_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca0834a3af36924___init___py": {"hash": "4b23997fc2d2138bbc7c9d7ccf3d26a8", "index": {"url": "z_bca0834a3af36924___init___py.html", "file": "src\\forex_bot\\volatility_indices\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca0834a3af36924_analyzer_py": {"hash": "fe4521c24895700785bab4d4cc69a8eb", "index": {"url": "z_bca0834a3af36924_analyzer_py.html", "file": "src\\forex_bot\\volatility_indices\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca0834a3af36924_client_py": {"hash": "5c60328e3c0fe60ada26ab15df61bff1", "index": {"url": "z_bca0834a3af36924_client_py.html", "file": "src\\forex_bot\\volatility_indices\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bca0834a3af36924_models_py": {"hash": "8c1b87104e9bb08296d8681adc35c2eb", "index": {"url": "z_bca0834a3af36924_models_py.html", "file": "src\\forex_bot\\volatility_indices\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd0688f78ad9707b___init___py": {"hash": "305ab029c800e69b5d73e1da7e225768", "index": {"url": "z_cd0688f78ad9707b___init___py.html", "file": "src\\forex_bot\\volume_profile\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd0688f78ad9707b_calculator_py": {"hash": "79541c06b565f72383a62a4716e1dd38", "index": {"url": "z_cd0688f78ad9707b_calculator_py.html", "file": "src\\forex_bot\\volume_profile\\calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd0688f78ad9707b_client_py": {"hash": "da134036a304a1703c7818e568c854ff", "index": {"url": "z_cd0688f78ad9707b_client_py.html", "file": "src\\forex_bot\\volume_profile\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd0688f78ad9707b_models_py": {"hash": "060663f65eaf1feb1e2eaa0ba29e16cb", "index": {"url": "z_cd0688f78ad9707b_models_py.html", "file": "src\\forex_bot\\volume_profile\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cd0688f78ad9707b_visualizer_py": {"hash": "7c3080f709c72844cad171eb3c2aff58", "index": {"url": "z_cd0688f78ad9707b_visualizer_py.html", "file": "src\\forex_bot\\volume_profile\\visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c02a38ddd1d4314___init___py": {"hash": "e10c64f98799ced7103b130381226f57", "index": {"url": "z_1c02a38ddd1d4314___init___py.html", "file": "src\\forex_bot\\vwap\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c02a38ddd1d4314_calculator_py": {"hash": "47797bd110463a88d6c5889d1d434b15", "index": {"url": "z_1c02a38ddd1d4314_calculator_py.html", "file": "src\\forex_bot\\vwap\\calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 135, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c02a38ddd1d4314_client_py": {"hash": "bbd7b089e8a520319c748368b6eea027", "index": {"url": "z_1c02a38ddd1d4314_client_py.html", "file": "src\\forex_bot\\vwap\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1c02a38ddd1d4314_models_py": {"hash": "c2addabbbab3e548532793b750468ba0", "index": {"url": "z_1c02a38ddd1d4314_models_py.html", "file": "src\\forex_bot\\vwap\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "temp_config_py": {"hash": "********************************", "index": {"url": "temp_config_py.html", "file": "temp_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_coverage_py": {"hash": "0432974c381dd621d8a48c8858163a01", "index": {"url": "test_coverage_py.html", "file": "test_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_report_py": {"hash": "87bbaa657cf265d87a0096d9d3be70c0", "index": {"url": "test_report_py.html", "file": "test_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 80, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531___init___py": {"hash": "f1ee423c2854b48bd9a60b23c68783fd", "index": {"url": "z_a44f0ac069e85531___init___py.html", "file": "tests\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_32cd21b2522f1423___init___py": {"hash": "7786bcdbe718ba1e83a9431d8d9b1388", "index": {"url": "z_32cd21b2522f1423___init___py.html", "file": "tests\\aaa_framework\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_32cd21b2522f1423_base_test_py": {"hash": "8efbfe6e19d908df4ed6d014ef186d8a", "index": {"url": "z_32cd21b2522f1423_base_test_py.html", "file": "tests\\aaa_framework\\base_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_32cd21b2522f1423_pytest_plugin_py": {"hash": "6c923576ad18c7837b72b34614de6657", "index": {"url": "z_32cd21b2522f1423_pytest_plugin_py.html", "file": "tests\\aaa_framework\\pytest_plugin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_32cd21b2522f1423_reporting_py": {"hash": "69d26e254fed35422583e01793fb1c27", "index": {"url": "z_32cd21b2522f1423_reporting_py.html", "file": "tests\\aaa_framework\\reporting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6d1b4bcb76938033___init___py": {"hash": "f48899cdf7bf8cb140ddd12f971d2c9a", "index": {"url": "z_6d1b4bcb76938033___init___py.html", "file": "tests\\backtester\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6d1b4bcb76938033_test_backtester_service_py": {"hash": "51abe1faf19dc94e299a729695dc7fd6", "index": {"url": "z_6d1b4bcb76938033_test_backtester_service_py.html", "file": "tests\\backtester\\test_backtester_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6d1b4bcb76938033_test_order_simulator_py": {"hash": "eae9b9956bd83b2adb877d68826233f9", "index": {"url": "z_6d1b4bcb76938033_test_order_simulator_py.html", "file": "tests\\backtester\\test_order_simulator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_conftest_py": {"hash": "34e7372ce63fafc94ab120c1779f1d93", "index": {"url": "z_a44f0ac069e85531_conftest_py.html", "file": "tests\\conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_decodetest_py": {"hash": "4380466f39b591449ea84f01ad5f14bb", "index": {"url": "z_a44f0ac069e85531_decodetest_py.html", "file": "tests\\decodetest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f___init___py": {"hash": "b3d81314aaa21bbac2b2c7cf8fffaae5", "index": {"url": "z_c5c97be05d9be59f___init___py.html", "file": "tests\\event_bus\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_conftest_py": {"hash": "085cd17613a9cfc8d3beb6a238d6c835", "index": {"url": "z_c5c97be05d9be59f_conftest_py.html", "file": "tests\\event_bus\\conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_config_py": {"hash": "2888af2ce9a83407faaae41b0e20a0bc", "index": {"url": "z_c5c97be05d9be59f_test_config_py.html", "file": "tests\\event_bus\\test_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 457, "n_excluded": 0, "n_missing": 359, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_config_coverage_py": {"hash": "ab7b48b2d0a1ddedc61a2d4531bd9be5", "index": {"url": "z_c5c97be05d9be59f_test_config_coverage_py.html", "file": "tests\\event_bus\\test_config_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_config_direct_py": {"hash": "44d07e47e2a042fc3efcf88391869938", "index": {"url": "z_c5c97be05d9be59f_test_config_direct_py.html", "file": "tests\\event_bus\\test_config_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 255, "n_excluded": 0, "n_missing": 229, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_config_final_py": {"hash": "cfce4f6d3e2c6b2f5e888e1089118155", "index": {"url": "z_c5c97be05d9be59f_test_config_final_py.html", "file": "tests\\event_bus\\test_config_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_config_isolated_py": {"hash": "91cf1d9f3015f1e28136f33aa5bd9cf2", "index": {"url": "z_c5c97be05d9be59f_test_config_isolated_py.html", "file": "tests\\event_bus\\test_config_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 294, "n_excluded": 0, "n_missing": 283, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_consumer_py": {"hash": "ba03502c8418e7f6256331827539bed5", "index": {"url": "z_c5c97be05d9be59f_test_consumer_py.html", "file": "tests\\event_bus\\test_consumer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 197, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_event_schemas_py": {"hash": "64a4dc4102fc7e4b4558af8d7e9e2b4d", "index": {"url": "z_c5c97be05d9be59f_test_event_schemas_py.html", "file": "tests\\event_bus\\test_event_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5c97be05d9be59f_test_producer_py": {"hash": "3cee6dfe076993a581fa5154fcac24a2", "index": {"url": "z_c5c97be05d9be59f_test_producer_py.html", "file": "tests\\event_bus\\test_producer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33b75668da6b6a11___init___py": {"hash": "9f62c8ee8b7230e87e448bc7e6ad214c", "index": {"url": "z_33b75668da6b6a11___init___py.html", "file": "tests\\integration\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33b75668da6b6a11_test_event_bus_config_integration_py": {"hash": "5c9ec7024a4de00ccbf664d525298bb5", "index": {"url": "z_33b75668da6b6a11_test_event_bus_config_integration_py.html", "file": "tests\\integration\\test_event_bus_config_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_measure_coverage_py": {"hash": "ec7f53c5892c3638ec6f18ab8837f9fa", "index": {"url": "z_a44f0ac069e85531_measure_coverage_py.html", "file": "tests\\measure_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_89bcb3827b927724___init___py": {"hash": "449097e47e56123fdb6c258b87dcace3", "index": {"url": "z_89bcb3827b927724___init___py.html", "file": "tests\\metrics\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_89bcb3827b927724_test_otel_tracing_py": {"hash": "cdf97ff14fd65893833f3f156b15f318", "index": {"url": "z_89bcb3827b927724_test_otel_tracing_py.html", "file": "tests\\metrics\\test_otel_tracing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_89bcb3827b927724_test_prometheus_metrics_py": {"hash": "6622bce34ba55316fab8ead755c9a5ed", "index": {"url": "z_89bcb3827b927724_test_prometheus_metrics_py.html", "file": "tests\\metrics\\test_prometheus_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_mock_modules_py": {"hash": "3d89d9afeb6c5f6041e7b10ba1e917ff", "index": {"url": "z_a44f0ac069e85531_mock_modules_py.html", "file": "tests\\mock_modules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_temp_config_py": {"hash": "********************************", "index": {"url": "z_a44f0ac069e85531_temp_config_py.html", "file": "tests\\temp_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_aaa_pattern_example_py": {"hash": "3c86e9454e3825e3f5c77367f9e3f1c1", "index": {"url": "z_a44f0ac069e85531_test_aaa_pattern_example_py.html", "file": "tests\\test_aaa_pattern_example.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_aaa_sample_py": {"hash": "94a8fa6d23e9b9ca621bd487274068fc", "index": {"url": "z_a44f0ac069e85531_test_aaa_sample_py.html", "file": "tests\\test_aaa_sample.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_py": {"hash": "6f8dba3c3f85697750c2b65f4e0f55ef", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_py.html", "file": "tests\\test_bot_orchestrator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 126, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_py": {"hash": "f22c042ca1b6eec65339816c900373f4", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_py.html", "file": "tests\\test_bot_orchestrator_70_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part2_py": {"hash": "65575c046e74db96f5ff05bd079382cb", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part2_py.html", "file": "tests\\test_bot_orchestrator_70_coverage_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part3_py": {"hash": "87b498b491eefc3512ee9fc415eeab1f", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part3_py.html", "file": "tests\\test_bot_orchestrator_70_coverage_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 323, "n_excluded": 0, "n_missing": 247, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part4_py": {"hash": "2cf2588e27e908ce063c7285bfec0b9c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part4_py.html", "file": "tests\\test_bot_orchestrator_70_coverage_part4.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 382, "n_excluded": 0, "n_missing": 265, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part5_py": {"hash": "c8a57af6dcb31e9e9458592f3df8d105", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_70_coverage_part5_py.html", "file": "tests\\test_bot_orchestrator_70_coverage_part5.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 439, "n_excluded": 0, "n_missing": 333, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_80_coverage_py": {"hash": "086078e313ebf9049bac2d653cd0c852", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_80_coverage_py.html", "file": "tests\\test_bot_orchestrator_80_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 337, "n_excluded": 0, "n_missing": 234, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_py": {"hash": "b99f7314a4b2ff4c8ff3a3d816bab4e4", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_py.html", "file": "tests\\test_bot_orchestrator_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 317, "n_excluded": 0, "n_missing": 221, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part1_py": {"hash": "0263a6d2608ec10265d7a0bc82dbb0ab", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part1_py.html", "file": "tests\\test_bot_orchestrator_90_coverage_part1.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part2_py": {"hash": "9c7d6d9520555cfe138878f3f59ca814", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part2_py.html", "file": "tests\\test_bot_orchestrator_90_coverage_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part3_py": {"hash": "fc2858521a71cb9b96057505d325de83", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part3_py.html", "file": "tests\\test_bot_orchestrator_90_coverage_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part4_py": {"hash": "760646c96710cdfd0bd1f21898ffa92b", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part4_py.html", "file": "tests\\test_bot_orchestrator_90_coverage_part4.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part5_py": {"hash": "8e8904fda0ff2ab04ef4d86b4977dd1d", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_90_coverage_part5_py.html", "file": "tests\\test_bot_orchestrator_90_coverage_part5.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_py": {"hash": "f7279aa508ff982d91a1af925cc1022c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_py.html", "file": "tests\\test_bot_orchestrator_check_closed_trades.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_comprehensive_py": {"hash": "4da35c25a174a8a3685eac835a26b02a", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_comprehensive_py.html", "file": "tests\\test_bot_orchestrator_check_closed_trades_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 239, "n_excluded": 0, "n_missing": 205, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_edge_cases_py": {"hash": "a3919d29d5d008dc647cc67580684195", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_edge_cases_py.html", "file": "tests\\test_bot_orchestrator_check_closed_trades_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_py": {"hash": "56506ea3171485cfc70823e816d8cead", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_py.html", "file": "tests\\test_bot_orchestrator_check_closed_trades_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_2_py": {"hash": "cb5361ad6c5febf5c919b8292545c416", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_check_closed_trades_simple_2_py.html", "file": "tests\\test_bot_orchestrator_check_closed_trades_simple_2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_py": {"hash": "0c085535e045a1d8282ffe9685982f75", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_py.html", "file": "tests\\test_bot_orchestrator_close_positions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_func_py": {"hash": "6d5f5f1f0ffcdaff17daf3dfaba3e608", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_func_py.html", "file": "tests\\test_bot_orchestrator_close_positions_func.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_simple_py": {"hash": "d354786fffb3adafeaef32336ed44f4c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_simple_py.html", "file": "tests\\test_bot_orchestrator_close_positions_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_wrapper_py": {"hash": "745f3311a1b8709d0c46961d499ef6fe", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_close_positions_wrapper_py.html", "file": "tests\\test_bot_orchestrator_close_positions_wrapper.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_closed_trades_py": {"hash": "1e82e4ea327286239fd44321ebb27dec", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_closed_trades_py.html", "file": "tests\\test_bot_orchestrator_closed_trades.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_combined_py": {"hash": "7885d3f39959a116b3c4141c550cb3c3", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_combined_py.html", "file": "tests\\test_bot_orchestrator_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_combined_coverage_py": {"hash": "0733668939b68f7b4a4536607b8b3525", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_combined_coverage_py.html", "file": "tests\\test_bot_orchestrator_combined_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_comprehensive_py": {"hash": "90174b31e6044a06a0f62432693eeead", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_comprehensive_py.html", "file": "tests\\test_bot_orchestrator_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_direct_py": {"hash": "beb2bcbc562ff3662abd18b26cdb0883", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_direct_py.html", "file": "tests\\test_bot_orchestrator_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_edge_cases_py": {"hash": "d4bb5d1c58d44c6f4ef3ed5418781490", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_edge_cases_py.html", "file": "tests\\test_bot_orchestrator_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_error_handling_py": {"hash": "ebb097ed1dd098dccc8e891a9cec47f2", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_error_handling_py.html", "file": "tests\\test_bot_orchestrator_error_handling.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_functions_py": {"hash": "d555ec4896d3f4dd2e901c0b98c616b2", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_functions_py.html", "file": "tests\\test_bot_orchestrator_functions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 79, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_py": {"hash": "7e456dd1371b9a630351eedb5f2efe5c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_py.html", "file": "tests\\test_bot_orchestrator_get_perf_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_simple_py": {"hash": "1f3a1bc90f0bc823d5c7deb5fa8386d4", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_get_perf_summary_simple_py.html", "file": "tests\\test_bot_orchestrator_get_perf_summary_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_get_recent_performance_summary_py": {"hash": "79347c00e6f137e471be8c53db725fe5", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_get_recent_performance_summary_py.html", "file": "tests\\test_bot_orchestrator_get_recent_performance_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_helpers_py": {"hash": "c3fd1fa95bdea4de1b55878a1edad5ca", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_helpers_py.html", "file": "tests\\test_bot_orchestrator_helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 288, "n_excluded": 0, "n_missing": 221, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_init_py": {"hash": "35aad71eab6d6bbe5c6c4422fe3cbc3c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_init_py.html", "file": "tests\\test_bot_orchestrator_init.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_isolated_py": {"hash": "5c668762490a57e8f02adf0c9bb066cc", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_isolated_py.html", "file": "tests\\test_bot_orchestrator_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_load_env_variables_py": {"hash": "76c4ec8fb59e16157fecc21b88ae87ee", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_load_env_variables_py.html", "file": "tests\\test_bot_orchestrator_load_env_variables.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_load_perf_data_py": {"hash": "951a8319fe26bf296d0f5831915f3bd5", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_load_perf_data_py.html", "file": "tests\\test_bot_orchestrator_load_perf_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_load_performance_data_simple_py": {"hash": "c697a61a3acb18629fa85b91dd2a3ffa", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_load_performance_data_simple_py.html", "file": "tests\\test_bot_orchestrator_load_performance_data_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_main_py": {"hash": "dd9ac7a27055fe23057e82551f1db689", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_main_py.html", "file": "tests\\test_bot_orchestrator_main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_main_loop_py": {"hash": "bc1f84f35533bb6d171d853fa5ccae73", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_main_loop_py.html", "file": "tests\\test_bot_orchestrator_main_loop.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 170, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_mock_py": {"hash": "dd9c9c9c1b3fafab224a7dfcb0805e2a", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_mock_py.html", "file": "tests\\test_bot_orchestrator_mock.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_mocked_py": {"hash": "6b1ce715ff74ab3b90008f29544e7adb", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_mocked_py.html", "file": "tests\\test_bot_orchestrator_mocked.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_optional_modules_py": {"hash": "57c7721f5384414f92df5c0888af96e1", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_optional_modules_py.html", "file": "tests\\test_bot_orchestrator_optional_modules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_perf_summary_py": {"hash": "d78c801202ed9150b47e6a1397be7fc2", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_perf_summary_py.html", "file": "tests\\test_bot_orchestrator_perf_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_performance_summary_py": {"hash": "0c5780a561068b7c2a3ffa80adf6efbc", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_performance_summary_py.html", "file": "tests\\test_bot_orchestrator_performance_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_py": {"hash": "33a6f4885b37fc7805f0b49425ee9033", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_py.html", "file": "tests\\test_bot_orchestrator_run_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_comprehensive_py": {"hash": "fd088924a2d8276a1a3c51003d221699", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_comprehensive_py.html", "file": "tests\\test_bot_orchestrator_run_bot_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_fixed_py": {"hash": "b73350d15b149efc47899e8b1688507d", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_fixed_py.html", "file": "tests\\test_bot_orchestrator_run_bot_fixed.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_function_py": {"hash": "0199284ea83bd2ef725f19bd061df3e6", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_function_py.html", "file": "tests\\test_bot_orchestrator_run_bot_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_py": {"hash": "da90323a26af0f456dc1be1259d76465", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_coverage_py": {"hash": "a4f899215024213866e7f32f65e51357", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_coverage_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 463, "n_excluded": 0, "n_missing": 356, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_py": {"hash": "7db9a174b506b3621550904a8363b9fa", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 68, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_2_py": {"hash": "eb1d74866f5cfceb2fd9dd7139199659", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_edge_cases_2_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_edge_cases_2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_function_py": {"hash": "413633add818b2a9fe91f7b395457d2a", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_function_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_simple_py": {"hash": "123e9f88e6b4aa155ea7950c4a8cc24e", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_simple_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_specific_py": {"hash": "e412f337fb444d35a1a7d365301c3254", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_once_specific_py.html", "file": "tests\\test_bot_orchestrator_run_bot_once_specific.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_simple_py": {"hash": "ae313b55fd18299328c8bde4cc0c3e7d", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_bot_simple_py.html", "file": "tests\\test_bot_orchestrator_run_bot_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_run_once_py": {"hash": "8b4159bb6acd1bcd2bd0ff36d66a1bd4", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_run_once_py.html", "file": "tests\\test_bot_orchestrator_run_once.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 310, "n_excluded": 0, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_signal_execution_py": {"hash": "816290ba2c736f886fb6c48927429c84", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_signal_execution_py.html", "file": "tests\\test_bot_orchestrator_signal_execution.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_signal_generation_py": {"hash": "60b7f50d863def7f774bd11c76e71800", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_signal_generation_py.html", "file": "tests\\test_bot_orchestrator_signal_generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 125, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_signals_py": {"hash": "84971395998b93dabc62e023baa87435", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_signals_py.html", "file": "tests\\test_bot_orchestrator_signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 299, "n_excluded": 0, "n_missing": 222, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_simple_py": {"hash": "85a8124a817b7de400dacd0d351f4f47", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_simple_py.html", "file": "tests\\test_bot_orchestrator_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_simple_70_py": {"hash": "8d3ec92c54beda33343ad816c954f446", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_simple_70_py.html", "file": "tests\\test_bot_orchestrator_simple_70.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 244, "n_excluded": 0, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_simple_functions_py": {"hash": "4b9c13ab8070a2cfea90ef062cf38cfe", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_simple_functions_py.html", "file": "tests\\test_bot_orchestrator_simple_functions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_specific_functions_py": {"hash": "b852207cc86e0bed526d8ea16edd175f", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_specific_functions_py.html", "file": "tests\\test_bot_orchestrator_specific_functions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_trade_context_py": {"hash": "aa54ef26132aa4a3f3dfeb313a9d4b0c", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_trade_context_py.html", "file": "tests\\test_bot_orchestrator_trade_context.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_py": {"hash": "79076c63e78ac687f66dae40fef1ba2e", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_py.html", "file": "tests\\test_bot_orchestrator_upload_log.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_file_py": {"hash": "ac1747a283ea433cc406f85d0935bd1b", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_file_py.html", "file": "tests\\test_bot_orchestrator_upload_log_file.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_function_py": {"hash": "79076c63e78ac687f66dae40fef1ba2e", "index": {"url": "z_a44f0ac069e85531_test_bot_orchestrator_upload_log_function_py.html", "file": "tests\\test_bot_orchestrator_upload_log_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_calculate_indicators_py": {"hash": "fc66d2615fe4ebca8622c803703adb78", "index": {"url": "z_a44f0ac069e85531_test_calculate_indicators_py.html", "file": "tests\\test_calculate_indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 0, "n_missing": 135, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_check_and_log_closed_trades_py": {"hash": "e54c15e9262e87a6ecf8f14deb2b353f", "index": {"url": "z_a44f0ac069e85531_test_check_and_log_closed_trades_py.html", "file": "tests\\test_check_and_log_closed_trades.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 272, "n_excluded": 0, "n_missing": 266, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_close_existing_positions_py": {"hash": "6eeab0e713eef7189a3193754140817e", "index": {"url": "z_a44f0ac069e85531_test_close_existing_positions_py.html", "file": "tests\\test_close_existing_positions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_combined_analyzers_py": {"hash": "cad7dc7b1238f05b7c1d83296620e10e", "index": {"url": "z_a44f0ac069e85531_test_combined_analyzers_py.html", "file": "tests\\test_combined_analyzers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_combined_coverage_py": {"hash": "88c953523fba3a0cb842cdb7d1999508", "index": {"url": "z_a44f0ac069e85531_test_combined_coverage_py.html", "file": "tests\\test_combined_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_py": {"hash": "3296643ee416b3cf220d12ca2504688c", "index": {"url": "z_a44f0ac069e85531_test_config_loader_py.html", "file": "tests\\test_config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 126, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_100_coverage_py": {"hash": "715f97bd5bab780c10e6672e4ea63116", "index": {"url": "z_a44f0ac069e85531_test_config_loader_100_coverage_py.html", "file": "tests\\test_config_loader_100_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_87_to_90_coverage_py": {"hash": "69af74f9f9bcbbaefe24d6809ca8c8ee", "index": {"url": "z_a44f0ac069e85531_test_config_loader_87_to_90_coverage_py.html", "file": "tests\\test_config_loader_87_to_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_90_coverage_py": {"hash": "e1e6a21c4127a4ce48c6be601bba4d28", "index": {"url": "z_a44f0ac069e85531_test_config_loader_90_coverage_py.html", "file": "tests\\test_config_loader_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 181, "n_excluded": 0, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_94_to_100_coverage_py": {"hash": "92e0d384e6f8cab7189bdc6022bf813d", "index": {"url": "z_a44f0ac069e85531_test_config_loader_94_to_100_coverage_py.html", "file": "tests\\test_config_loader_94_to_100_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_95_percent_py": {"hash": "4c580d2c1a8e5fe76227fbb534a9b331", "index": {"url": "z_a44f0ac069e85531_test_config_loader_95_percent_py.html", "file": "tests\\test_config_loader_95_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_additional_py": {"hash": "cea92ab42908292bcb755f54113a22ab", "index": {"url": "z_a44f0ac069e85531_test_config_loader_additional_py.html", "file": "tests\\test_config_loader_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_batch3_90_coverage_py": {"hash": "032df735b9d4334303bc8ea9bd17ee24", "index": {"url": "z_a44f0ac069e85531_test_config_loader_batch3_90_coverage_py.html", "file": "tests\\test_config_loader_batch3_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_batch5_90_coverage_py": {"hash": "b837b0136a3224a9dc9ec19c84557197", "index": {"url": "z_a44f0ac069e85531_test_config_loader_batch5_90_coverage_py.html", "file": "tests\\test_config_loader_batch5_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 221, "n_excluded": 0, "n_missing": 188, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_batch6_90_coverage_py": {"hash": "335384b1c2b80972e22946ae8e4530d3", "index": {"url": "z_a44f0ac069e85531_test_config_loader_batch6_90_coverage_py.html", "file": "tests\\test_config_loader_batch6_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_final_90_coverage_py": {"hash": "e3798e23ab7ccb057b834a0c64ba5a76", "index": {"url": "z_a44f0ac069e85531_test_config_loader_final_90_coverage_py.html", "file": "tests\\test_config_loader_final_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 243, "n_excluded": 0, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_final_coverage_py": {"hash": "8feaa0cd741979858da1087784a3101a", "index": {"url": "z_a44f0ac069e85531_test_config_loader_final_coverage_py.html", "file": "tests\\test_config_loader_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_loader_simple_95_py": {"hash": "f39626580e11b7456fd81956921829ec", "index": {"url": "z_a44f0ac069e85531_test_config_loader_simple_95_py.html", "file": "tests\\test_config_loader_simple_95.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_py": {"hash": "f16572c04e85c2f2560c46449ee5b92f", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_py.html", "file": "tests\\test_correlation_matrix.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506___init___py": {"hash": "9294e3e0da6a7d612d22f1354c39b8fa", "index": {"url": "z_f0ebcc3a63e8c506___init___py.html", "file": "tests\\test_correlation_matrix\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_calculator_py": {"hash": "aa33f103d23b2491485b1679b246afa8", "index": {"url": "z_f0ebcc3a63e8c506_test_calculator_py.html", "file": "tests\\test_correlation_matrix\\test_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_calculator_part2_py": {"hash": "f8fe6d9e76f385f62b2f10786629aa5d", "index": {"url": "z_f0ebcc3a63e8c506_test_calculator_part2_py.html", "file": "tests\\test_correlation_matrix\\test_calculator_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_calculator_part3_py": {"hash": "2c87b2ad26b924c31f185e5bdb9cbb2a", "index": {"url": "z_f0ebcc3a63e8c506_test_calculator_part3_py.html", "file": "tests\\test_correlation_matrix\\test_calculator_part3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_calculator_part3_copy_py": {"hash": "5e8c49d6f89fc6cf1b3fa511e692ba72", "index": {"url": "z_f0ebcc3a63e8c506_test_calculator_part3_copy_py.html", "file": "tests\\test_correlation_matrix\\test_calculator_part3_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 244, "n_excluded": 0, "n_missing": 193, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_client_py": {"hash": "ae35f50a8f2d0185c318fcdb49b42356", "index": {"url": "z_f0ebcc3a63e8c506_test_client_py.html", "file": "tests\\test_correlation_matrix\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_client_copy_py": {"hash": "24012d9dff734725029edbe8431592ff", "index": {"url": "z_f0ebcc3a63e8c506_test_client_copy_py.html", "file": "tests\\test_correlation_matrix\\test_client_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 286, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_models_py": {"hash": "0630d4a8ae070aec273249c625e1e699", "index": {"url": "z_f0ebcc3a63e8c506_test_models_py.html", "file": "tests\\test_correlation_matrix\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_models_copy_py": {"hash": "f402b79e70074eab5fcecd99d85ce6d3", "index": {"url": "z_f0ebcc3a63e8c506_test_models_copy_py.html", "file": "tests\\test_correlation_matrix\\test_models_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_models_part2_py": {"hash": "5979bca390dbcaa1318644d59b0a5e13", "index": {"url": "z_f0ebcc3a63e8c506_test_models_part2_py.html", "file": "tests\\test_correlation_matrix\\test_models_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_models_part2_copy_py": {"hash": "7fcfc516351ae493cfd7603d43249bde", "index": {"url": "z_f0ebcc3a63e8c506_test_models_part2_copy_py.html", "file": "tests\\test_correlation_matrix\\test_models_part2_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_py": {"hash": "b140c755cb1a44198058abcb082d29b5", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_py": {"hash": "526cee4fec68c2d080637697561ee3e7", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 130, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_copy_py": {"hash": "e92a3b4e7166c184ef46fd36e0bf267d", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_copy_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 313, "n_excluded": 0, "n_missing": 257, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_direct_py": {"hash": "d99033f0ac9912db35486578f2d7a5bc", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_direct_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_functions_py": {"hash": "60812ab9aba86b2138ea91500e46cb7d", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_functions_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_functions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_isolated_py": {"hash": "2392936d7fc45da18de8cdb05fddaca8", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_isolated_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 313, "n_excluded": 0, "n_missing": 257, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_mock_py": {"hash": "85e052c89559b68c2d98ca4cb08c2fb1", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_mock_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_mock.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_mock_new_py": {"hash": "27a29cfafa2c5e060edc03cd5dc64aa6", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_mock_new_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_mock_new.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_part2_simple_py": {"hash": "ba696b300f78c42d5de667ac670f8d7c", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_part2_simple_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_part2_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_test_visualizer_simple_py": {"hash": "db0c4b173deccbe7f2f7740627d1d34b", "index": {"url": "z_f0ebcc3a63e8c506_test_visualizer_simple_py.html", "file": "tests\\test_correlation_matrix\\test_visualizer_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0ebcc3a63e8c506_visualizer_part2_copy_py": {"hash": "52a306a20076f77f3f4145a47c5af759", "index": {"url": "z_f0ebcc3a63e8c506_visualizer_part2_copy_py.html", "file": "tests\\test_correlation_matrix\\visualizer_part2_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_comprehensive_py": {"hash": "18f6a9c7aba57dd2df9836fa4e0e4f62", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_comprehensive_py.html", "file": "tests\\test_correlation_matrix_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_event_bus_integration_py": {"hash": "729f8bed687d048af94c9888a7ccdbd5", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_event_bus_integration_py.html", "file": "tests\\test_correlation_matrix_event_bus_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_init_batch10_90_coverage_py": {"hash": "56d2c092e68e0f95503559847ef81f70", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_init_batch10_90_coverage_py.html", "file": "tests\\test_correlation_matrix_init_batch10_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_init_batch5_90_coverage_py": {"hash": "e36ae9dadd20c0722f3f5baab1e48714", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_init_batch5_90_coverage_py.html", "file": "tests\\test_correlation_matrix_init_batch5_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_100_percent_py": {"hash": "7a93520bdfbcb2c4c49b1db1ea29acc2", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_100_percent_py.html", "file": "tests\\test_correlation_matrix_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_py": {"hash": "cc05caf4dee812429e7ca57c26eb7173", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_py.html", "file": "tests\\test_correlation_matrix_models_60_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_corrected_py": {"hash": "d678c3dbd10a3c377275501cc18de17e", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_60_percent_corrected_py.html", "file": "tests\\test_correlation_matrix_models_60_percent_corrected.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_enhanced_py": {"hash": "00b4f21e024f877975d905b027f2f404", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_enhanced_py.html", "file": "tests\\test_correlation_matrix_models_enhanced.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_phase3_py": {"hash": "adf4b7c6069233e35e99b6b4b6c36715", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_phase3_py.html", "file": "tests\\test_correlation_matrix_models_phase3.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_phase5_clean_py": {"hash": "0b8ab303271cfa00f61b7a91465779d8", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_phase5_clean_py.html", "file": "tests\\test_correlation_matrix_models_phase5_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_phase5_final_py": {"hash": "e74f2798bbb1027f8d535433eb2ba7d4", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_phase5_final_py.html", "file": "tests\\test_correlation_matrix_models_phase5_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_phase5m_py": {"hash": "cdf7d40d90901a38e8e7c62bb9131386", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_phase5m_py.html", "file": "tests\\test_correlation_matrix_models_phase5m.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 0, "n_missing": 169, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_models_phase6a_simple_py": {"hash": "9cc4052eb6043852245c9a4fb733f2ae", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_models_phase6a_simple_py.html", "file": "tests\\test_correlation_matrix_models_phase6a_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_correlation_matrix_structure_py": {"hash": "4d5a21328c1b0f15b11db75cb193a96e", "index": {"url": "z_a44f0ac069e85531_test_correlation_matrix_structure_py.html", "file": "tests\\test_correlation_matrix_structure.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac729262877445e___init___py": {"hash": "0f81b925fc02d9e9daec48201716eb90", "index": {"url": "z_6ac729262877445e___init___py.html", "file": "tests\\test_cot_reports\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac729262877445e_test_analyzer_py": {"hash": "1931ddd8b7bf33c288e391071bea1e66", "index": {"url": "z_6ac729262877445e_test_analyzer_py.html", "file": "tests\\test_cot_reports\\test_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac729262877445e_test_client_py": {"hash": "bab2534c8db117552090da783386f4e0", "index": {"url": "z_6ac729262877445e_test_client_py.html", "file": "tests\\test_cot_reports\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac729262877445e_test_models_py": {"hash": "605280a304997fcef76b919ddfb55ea9", "index": {"url": "z_6ac729262877445e_test_models_py.html", "file": "tests\\test_cot_reports\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cot_reports_models_100_percent_py": {"hash": "fe871aedacc19445841ee677d80d7c01", "index": {"url": "z_a44f0ac069e85531_test_cot_reports_models_100_percent_py.html", "file": "tests\\test_cot_reports_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cot_reports_models_batch9_90_coverage_py": {"hash": "9638ca8d24def1c07b2d5eeee5b0e355", "index": {"url": "z_a44f0ac069e85531_test_cot_reports_models_batch9_90_coverage_py.html", "file": "tests\\test_cot_reports_models_batch9_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cot_reports_models_phase5_simple_py": {"hash": "a5999dfc49dd4b2a4ee36a2a5d505390", "index": {"url": "z_a44f0ac069e85531_test_cot_reports_models_phase5_simple_py.html", "file": "tests\\test_cot_reports_models_phase5_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ccb7ed9c01d1cfe0___init___py": {"hash": "e285c6d51368d17e049e95b4aadc53d6", "index": {"url": "z_ccb7ed9c01d1cfe0___init___py.html", "file": "tests\\test_cvd\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ccb7ed9c01d1cfe0_test_calculator_py": {"hash": "71d3c5dbe0febff05291e0af566028b1", "index": {"url": "z_ccb7ed9c01d1cfe0_test_calculator_py.html", "file": "tests\\test_cvd\\test_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ccb7ed9c01d1cfe0_test_client_py": {"hash": "061d5ec14c54847d81e27e0caea10bfb", "index": {"url": "z_ccb7ed9c01d1cfe0_test_client_py.html", "file": "tests\\test_cvd\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ccb7ed9c01d1cfe0_test_models_py": {"hash": "ad7b7fbcb883c5644c8c25edd2a1fd2a", "index": {"url": "z_ccb7ed9c01d1cfe0_test_models_py.html", "file": "tests\\test_cvd\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cvd_init_batch7_90_coverage_py": {"hash": "b0494809af75c63551fbd4e050660edf", "index": {"url": "z_a44f0ac069e85531_test_cvd_init_batch7_90_coverage_py.html", "file": "tests\\test_cvd_init_batch7_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 203, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cvd_models_100_percent_py": {"hash": "16011e664eb21fe4f9378d5a145cb6f1", "index": {"url": "z_a44f0ac069e85531_test_cvd_models_100_percent_py.html", "file": "tests\\test_cvd_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cvd_models_73_to_90_coverage_py": {"hash": "c0974a00764a4a9f1c31547e825c4020", "index": {"url": "z_a44f0ac069e85531_test_cvd_models_73_to_90_coverage_py.html", "file": "tests\\test_cvd_models_73_to_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cvd_models_batch4_90_coverage_py": {"hash": "78de5d4317af72f481d69a53d1398c58", "index": {"url": "z_a44f0ac069e85531_test_cvd_models_batch4_90_coverage_py.html", "file": "tests\\test_cvd_models_batch4_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_cvd_models_phase5f_py": {"hash": "6b53f7b091b0a98c2bbceaf8cdfcb9a3", "index": {"url": "z_a44f0ac069e85531_test_cvd_models_phase5f_py.html", "file": "tests\\test_cvd_models_phase5f.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_direct_import_py": {"hash": "29a3dceb48b234beef49aca32a391d32", "index": {"url": "z_a44f0ac069e85531_test_direct_import_py.html", "file": "tests\\test_direct_import.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_enhanced_metrics_dashboard_py": {"hash": "7e946d9bba67e554d2b856673b1796b0", "index": {"url": "z_a44f0ac069e85531_test_enhanced_metrics_dashboard_py.html", "file": "tests\\test_enhanced_metrics_dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 0, "n_missing": 202, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_analysis_consumer_py": {"hash": "1b54a498ce26d544277065f577cdf58c", "index": {"url": "z_a44f0ac069e85531_test_event_bus_analysis_consumer_py.html", "file": "tests\\test_event_bus_analysis_consumer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_basic_py": {"hash": "183d5283615205791e5623fa976e32c9", "index": {"url": "z_a44f0ac069e85531_test_event_bus_basic_py.html", "file": "tests\\test_event_bus_basic.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_comprehensive_py": {"hash": "28adc89b86123cf6150081e00debd35e", "index": {"url": "z_a44f0ac069e85531_test_event_bus_comprehensive_py.html", "file": "tests\\test_event_bus_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 0, "n_missing": 130, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_py": {"hash": "826065ce847189be75c53ad618ff8ce6", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_py.html", "file": "tests\\test_event_bus_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_90_percent_py": {"hash": "dc6ef765ec95b3852c396d34d16bd75c", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_90_percent_py.html", "file": "tests\\test_event_bus_config_90_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_basic_py": {"hash": "0f76a3c51b90f4c40e2b05e6033c28bd", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_basic_py.html", "file": "tests\\test_event_bus_config_basic.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_coverage_py": {"hash": "54a6c42d45847fcd60737e933073d9bf", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_coverage_py.html", "file": "tests\\test_event_bus_config_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 214, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_direct_py": {"hash": "21f57ce348935bb7c498e88d44a6f607", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_direct_py.html", "file": "tests\\test_event_bus_config_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_focused_py": {"hash": "cc7e68535e73dc875515825bcce20bee", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_focused_py.html", "file": "tests\\test_event_bus_config_focused.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_isolated_py": {"hash": "9e9d666b717ba0d8cc44efab8aa78287", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_isolated_py.html", "file": "tests\\test_event_bus_config_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 278, "n_excluded": 0, "n_missing": 130, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_minimal_py": {"hash": "cec4f2297deec2f0e3d0b7f64b90d71c", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_minimal_py.html", "file": "tests\\test_event_bus_config_minimal.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_pytest_py": {"hash": "fc76fe9fe9a0f90e2a2839e07bdbc8e2", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_pytest_py.html", "file": "tests\\test_event_bus_config_pytest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 237, "n_excluded": 0, "n_missing": 229, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_real_py": {"hash": "82bf27788620a4ce838ec6dd9f202c7b", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_real_py.html", "file": "tests\\test_event_bus_config_real.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_simple_py": {"hash": "63d6ae100d0b7d12aa979fa66f14886c", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_simple_py.html", "file": "tests\\test_event_bus_config_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_standalone_py": {"hash": "9b49a57e35abf077ca360f7a94e6e3bc", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_standalone_py.html", "file": "tests\\test_event_bus_config_standalone.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_config_validators_py": {"hash": "53406d0cf599b43c354b9700fe494e00", "index": {"url": "z_a44f0ac069e85531_test_event_bus_config_validators_py.html", "file": "tests\\test_event_bus_config_validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 0, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_consumer_py": {"hash": "dc0f125c39385a3571467215dd0782a6", "index": {"url": "z_a44f0ac069e85531_test_event_bus_consumer_py.html", "file": "tests\\test_event_bus_consumer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_consumer_comprehensive_py": {"hash": "01c20bec26316cdca9b0a859e71494cf", "index": {"url": "z_a44f0ac069e85531_test_event_bus_consumer_comprehensive_py.html", "file": "tests\\test_event_bus_consumer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_consumer_detailed_py": {"hash": "91832d2d73ae7e5a9e91c37fcffa31ab", "index": {"url": "z_a44f0ac069e85531_test_event_bus_consumer_detailed_py.html", "file": "tests\\test_event_bus_consumer_detailed.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 80, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_consumer_extended_py": {"hash": "4ef67dce28fdfc2914905e3e296aed81", "index": {"url": "z_a44f0ac069e85531_test_event_bus_consumer_extended_py.html", "file": "tests\\test_event_bus_consumer_extended.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_integration_py": {"hash": "cf3aa1c1335ed4925921fc90cdd39ab1", "index": {"url": "z_a44f0ac069e85531_test_event_bus_integration_py.html", "file": "tests\\test_event_bus_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_producer_py": {"hash": "90779811718e7f9fcb27522942e6dc80", "index": {"url": "z_a44f0ac069e85531_test_event_bus_producer_py.html", "file": "tests\\test_event_bus_producer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_producer_comprehensive_py": {"hash": "4485ce50d3bd0258e715b79d95931c5a", "index": {"url": "z_a44f0ac069e85531_test_event_bus_producer_comprehensive_py.html", "file": "tests\\test_event_bus_producer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 290, "n_excluded": 0, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_producer_extended_py": {"hash": "aaac1c0d92c6bee678c731436b011ce9", "index": {"url": "z_a44f0ac069e85531_test_event_bus_producer_extended_py.html", "file": "tests\\test_event_bus_producer_extended.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_pytest_py": {"hash": "1c4e74cbd71c6317562a9cd7d670a484", "index": {"url": "z_a44f0ac069e85531_test_event_bus_pytest_py.html", "file": "tests\\test_event_bus_pytest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_schemas_py": {"hash": "0cae49ab0564f1c6d0f97d290201614a", "index": {"url": "z_a44f0ac069e85531_test_event_bus_schemas_py.html", "file": "tests\\test_event_bus_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_specialized_consumers_py": {"hash": "78ea8e23ce5cde1a26a8ae33430e7324", "index": {"url": "z_a44f0ac069e85531_test_event_bus_specialized_consumers_py.html", "file": "tests\\test_event_bus_specialized_consumers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_bus_structure_py": {"hash": "4ba321cc700b4e86924899a6a10da312", "index": {"url": "z_a44f0ac069e85531_test_event_bus_structure_py.html", "file": "tests\\test_event_bus_structure.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_event_schemas_isolated_py": {"hash": "a010bb053f7c0efc8d35de15b40d48ca", "index": {"url": "z_a44f0ac069e85531_test_event_schemas_isolated_py.html", "file": "tests\\test_event_schemas_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_basic_py": {"hash": "25069582e8f86ca74855d57b3aa9a5ee", "index": {"url": "z_a44f0ac069e85531_test_garch_model_basic_py.html", "file": "tests\\test_garch_model_basic.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_copy_py": {"hash": "9511b0d1fac002cb99fa28f7c77d3968", "index": {"url": "z_a44f0ac069e85531_test_garch_model_copy_py.html", "file": "tests\\test_garch_model_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_coverage_py": {"hash": "f14e5c8a5ec512dcf8310fa46eb2ab92", "index": {"url": "z_a44f0ac069e85531_test_garch_model_coverage_py.html", "file": "tests\\test_garch_model_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 184, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_direct_py": {"hash": "9fa526451c00f119579ee6d0aca9aef5", "index": {"url": "z_a44f0ac069e85531_test_garch_model_direct_py.html", "file": "tests\\test_garch_model_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_direct_copy_py": {"hash": "79893d737116880316461e10b24b0e8e", "index": {"url": "z_a44f0ac069e85531_test_garch_model_direct_copy_py.html", "file": "tests\\test_garch_model_direct_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_isolated_py": {"hash": "b228b914d6447885f9271495ce30d3aa", "index": {"url": "z_a44f0ac069e85531_test_garch_model_isolated_py.html", "file": "tests\\test_garch_model_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_mocked_py": {"hash": "e715d88ba8b8037355d27dd038a9d0b6", "index": {"url": "z_a44f0ac069e85531_test_garch_model_mocked_py.html", "file": "tests\\test_garch_model_mocked.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_simple_py": {"hash": "fb68aae533f69a47d360ea787c6793d4", "index": {"url": "z_a44f0ac069e85531_test_garch_model_simple_py.html", "file": "tests\\test_garch_model_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_garch_model_standalone_py": {"hash": "e8773dc0c654b54aa83169d4c0ac7712", "index": {"url": "z_a44f0ac069e85531_test_garch_model_standalone_py.html", "file": "tests\\test_garch_model_standalone.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_py": {"hash": "0a493b12024dd6c1f57ffb8473ab0bae", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_py.html", "file": "tests\\test_gemini_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_100_coverage_py": {"hash": "9f8e75963763ffa089cc4468dccbc2a3", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_100_coverage_py.html", "file": "tests\\test_gemini_client_100_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_90_coverage_py": {"hash": "5e5a6c3fbd447aee61718c1d02a11dbe", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_90_coverage_py.html", "file": "tests\\test_gemini_client_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_comprehensive_py": {"hash": "3ecefcdfc3c93499b3555ad9f9f55a1b", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_comprehensive_py.html", "file": "tests\\test_gemini_client_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 259, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_edge_cases_py": {"hash": "20f9f9d30ab8acd036adbc0680c10ab8", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_edge_cases_py.html", "file": "tests\\test_gemini_client_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_enhanced_py": {"hash": "9bb08381b67a32f8b9a710686f980d5e", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_enhanced_py.html", "file": "tests\\test_gemini_client_enhanced.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_final_90_py": {"hash": "317a07775e25282dae3f4cca54f292ab", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_final_90_py.html", "file": "tests\\test_gemini_client_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_gemini_client_final_coverage_py": {"hash": "bdf1f2af54027e74f647665afd55d672", "index": {"url": "z_a44f0ac069e85531_test_gemini_client_final_coverage_py.html", "file": "tests\\test_gemini_client_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_get_recent_performance_summary_py": {"hash": "1c4073b3965729315df66bcae4d12c3e", "index": {"url": "z_a44f0ac069e85531_test_get_recent_performance_summary_py.html", "file": "tests\\test_get_recent_performance_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db5bc7cc00fbdeae___init___py": {"hash": "d1f55ec9c977982b5689d4b1cbb2bd06", "index": {"url": "z_db5bc7cc00fbdeae___init___py.html", "file": "tests\\test_global_pmi\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db5bc7cc00fbdeae_test_analyzer_py": {"hash": "d0bcf37f17c88eeefd75a3c38ce4f2e9", "index": {"url": "z_db5bc7cc00fbdeae_test_analyzer_py.html", "file": "tests\\test_global_pmi\\test_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db5bc7cc00fbdeae_test_client_py": {"hash": "3b4fe1616278c66c5b2bcd6db6e6b80b", "index": {"url": "z_db5bc7cc00fbdeae_test_client_py.html", "file": "tests\\test_global_pmi\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_db5bc7cc00fbdeae_test_models_py": {"hash": "515632336b94bb61f47fe6ae9fd93666", "index": {"url": "z_db5bc7cc00fbdeae_test_models_py.html", "file": "tests\\test_global_pmi\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_global_pmi_models_100_percent_py": {"hash": "10d326807e93a3f19c29ad52d9d56742", "index": {"url": "z_a44f0ac069e85531_test_global_pmi_models_100_percent_py.html", "file": "tests\\test_global_pmi_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_global_pmi_models_73_to_90_coverage_py": {"hash": "8f005bfef9ff60ab229fb6445ddd1eed", "index": {"url": "z_a44f0ac069e85531_test_global_pmi_models_73_to_90_coverage_py.html", "file": "tests\\test_global_pmi_models_73_to_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_global_pmi_models_batch4_90_coverage_py": {"hash": "b3a0b688b355c000711ae98e62cda040", "index": {"url": "z_a44f0ac069e85531_test_global_pmi_models_batch4_90_coverage_py.html", "file": "tests\\test_global_pmi_models_batch4_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_global_pmi_models_batch6_90_coverage_py": {"hash": "3aa824129196d01ebbe6d184365a25f8", "index": {"url": "z_a44f0ac069e85531_test_global_pmi_models_batch6_90_coverage_py.html", "file": "tests\\test_global_pmi_models_batch6_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_global_pmi_models_phase5g_py": {"hash": "c80f5a0f1c9065fb16adecd0562146bd", "index": {"url": "z_a44f0ac069e85531_test_global_pmi_models_phase5g_py.html", "file": "tests\\test_global_pmi_models_phase5g.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_calculator_py": {"hash": "4fffa53c88f44b21e690a0f4c0b282d2", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_calculator_py.html", "file": "tests\\test_heikin_ashi_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_calculator_additional_py": {"hash": "4497f9fd0e8bae70f4df90ea9293417d", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_calculator_additional_py.html", "file": "tests\\test_heikin_ashi_calculator_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 199, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_calculator_comprehensive_py": {"hash": "98325d80632a28a35df6342fbac4495f", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_calculator_comprehensive_py.html", "file": "tests\\test_heikin_ashi_calculator_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_calculator_direct_py": {"hash": "f51240f099c2f269d617aaa3c5e27454", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_calculator_direct_py.html", "file": "tests\\test_heikin_ashi_calculator_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_calculator_final_90_py": {"hash": "9dd26ae27fd6f317c36c0f82c3c9a226", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_calculator_final_90_py.html", "file": "tests\\test_heikin_ashi_calculator_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_heikin_ashi_combined_py": {"hash": "deefa12934ac4b1a9b04cb3a94ecdfff", "index": {"url": "z_a44f0ac069e85531_test_heikin_ashi_combined_py.html", "file": "tests\\test_heikin_ashi_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_helpers_py": {"hash": "dd9f0298eeca7d169428ca2d43d4f3ea", "index": {"url": "z_a44f0ac069e85531_test_helpers_py.html", "file": "tests\\test_helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_py": {"hash": "9d2427d7fd08b2a35d87ba419cd7dfb9", "index": {"url": "z_a44f0ac069e85531_test_indicators_py.html", "file": "tests\\test_indicators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_additional_py": {"hash": "027b044ffeccf5f4e7e4f8c5944822b7", "index": {"url": "z_a44f0ac069e85531_test_indicators_additional_py.html", "file": "tests\\test_indicators_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_comprehensive_py": {"hash": "040ab49256896c41a22327ee09eace98", "index": {"url": "z_a44f0ac069e85531_test_indicators_comprehensive_py.html", "file": "tests\\test_indicators_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_direct_py": {"hash": "5e65dbfb27c6256d9d82223aa75ab205", "index": {"url": "z_a44f0ac069e85531_test_indicators_direct_py.html", "file": "tests\\test_indicators_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_fixed_py": {"hash": "87dac442b7f77002e65ef66ff4804036", "index": {"url": "z_a44f0ac069e85531_test_indicators_fixed_py.html", "file": "tests\\test_indicators_fixed.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_new_py": {"hash": "2cf005108a7570fed7b332d4cfa495ea", "index": {"url": "z_a44f0ac069e85531_test_indicators_new_py.html", "file": "tests\\test_indicators_new.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_indicators_simple_new_py": {"hash": "d03da867569ca0320630a3c2a6ce26f6", "index": {"url": "z_a44f0ac069e85531_test_indicators_simple_new_py.html", "file": "tests\\test_indicators_simple_new.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_integration_data_flow_py": {"hash": "72d4c81c50ad1812a58fd1503ae1a3c1", "index": {"url": "z_a44f0ac069e85531_test_integration_data_flow_py.html", "file": "tests\\test_integration_data_flow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_integration_end_to_end_py": {"hash": "4649e361f7ded0977c1420bdb36b1022", "index": {"url": "z_a44f0ac069e85531_test_integration_end_to_end_py.html", "file": "tests\\test_integration_end_to_end.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_integration_signal_generation_py": {"hash": "75de4b1f011a2bce6a6dc937ee3e39e3", "index": {"url": "z_a44f0ac069e85531_test_integration_signal_generation_py.html", "file": "tests\\test_integration_signal_generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_load_performance_data_py": {"hash": "3af9f87fdbba033489fe1ff5ee9a78f9", "index": {"url": "z_a44f0ac069e85531_test_load_performance_data_py.html", "file": "tests\\test_load_performance_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_load_performance_data_simple_py": {"hash": "8d0c0b8aaf77e58bac221270b68eb702", "index": {"url": "z_a44f0ac069e85531_test_load_performance_data_simple_py.html", "file": "tests\\test_load_performance_data_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_manager_py": {"hash": "34dc6914ca120363916377d870bd1f1c", "index": {"url": "z_a44f0ac069e85531_test_log_manager_py.html", "file": "tests\\test_log_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 264, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_manager_isolated_py": {"hash": "992dfe067ceb7692faab6ed7580cd0eb", "index": {"url": "z_a44f0ac069e85531_test_log_manager_isolated_py.html", "file": "tests\\test_log_manager_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 274, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_manager_simple_py": {"hash": "0e89f722c017d820034af94928905ee8", "index": {"url": "z_a44f0ac069e85531_test_log_manager_simple_py.html", "file": "tests\\test_log_manager_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_uploader_py": {"hash": "54813baa0dbc49af8c49338f0aa54183", "index": {"url": "z_a44f0ac069e85531_test_log_uploader_py.html", "file": "tests\\test_log_uploader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_uploader_90_coverage_py": {"hash": "1f9102e8f18d520036ee975fb20c017b", "index": {"url": "z_a44f0ac069e85531_test_log_uploader_90_coverage_py.html", "file": "tests\\test_log_uploader_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_uploader_additional_py": {"hash": "a3387f09b64ab69098d006d439b0af4c", "index": {"url": "z_a44f0ac069e85531_test_log_uploader_additional_py.html", "file": "tests\\test_log_uploader_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_uploader_batch2_90_coverage_py": {"hash": "385f251e24e4221018f2a84e80775162", "index": {"url": "z_a44f0ac069e85531_test_log_uploader_batch2_90_coverage_py.html", "file": "tests\\test_log_uploader_batch2_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_log_uploader_comprehensive_py": {"hash": "db0deacd4953b4da585abebf60ce983c", "index": {"url": "z_a44f0ac069e85531_test_log_uploader_comprehensive_py.html", "file": "tests\\test_log_uploader_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_additional_py": {"hash": "529a8237040b22ce11e5fc94301646fd", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_additional_py.html", "file": "tests\\test_macro_analyzer_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 214, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_analyzer_py": {"hash": "3e5ff2e1f7fdc0e9589d1782656d944e", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_analyzer_py.html", "file": "tests\\test_macro_analyzer_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_analyzer_comprehensive_py": {"hash": "4b6e786a6e5a0e0b094a355c923c331a", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_analyzer_comprehensive_py.html", "file": "tests\\test_macro_analyzer_analyzer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 138, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_analyzer_final_90_py": {"hash": "9167af5637cb93bbc6a3ef77df79e9c1", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_analyzer_final_90_py.html", "file": "tests\\test_macro_analyzer_analyzer_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 201, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_calendar_py": {"hash": "b369514c0b63225f5ff0a8fcbb24120b", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_calendar_py.html", "file": "tests\\test_macro_analyzer_calendar.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_classify_py": {"hash": "57faed306ab12b01f87832dacca15977", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_classify_py.html", "file": "tests\\test_macro_analyzer_classify.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_combined_py": {"hash": "7a9d6e436309fcc1ef7c7a656c8d2161", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_combined_py.html", "file": "tests\\test_macro_analyzer_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_context_py": {"hash": "7a6c760e82198ed81c5ba13b73206234", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_context_py.html", "file": "tests\\test_macro_analyzer_context.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_fetcher_py": {"hash": "f7eb3af872fff28b0d1a62a7488b54f8", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_fetcher_py.html", "file": "tests\\test_macro_analyzer_fetcher.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_fetcher_comprehensive_py": {"hash": "547aa09d3866184b6c983961b5f8b631", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_fetcher_comprehensive_py.html", "file": "tests\\test_macro_analyzer_fetcher_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 238, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_fetcher_final_90_py": {"hash": "56c0106efb4ade070c298ac9076326f9", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_fetcher_final_90_py.html", "file": "tests\\test_macro_analyzer_fetcher_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_fetcher_simple_py": {"hash": "82b0893224dd149dfa886d1f96eac324", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_fetcher_simple_py.html", "file": "tests\\test_macro_analyzer_fetcher_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_macro_analyzer_simple_py": {"hash": "de66efba8597b2ae37f8ee5f253fe9ae", "index": {"url": "z_a44f0ac069e85531_test_macro_analyzer_simple_py.html", "file": "tests\\test_macro_analyzer_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_main_py": {"hash": "e2ffb01fe62086ce9081474f15e6e72c", "index": {"url": "z_a44f0ac069e85531_test_main_py.html", "file": "tests\\test_main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_event_bus_integration_py": {"hash": "ba9b42c49a80ff3a2da4ff508a3fb9ef", "index": {"url": "z_a44f0ac069e85531_test_market_depth_event_bus_integration_py.html", "file": "tests\\test_market_depth_event_bus_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_models_py": {"hash": "253f4e64f4090b2b049d60c2dad61be1", "index": {"url": "z_a44f0ac069e85531_test_market_depth_models_py.html", "file": "tests\\test_market_depth_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_models_phase5z_py": {"hash": "6cda8307f98a176cbb0b7eccf03e436a", "index": {"url": "z_a44f0ac069e85531_test_market_depth_models_phase5z_py.html", "file": "tests\\test_market_depth_models_phase5z.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5aa_py": {"hash": "59a7478b3e90306fc66be914f3b54b5e", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5aa_py.html", "file": "tests\\test_market_depth_phase5aa.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5bb_py": {"hash": "00004fcb241c3ead97794c58e9e452e1", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5bb_py.html", "file": "tests\\test_market_depth_phase5bb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5cc_simple_py": {"hash": "96e21b08c383b25be87d7fcb13e117e6", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5cc_simple_py.html", "file": "tests\\test_market_depth_phase5cc_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5dd_py": {"hash": "ee940379cb9361f4e8f236e0504812da", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5dd_py.html", "file": "tests\\test_market_depth_phase5dd.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5ee_simple_py": {"hash": "98d41a821cd5aaf3b05cd731b9e5abfe", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5ee_simple_py.html", "file": "tests\\test_market_depth_phase5ee_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5ff_py": {"hash": "495f06fe2ffe89fd5cf812ad1ee53fd0", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5ff_py.html", "file": "tests\\test_market_depth_phase5ff.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase5ff_simple_py": {"hash": "4c9840a4c6505d8256023675b47e530c", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase5ff_simple_py.html", "file": "tests\\test_market_depth_phase5ff_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase6b_clean_py": {"hash": "e6647d0f6bf30a701a0a3366d1fc8f1e", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase6b_clean_py.html", "file": "tests\\test_market_depth_phase6b_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase6c_simple_py": {"hash": "3caaeb435ec0d210fcac6ca8df33d3c8", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase6c_simple_py.html", "file": "tests\\test_market_depth_phase6c_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase6f_surgical_py": {"hash": "eb5557dbc60421dddb504c0c6782a32c", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase6f_surgical_py.html", "file": "tests\\test_market_depth_phase6f_surgical.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_phase6f_surgical_fixed_py": {"hash": "8f66dd4fd57a282202f44e58d064501a", "index": {"url": "z_a44f0ac069e85531_test_market_depth_phase6f_surgical_fixed_py.html", "file": "tests\\test_market_depth_phase6f_surgical_fixed.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_structure_py": {"hash": "98b4f3b94911a8fb6c521644359bc37a", "index": {"url": "z_a44f0ac069e85531_test_market_depth_structure_py.html", "file": "tests\\test_market_depth_structure.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_py": {"hash": "b7c838f3241136f43625e22855a2a975", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_py.html", "file": "tests\\test_market_depth_visualizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_220c46feb8f69054_test_client_py": {"hash": "a64c83c2dcaed37315fb99ea6e5b3380", "index": {"url": "z_220c46feb8f69054_test_client_py.html", "file": "tests\\test_market_depth_visualizer\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 287, "n_excluded": 0, "n_missing": 224, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_220c46feb8f69054_test_models_py": {"hash": "cdc14ee2cb32c2b91899404704fcb0df", "index": {"url": "z_220c46feb8f69054_test_models_py.html", "file": "tests\\test_market_depth_visualizer\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_comprehensive_py": {"hash": "8455d379c53fb1afe3e5efa95c0f4bc7", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_comprehensive_py.html", "file": "tests\\test_market_depth_visualizer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_init_batch8_90_coverage_py": {"hash": "4f2bef17802d6de7354853a75472d4f5", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_init_batch8_90_coverage_py.html", "file": "tests\\test_market_depth_visualizer_init_batch8_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_100_percent_py": {"hash": "052396f71f5a7d4ec3558b12c026a695", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_100_percent_py.html", "file": "tests\\test_market_depth_visualizer_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_enhanced_py": {"hash": "9e1dc10e94c1330110f7f507953ce021", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_enhanced_py.html", "file": "tests\\test_market_depth_visualizer_models_enhanced.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase2_py": {"hash": "2b584a0e5f9ffec06dc7886273e89231", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase2_py.html", "file": "tests\\test_market_depth_visualizer_models_phase2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5_py": {"hash": "91c27d4f046e30de60f006bd56fe8cf8", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5_py.html", "file": "tests\\test_market_depth_visualizer_models_phase5.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5n_py": {"hash": "faf2e18e2dc16ff04aaf284670bf3a26", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5n_py.html", "file": "tests\\test_market_depth_visualizer_models_phase5n.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 264, "n_excluded": 0, "n_missing": 240, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5z_final_py": {"hash": "956ac363b619f4620d94c48403310d94", "index": {"url": "z_a44f0ac069e85531_test_market_depth_visualizer_models_phase5z_final_py.html", "file": "tests\\test_market_depth_visualizer_models_phase5z_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_hours_session_info_py": {"hash": "6663404885ee543386736d24fdf26dc4", "index": {"url": "z_a44f0ac069e85531_test_market_hours_session_info_py.html", "file": "tests\\test_market_hours_session_info.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_hours_session_info_additional_py": {"hash": "e8364c20cc5eac874a93875ef4d1a7ea", "index": {"url": "z_a44f0ac069e85531_test_market_hours_session_info_additional_py.html", "file": "tests\\test_market_hours_session_info_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_hours_session_info_final_100_py": {"hash": "aa29f537401f18dc097e5902605b3c85", "index": {"url": "z_a44f0ac069e85531_test_market_hours_session_info_final_100_py.html", "file": "tests\\test_market_hours_session_info_final_100.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_market_hours_settings_py": {"hash": "f176fc22838b8c5efdcf1c745c3f5fb3", "index": {"url": "z_a44f0ac069e85531_test_market_hours_settings_py.html", "file": "tests\\test_market_hours_settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metatrader_py": {"hash": "09109f2807f9b049bf55e82979c5c841", "index": {"url": "z_a44f0ac069e85531_test_metatrader_py.html", "file": "tests\\test_metatrader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2040, "n_excluded": 0, "n_missing": 2017, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metatrader_sleep_py": {"hash": "bf3576d7697b86d15a7e325f64752446", "index": {"url": "z_a44f0ac069e85531_test_metatrader_sleep_py.html", "file": "tests\\test_metatrader_sleep.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metatrader_uploads_py": {"hash": "9e532e38d0983df03de12d6377a6660d", "index": {"url": "z_a44f0ac069e85531_test_metatrader_uploads_py.html", "file": "tests\\test_metatrader_uploads.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_config_phase5p_py": {"hash": "3924755c1bdc5a4cf20c34832cf98de6", "index": {"url": "z_a44f0ac069e85531_test_metrics_config_phase5p_py.html", "file": "tests\\test_metrics_config_phase5p.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_client_py": {"hash": "05d1b568ae362b8b079d6929dc2104ca", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_client_py.html", "file": "tests\\test_metrics_dashboard_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_enums_py": {"hash": "f431df8bbd7e8dcad02aecd4f8ddd049", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_enums_py.html", "file": "tests\\test_metrics_dashboard_enums.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_event_bus_integration_py": {"hash": "c11a2bdba85089ed5a373844987394a5", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_event_bus_integration_py.html", "file": "tests\\test_metrics_dashboard_event_bus_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_init_batch9_90_coverage_py": {"hash": "74d71eb44c2206d444376627b80508ac", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_init_batch9_90_coverage_py.html", "file": "tests\\test_metrics_dashboard_init_batch9_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_py": {"hash": "6294e9e39de83d84663f377504ffd2b3", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_py.html", "file": "tests\\test_metrics_dashboard_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 245, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_100_percent_py": {"hash": "aa7156deb48536dc2b2fdf11c2792d4f", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_100_percent_py.html", "file": "tests\\test_metrics_dashboard_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 181, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_90_percent_py": {"hash": "835e436327b73b46c992f4a4b8d5e97a", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_90_percent_py.html", "file": "tests\\test_metrics_dashboard_models_90_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_clean_py": {"hash": "fbc753452aedb8c6aed64e9a40774b9a", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_clean_py.html", "file": "tests\\test_metrics_dashboard_models_phase5_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_final_py": {"hash": "6b4aa5ffd0e6d7f1d5bb0463f85f641b", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5_final_py.html", "file": "tests\\test_metrics_dashboard_models_phase5_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_90_coverage_py": {"hash": "5e22a5a7444894c34a163d8a9c23b70a", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_90_coverage_py.html", "file": "tests\\test_metrics_dashboard_models_phase5x_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_simple_py": {"hash": "fa33173740c9453ce49ae8d704eaab7e", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5x_simple_py.html", "file": "tests\\test_metrics_dashboard_models_phase5x_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5y_final_py": {"hash": "b6526f02db34bfef57af6d380b770ff5", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_models_phase5y_final_py.html", "file": "tests\\test_metrics_dashboard_models_phase5y_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_metrics_dashboard_structure_py": {"hash": "2dfbfdf8367b52d31ed827a1685ce0f4", "index": {"url": "z_a44f0ac069e85531_test_metrics_dashboard_structure_py.html", "file": "tests\\test_metrics_dashboard_structure.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7___init___py": {"hash": "3da19b61c01027720e2623a4fb765938", "index": {"url": "z_8d9fd453d1a7f5f7___init___py.html", "file": "tests\\test_ml_registry\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_integrations_py": {"hash": "f830c7ebb1d4fe2993d48e181d774efd", "index": {"url": "z_8d9fd453d1a7f5f7_test_integrations_py.html", "file": "tests\\test_ml_registry\\test_integrations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_mlflow_registry_py": {"hash": "3524e68d6b4b952eb735ff5b81312033", "index": {"url": "z_8d9fd453d1a7f5f7_test_mlflow_registry_py.html", "file": "tests\\test_ml_registry\\test_mlflow_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 262, "n_excluded": 0, "n_missing": 227, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_model_config_py": {"hash": "b25a3c08511b0f9fa2ee380bb5ca66a9", "index": {"url": "z_8d9fd453d1a7f5f7_test_model_config_py.html", "file": "tests\\test_ml_registry\\test_model_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_model_deployment_py": {"hash": "92b99622acfb21f57382f145fa2ff9b3", "index": {"url": "z_8d9fd453d1a7f5f7_test_model_deployment_py.html", "file": "tests\\test_ml_registry\\test_model_deployment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_model_evaluation_py": {"hash": "8ffe5e53f996b2224c1bb6db3a462db3", "index": {"url": "z_8d9fd453d1a7f5f7_test_model_evaluation_py.html", "file": "tests\\test_ml_registry\\test_model_evaluation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 265, "n_excluded": 0, "n_missing": 234, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8d9fd453d1a7f5f7_test_model_manager_py": {"hash": "4d1cbb270119d810c50c91f91660570d", "index": {"url": "z_8d9fd453d1a7f5f7_test_model_manager_py.html", "file": "tests\\test_ml_registry\\test_model_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_ml_registry_model_config_phase5p_py": {"hash": "9ed3ef01263145c5c80655fb053bfa55", "index": {"url": "z_a44f0ac069e85531_test_ml_registry_model_config_phase5p_py.html", "file": "tests\\test_ml_registry_model_config_phase5p.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 326, "n_excluded": 0, "n_missing": 297, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_models_train_hmm_py": {"hash": "e2e1741c1f45f2615e755239a127e960", "index": {"url": "z_a44f0ac069e85531_test_models_train_hmm_py.html", "file": "tests\\test_models_train_hmm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_py": {"hash": "5a9bb8c1f43643fb62dad7c8af3c8a28", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_py.html", "file": "tests\\test_mt5_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 179, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_90_coverage_py": {"hash": "33e8085ad9988587286ddcab6c4d6c01", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_90_coverage_py.html", "file": "tests\\test_mt5_client_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 190, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_90_plus_py": {"hash": "3e39384d22a932309b1fb3640eb32079", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_90_plus_py.html", "file": "tests\\test_mt5_client_90_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_additional_py": {"hash": "dbfc2e50868cb51b3c61f88b22ab6551", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_additional_py.html", "file": "tests\\test_mt5_client_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_comprehensive_py": {"hash": "d9cd5661ed241b49df5722b9da255340", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_comprehensive_py.html", "file": "tests\\test_mt5_client_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 498, "n_excluded": 0, "n_missing": 396, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_coverage_boost_py": {"hash": "13357830be59c5ef36f860fc704a76e7", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_coverage_boost_py.html", "file": "tests\\test_mt5_client_coverage_boost.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_edge_cases_py": {"hash": "ab89bfcb7b482f54d42d8950cdc3863d", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_edge_cases_py.html", "file": "tests\\test_mt5_client_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_event_bus_py": {"hash": "25c8df33dda06778f878ab7d2341b450", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_event_bus_py.html", "file": "tests\\test_mt5_client_event_bus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 142, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_py": {"hash": "403917ea34d69ab231593d50587dec2d", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_py.html", "file": "tests\\test_mt5_client_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 268, "n_excluded": 0, "n_missing": 214, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_py": {"hash": "421ee41221e2bb1131e5838a479afd75", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_py.html", "file": "tests\\test_mt5_client_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_complete_py": {"hash": "ad75bad1a45102f862832f1bd3342d25", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_complete_py.html", "file": "tests\\test_mt5_client_final_90_complete.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_coverage_py": {"hash": "2f055a7057b66dd5029863255e421b1f", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_coverage_py.html", "file": "tests\\test_mt5_client_final_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_plus_py": {"hash": "340c6f0aa01e596b32306613a34fa066", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_plus_py.html", "file": "tests\\test_mt5_client_final_90_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_py": {"hash": "641a229509b64e4fe6b7f7c768f20089", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_py.html", "file": "tests\\test_mt5_client_final_90_plus_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_plus_py": {"hash": "fc600a53c56c1b9e779686aea7211e66", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_90_plus_plus_plus_py.html", "file": "tests\\test_mt5_client_final_90_plus_plus_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_coverage_py": {"hash": "b8854065fdbaebf855cc22c3a20235cf", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_coverage_py.html", "file": "tests\\test_mt5_client_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_final_coverage_complete_py": {"hash": "8a4bf6669d66820a20566299730fad67", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_final_coverage_complete_py.html", "file": "tests\\test_mt5_client_final_coverage_complete.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_integration_py": {"hash": "d0a73131c1534ad58017474188ce371e", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_integration_py.html", "file": "tests\\test_mt5_client_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_client_remaining_py": {"hash": "c8ee475b73a3f68b295a09f4ef2c8625", "index": {"url": "z_a44f0ac069e85531_test_mt5_client_remaining_py.html", "file": "tests\\test_mt5_client_remaining.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 0, "n_missing": 183, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_mt5_event_producer_py": {"hash": "028fb7bd986a6e776b0248ad6f3c062f", "index": {"url": "z_a44f0ac069e85531_test_mt5_event_producer_py.html", "file": "tests\\test_mt5_event_producer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_py": {"hash": "6b4db2dbfaa5729f1944d0112019f48e", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_py.html", "file": "tests\\test_multilingual_news.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_90_coverage_py": {"hash": "7ad90f2f58d17bcfe15f55f1903945d7", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_90_coverage_py.html", "file": "tests\\test_multilingual_news_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_analyzer_90_coverage_py": {"hash": "d7b715b647a299ebdf13bb853782a84d", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_analyzer_90_coverage_py.html", "file": "tests\\test_multilingual_news_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_analyzer_copy_py": {"hash": "d58b7c5bbcc64fb34852248793a1db2b", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_analyzer_copy_py.html", "file": "tests\\test_multilingual_news_analyzer_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 428, "n_excluded": 0, "n_missing": 352, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_client_90_coverage_py": {"hash": "299b1f6a5a214fb6ae0ac183e35913c3", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_client_90_coverage_py.html", "file": "tests\\test_multilingual_news_client_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_client_copy_py": {"hash": "777b1233b819fc510dd512d055e8ae4f", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_client_copy_py.html", "file": "tests\\test_multilingual_news_client_copy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 270, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_comprehensive_py": {"hash": "5e57265fa81467e385037f6987d7ad13", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_comprehensive_py.html", "file": "tests\\test_multilingual_news_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_direct_py": {"hash": "b48b4b66f89d3e528c8f2a682cd01e8b", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_direct_py.html", "file": "tests\\test_multilingual_news_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 125, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_py": {"hash": "b59885055eddbd5b3e17ea5dc7081b0c", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_py.html", "file": "tests\\test_multilingual_news_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_90_coverage_py": {"hash": "f7cf84e309ceaeaaebcbee690c1a4bf3", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_90_coverage_py.html", "file": "tests\\test_multilingual_news_models_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_95_percent_py": {"hash": "c176a906504da44d0f0b41da6a1e84de", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_95_percent_py.html", "file": "tests\\test_multilingual_news_models_95_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_comprehensive_py": {"hash": "5820861556b6469b52215690122b4c80", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_comprehensive_py.html", "file": "tests\\test_multilingual_news_models_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 90, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_mock_py": {"hash": "fbef5a9d57161db3235a7d253a6a2719", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_mock_py.html", "file": "tests\\test_multilingual_news_models_mock.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 356, "n_excluded": 0, "n_missing": 262, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_models_phase5k_py": {"hash": "d5d438e573245d722dcd8cc619134015", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_models_phase5k_py.html", "file": "tests\\test_multilingual_news_models_phase5k.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_multilingual_news_simple_py": {"hash": "06b49fb72aadb85c893a96916f55aa31", "index": {"url": "z_a44f0ac069e85531_test_multilingual_news_simple_py.html", "file": "tests\\test_multilingual_news_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_py": {"hash": "867da1c41756381a150e1dcee3569be8", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_py.html", "file": "tests\\test_news_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_90_coverage_py": {"hash": "b178a9fc722db043a6910aae2281483c", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_90_coverage_py.html", "file": "tests\\test_news_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_90_coverage_part2_py": {"hash": "8afd21da7bce78d2ece0bc8b471307be", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_90_coverage_part2_py.html", "file": "tests\\test_news_analyzer_90_coverage_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_direct_py": {"hash": "9cc365da5d9ceed986bcfca95754e584", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_direct_py.html", "file": "tests\\test_news_analyzer_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_isolated_py": {"hash": "435ed9c74ab9c88de911649fac0e3862", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_isolated_py.html", "file": "tests\\test_news_analyzer_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_simple_py": {"hash": "aeffa1cee59051bad0f8bbb36cc23075", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_simple_py.html", "file": "tests\\test_news_analyzer_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_analyzer_standalone_py": {"hash": "bbc53fe21626fe2930280f7d57f2c74c", "index": {"url": "z_a44f0ac069e85531_test_news_analyzer_standalone_py.html", "file": "tests\\test_news_analyzer_standalone.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_service_py": {"hash": "66af99d7d4163524e4cef0e595111da4", "index": {"url": "z_a44f0ac069e85531_test_news_service_py.html", "file": "tests\\test_news_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_news_service_comprehensive_py": {"hash": "82b62bee5f2a25d809f949252684b1df", "index": {"url": "z_a44f0ac069e85531_test_news_service_comprehensive_py.html", "file": "tests\\test_news_service_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_book_py": {"hash": "f7cae88ff7c9d1c94a9281a71845695e", "index": {"url": "z_a44f0ac069e85531_test_order_book_py.html", "file": "tests\\test_order_book.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_book_init_batch7_90_coverage_py": {"hash": "33f50ea53a256eed571ab4c20f5c5dcf", "index": {"url": "z_a44f0ac069e85531_test_order_book_init_batch7_90_coverage_py.html", "file": "tests\\test_order_book_init_batch7_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 228, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_book_models_100_percent_py": {"hash": "278114b155726d71fa4e08d098258a4b", "index": {"url": "z_a44f0ac069e85531_test_order_book_models_100_percent_py.html", "file": "tests\\test_order_book_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_book_models_100_percent_fixed_py": {"hash": "a9382443cf47f31381b8ba966f8521a0", "index": {"url": "z_a44f0ac069e85531_test_order_book_models_100_percent_fixed_py.html", "file": "tests\\test_order_book_models_100_percent_fixed.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_book_models_phase5o_py": {"hash": "4021cc479b3b4fca62078b9ddeb8cc4c", "index": {"url": "z_a44f0ac069e85531_test_order_book_models_phase5o_py.html", "file": "tests\\test_order_book_models_phase5o.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_py": {"hash": "c7b001c8f932bc829c9e10d7110f5ec7", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_py.html", "file": "tests\\test_order_flow_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_comprehensive_py": {"hash": "f8a3335ac01b329fde84148eb3017095", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_comprehensive_py.html", "file": "tests\\test_order_flow_analyzer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_init_batch9_90_coverage_py": {"hash": "ccee94073fe88328afd6b7e5014c48bf", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_init_batch9_90_coverage_py.html", "file": "tests\\test_order_flow_analyzer_init_batch9_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_mock_py": {"hash": "d2d27004d279ca8430877574ac3319b9", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_mock_py.html", "file": "tests\\test_order_flow_analyzer_mock.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_100_percent_py": {"hash": "041a5bcd0b12024c753ae8c766adc009", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_100_percent_py.html", "file": "tests\\test_order_flow_analyzer_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_70_percent_py": {"hash": "d4a431d7a69d36c06ebaa6d969056b92", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_70_percent_py.html", "file": "tests\\test_order_flow_analyzer_models_70_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_enhanced_py": {"hash": "212042b5c2aab29a343cb236c0fba79f", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_enhanced_py.html", "file": "tests\\test_order_flow_analyzer_models_enhanced.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase4_py": {"hash": "42ca441431424edf962035301db6fd45", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase4_py.html", "file": "tests\\test_order_flow_analyzer_models_phase4.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5_clean_py": {"hash": "b38f320dce77b8b2d94f06802c6d5568", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5_clean_py.html", "file": "tests\\test_order_flow_analyzer_models_phase5_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5l_py": {"hash": "096464eef3b114522ab3a95c5c04be80", "index": {"url": "z_a44f0ac069e85531_test_order_flow_analyzer_models_phase5l_py.html", "file": "tests\\test_order_flow_analyzer_models_phase5l.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_client_py": {"hash": "ad0923618c80dd6675e80df4a5552502", "index": {"url": "z_a44f0ac069e85531_test_order_flow_client_py.html", "file": "tests\\test_order_flow_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_event_bus_integration_py": {"hash": "9f9c8ea4233623f287edd0bcd17e5db4", "index": {"url": "z_a44f0ac069e85531_test_order_flow_event_bus_integration_py.html", "file": "tests\\test_order_flow_event_bus_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_models_py": {"hash": "a1f2a5c7bd67d45b6cea1bc9a7647eab", "index": {"url": "z_a44f0ac069e85531_test_order_flow_models_py.html", "file": "tests\\test_order_flow_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_order_flow_models_simple_py": {"hash": "18f94e79693d25d3ded02dfd91c33301", "index": {"url": "z_a44f0ac069e85531_test_order_flow_models_simple_py.html", "file": "tests\\test_order_flow_models_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_90_coverage_py": {"hash": "bbe6aa5466b006e6f432fb4169e22e43", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_90_coverage_py.html", "file": "tests\\test_pattern_recognizer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_additional_py": {"hash": "6434badd33a4eb1a1fb371591ae5dbd7", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_additional_py.html", "file": "tests\\test_pattern_recognizer_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 208, "n_excluded": 0, "n_missing": 179, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_config_py": {"hash": "f638c877be75e51be80dd47ab4b38ba2", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_config_py.html", "file": "tests\\test_pattern_recognizer_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_coverage_py": {"hash": "9c80b1db52a8f5818991c391bbc8ea2e", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_coverage_py.html", "file": "tests\\test_pattern_recognizer_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_direct_py": {"hash": "edf9184749890700b8a978872c6a7574", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_direct_py.html", "file": "tests\\test_pattern_recognizer_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_edge_cases_py": {"hash": "1cdd703fa909e21c211d5818cd129ed4", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_edge_cases_py.html", "file": "tests\\test_pattern_recognizer_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_final_py": {"hash": "c4c12f080c26844af58b8ee7f128873d", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_final_py.html", "file": "tests\\test_pattern_recognizer_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_final_coverage_py": {"hash": "809618d7dbc341c45321cf6bf416a37c", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_final_coverage_py.html", "file": "tests\\test_pattern_recognizer_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_recognizer_py": {"hash": "41110b1d2edfb7a8c7f3f6ee5a5a4a01", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_recognizer_py.html", "file": "tests\\test_pattern_recognizer_recognizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 341, "n_excluded": 0, "n_missing": 289, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_pattern_recognizer_uncovered_py": {"hash": "7118f4557890af411b38694b4df00cfd", "index": {"url": "z_a44f0ac069e85531_test_pattern_recognizer_uncovered_py.html", "file": "tests\\test_pattern_recognizer_uncovered.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_analyzer_py": {"hash": "39538ab3fed7f064b116db005be9151e", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_analyzer_py.html", "file": "tests\\test_performance_analyzer_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_analyzer_90_coverage_py": {"hash": "4c59e3d025300858cf76ef37614fce7f", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_analyzer_90_coverage_py.html", "file": "tests\\test_performance_analyzer_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_analyzer_comprehensive_py": {"hash": "122533dc19c97a3361c406c0bf9a0dc2", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_analyzer_comprehensive_py.html", "file": "tests\\test_performance_analyzer_analyzer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_analyzer_final_90_py": {"hash": "751e2ccfa69b890966ae333f65580bcc", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_analyzer_final_90_py.html", "file": "tests\\test_performance_analyzer_analyzer_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_benchmark_metrics_py": {"hash": "a430bd55fc1ae4a5e33ee5b882c58e55", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_benchmark_metrics_py.html", "file": "tests\\test_performance_analyzer_benchmark_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_combined_py": {"hash": "ba6ff9efe7c5dcd837ecb5d47557acae", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_combined_py.html", "file": "tests\\test_performance_analyzer_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_get_summary_py": {"hash": "734856e97f099ff0f3af28ce55b84c77", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_get_summary_py.html", "file": "tests\\test_performance_analyzer_get_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_py": {"hash": "7b628ded0316957903de446cde154610", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_py.html", "file": "tests\\test_performance_analyzer_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 300, "n_excluded": 0, "n_missing": 247, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_py": {"hash": "c00ffe2072f2c435be801c97c1ed63ee", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_py.html", "file": "tests\\test_performance_analyzer_metrics_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 330, "n_excluded": 0, "n_missing": 221, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_part2_py": {"hash": "ca35c5c37532ab29dcff4491b28fb31d", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_90_coverage_part2_py.html", "file": "tests\\test_performance_analyzer_metrics_90_coverage_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_py": {"hash": "afb6eae7739f9bf8f27b15e5a5a50379", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_py.html", "file": "tests\\test_performance_analyzer_metrics_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_part2_py": {"hash": "2aea8a5c321a1959fec73a4fce0bb69f", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_90_part2_py.html", "file": "tests\\test_performance_analyzer_metrics_final_90_part2.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_coverage_py": {"hash": "6b560124528079b51739015dfee1ec2b", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_final_coverage_py.html", "file": "tests\\test_performance_analyzer_metrics_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_metrics_simple_py": {"hash": "33310f25c6cd949bc305ecc7ba5b1783", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_metrics_simple_py.html", "file": "tests\\test_performance_analyzer_metrics_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_recovery_metrics_py": {"hash": "6cf5bd31a67d365bffa65bc57333943a", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_recovery_metrics_py.html", "file": "tests\\test_performance_analyzer_recovery_metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_summary_py": {"hash": "fe8dbd1ba85f73f0cbde31dc8b191e3c", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_summary_py.html", "file": "tests\\test_performance_analyzer_summary.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_utils_py": {"hash": "3eda9fcbab0f5dac447e29c3c2b2f2dc", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_utils_py.html", "file": "tests\\test_performance_analyzer_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_utils_90_coverage_py": {"hash": "7ca6c1eab2cb3f2e20734bd3af22897b", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_utils_90_coverage_py.html", "file": "tests\\test_performance_analyzer_utils_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_utils_final_90_py": {"hash": "2aca9a9ca81f224af8f0822d1eee72d0", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_utils_final_90_py.html", "file": "tests\\test_performance_analyzer_utils_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_utils_final_coverage_py": {"hash": "e69b2a2c644d76fb384ef1ec1c8600b8", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_utils_final_coverage_py.html", "file": "tests\\test_performance_analyzer_utils_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_analyzer_utils_final_simple_py": {"hash": "aa2776af18b68e424be08348dd79412e", "index": {"url": "z_a44f0ac069e85531_test_performance_analyzer_utils_final_simple_py.html", "file": "tests\\test_performance_analyzer_utils_final_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_tracker_py": {"hash": "20c91567b908e6854d354bc2883bb102", "index": {"url": "z_a44f0ac069e85531_test_performance_tracker_py.html", "file": "tests\\test_performance_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1225, "n_excluded": 0, "n_missing": 1123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_tracker_new_py": {"hash": "9b9faf591bcf79e934715bce22aef55f", "index": {"url": "z_a44f0ac069e85531_test_performance_tracker_new_py.html", "file": "tests\\test_performance_tracker_new.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 950, "n_excluded": 0, "n_missing": 858, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_tracker_simple_py": {"hash": "210f98921c92954bceab2f7b44585c7a", "index": {"url": "z_a44f0ac069e85531_test_performance_tracker_simple_py.html", "file": "tests\\test_performance_tracker_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 119, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_phase3_integration_py": {"hash": "3d07ff0ab9ae8a8af02a1c289b246642", "index": {"url": "z_a44f0ac069e85531_test_phase3_integration_py.html", "file": "tests\\test_phase3_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_additional_py": {"hash": "a01d6ba8b278eddfaa53340d961b6738", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_additional_py.html", "file": "tests\\test_position_sizer_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_combined_py": {"hash": "4bc1d1b286571f2214e21f920b98d9f9", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_combined_py.html", "file": "tests\\test_position_sizer_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_comprehensive_py": {"hash": "bec962b8d3f31efd0db98bb0c6618c97", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_comprehensive_py.html", "file": "tests\\test_position_sizer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 303, "n_excluded": 0, "n_missing": 242, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_final_90_coverage_py": {"hash": "abcd0a5cdbcf33917535cf770105860e", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_final_90_coverage_py.html", "file": "tests\\test_position_sizer_final_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 130, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_sizer_py": {"hash": "1dc386fc7f139806ffcba2141c1d9012", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_sizer_py.html", "file": "tests\\test_position_sizer_sizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_sizer_90_coverage_py": {"hash": "ab571dd27caad7b2926df00135d7d35e", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_sizer_90_coverage_py.html", "file": "tests\\test_position_sizer_sizer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_sizer_90_plus_py": {"hash": "aff1a4a00cb084dfd983bdc5e05f6f03", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_sizer_90_plus_py.html", "file": "tests\\test_position_sizer_sizer_90_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_position_sizer_sizer_final_py": {"hash": "02285711ae71736524f4f77b08ab85f8", "index": {"url": "z_a44f0ac069e85531_test_position_sizer_sizer_final_py.html", "file": "tests\\test_position_sizer_sizer_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_build_prompt_py": {"hash": "35b9a21fc57efdf219455560afc3cc5e", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_build_prompt_py.html", "file": "tests\\test_prompt_builder_build_prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_builder_py": {"hash": "beb716eca6d7d389ba2cc84b3d2a19c0", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_builder_py.html", "file": "tests\\test_prompt_builder_builder.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_builder_additional_py": {"hash": "3a55ff9601aa3f88739ce767f8a74176", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_builder_additional_py.html", "file": "tests\\test_prompt_builder_builder_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 130, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_builder_final_90_py": {"hash": "97720ae5b885f049b0ca39ee9237d486", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_builder_final_90_py.html", "file": "tests\\test_prompt_builder_builder_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_format_functions_py": {"hash": "61196c73accd115da1b850d72e1dcc73", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_format_functions_py.html", "file": "tests\\test_prompt_builder_format_functions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_prompt_builder_generate_forex_prompt_py": {"hash": "3e44b19b326992796070c70f5ffd755f", "index": {"url": "z_a44f0ac069e85531_test_prompt_builder_generate_forex_prompt_py.html", "file": "tests\\test_prompt_builder_generate_forex_prompt.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_py": {"hash": "1d37926b2e8e28069e4f64722986374a", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_py.html", "file": "tests\\test_qdrant_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_70_coverage_py": {"hash": "7cb227e7fb4f2e458ca9e796f17ea845", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_70_coverage_py.html", "file": "tests\\test_qdrant_service_70_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_additional_py": {"hash": "082ac7f06fb9b5e8a5fff6922553b61f", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_additional_py.html", "file": "tests\\test_qdrant_service_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_advanced_py": {"hash": "8ff38d8f20ea0551e4b430e83a7d2f5b", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_advanced_py.html", "file": "tests\\test_qdrant_service_advanced.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_batch_py": {"hash": "9d774ef55e53b7e71da91e7eb08c1351", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_batch_py.html", "file": "tests\\test_qdrant_service_batch.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_comprehensive_py": {"hash": "f76bf564ffda42bcc0208202a50c9d14", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_comprehensive_py.html", "file": "tests\\test_qdrant_service_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_edge_cases_py": {"hash": "9a350e53bfbbd44faa9487cae072f9f2", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_edge_cases_py.html", "file": "tests\\test_qdrant_service_edge_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_init_py": {"hash": "27a2a53166426fd0c042320a8284f348", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_init_py.html", "file": "tests\\test_qdrant_service_init.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 137, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_qdrant_service_simple_py": {"hash": "fd2cb96548ec4f587ef40e4598dfe5dc", "index": {"url": "z_a44f0ac069e85531_test_qdrant_service_simple_py.html", "file": "tests\\test_qdrant_service_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_regime_detector_hmm_model_py": {"hash": "545a91486585ff0bc01e51b9b880b7b8", "index": {"url": "z_a44f0ac069e85531_test_regime_detector_hmm_model_py.html", "file": "tests\\test_regime_detector_hmm_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 224, "n_excluded": 0, "n_missing": 149, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_regime_detector_hmm_model_90_coverage_py": {"hash": "15e493776422f04da155fcf82cedb755", "index": {"url": "z_a44f0ac069e85531_test_regime_detector_hmm_model_90_coverage_py.html", "file": "tests\\test_regime_detector_hmm_model_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_regime_detector_hmm_model_comprehensive_py": {"hash": "d43fa425479633c2b0832debf3ffe7d9", "index": {"url": "z_a44f0ac069e85531_test_regime_detector_hmm_model_comprehensive_py.html", "file": "tests\\test_regime_detector_hmm_model_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_run_bot_py": {"hash": "3f62de5b2bc1ac05a63373e19dea6516", "index": {"url": "z_a44f0ac069e85531_test_run_bot_py.html", "file": "tests\\test_run_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 251, "n_excluded": 0, "n_missing": 211, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_run_bot_comprehensive_py": {"hash": "ddd197ad95a3aff0a4a55834308507a9", "index": {"url": "z_a44f0ac069e85531_test_run_bot_comprehensive_py.html", "file": "tests\\test_run_bot_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 120, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_run_bot_once_signals_py": {"hash": "5013d6a38d125ccdc53a57d7cbbf75de", "index": {"url": "z_a44f0ac069e85531_test_run_bot_once_signals_py.html", "file": "tests\\test_run_bot_once_signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_90_coverage_py": {"hash": "c0f07098eefd28b1e7540a00e369d2d4", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_90_coverage_py.html", "file": "tests\\test_sentiment_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_additional_py": {"hash": "89ae71ad0f4a99dcb6a85f92119e9789", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_additional_py.html", "file": "tests\\test_sentiment_analyzer_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_py": {"hash": "d9cee47c71ca96f08f99a89c80bc981b", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_py.html", "file": "tests\\test_sentiment_analyzer_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_90_coverage_py": {"hash": "189ce3ce78fab01a5dca982d48cf9586", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_90_coverage_py.html", "file": "tests\\test_sentiment_analyzer_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_full_py": {"hash": "65f2343a79a2a3b9c65480db5dbbfd5f", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_analyzer_full_py.html", "file": "tests\\test_sentiment_analyzer_analyzer_full.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_combined_py": {"hash": "ed299151e5a6b60ba08f2141f319fd26", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_combined_py.html", "file": "tests\\test_sentiment_analyzer_combined.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_comprehensive_py": {"hash": "b0828443db70cbfc040ebaad93f4e3d5", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_comprehensive_py.html", "file": "tests\\test_sentiment_analyzer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 237, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_direct_py": {"hash": "4e95e65eae6da268484463d895c7e20d", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_direct_py.html", "file": "tests\\test_sentiment_analyzer_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_final_90_coverage_py": {"hash": "d442fd2d4db2674c7a767f917100b109", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_final_90_coverage_py.html", "file": "tests\\test_sentiment_analyzer_final_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_py": {"hash": "43e0ced310baf3eab2ba1f50084c7796", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_py.html", "file": "tests\\test_sentiment_analyzer_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 266, "n_excluded": 0, "n_missing": 161, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_90_py": {"hash": "61924ecfe8cbf51f527c8cc4fc159f8c", "index": {"url": "z_a44f0ac069e85531_test_sentiment_analyzer_final_coverage_90_py.html", "file": "tests\\test_sentiment_analyzer_final_coverage_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_py": {"hash": "66b264999e5d4bef3937b5a0ea62d329", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_py.html", "file": "tests\\test_signal_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_90_percent_py": {"hash": "bd22e5447b822c889e7334ed6c46f982", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_90_percent_py.html", "file": "tests\\test_signal_generator_90_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_additional_py": {"hash": "fa1f817fb9ad5b1931b771f068c18a18", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_additional_py.html", "file": "tests\\test_signal_generator_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 234, "n_excluded": 0, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_batch2_90_coverage_py": {"hash": "c46a81a756f0e5ec6cb9936de38639f3", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_batch2_90_coverage_py.html", "file": "tests\\test_signal_generator_batch2_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_batch7_90_coverage_py": {"hash": "f25521d9d300609e6ff591b4aaf723ce", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_batch7_90_coverage_py.html", "file": "tests\\test_signal_generator_batch7_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 236, "n_excluded": 0, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_complete_py": {"hash": "6796496c6ccada6309f1ef4bc80b7af9", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_complete_py.html", "file": "tests\\test_signal_generator_complete.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 288, "n_excluded": 0, "n_missing": 242, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_coverage_py": {"hash": "b659577b42496087731c0437906bfa9c", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_coverage_py.html", "file": "tests\\test_signal_generator_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 0, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_final_py": {"hash": "84fe7ff24b683e5f96eea41e6a71844f", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_final_py.html", "file": "tests\\test_signal_generator_final.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_final_coverage_py": {"hash": "252736f67c86e34181c2619a21320f1c", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_final_coverage_py.html", "file": "tests\\test_signal_generator_final_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_final_simple_py": {"hash": "7f92545cbbd1ac394f8f846b27b45a5c", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_final_simple_py.html", "file": "tests\\test_signal_generator_final_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 206, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_signal_generator_simple_py": {"hash": "ab9213db2590c0b4122da58cbed293a4", "index": {"url": "z_a44f0ac069e85531_test_signal_generator_simple_py.html", "file": "tests\\test_signal_generator_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 186, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_simple_py": {"hash": "a98954517a08a87f7db9834d38d09a52", "index": {"url": "z_a44f0ac069e85531_test_simple_py.html", "file": "tests\\test_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_symbol_processing_py": {"hash": "34ba2be9c632ea951ed5002099a42ddc", "index": {"url": "z_a44f0ac069e85531_test_symbol_processing_py.html", "file": "tests\\test_symbol_processing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 238, "n_excluded": 0, "n_missing": 185, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_system_monitor_py": {"hash": "36d3e23177bf740cbef5aacb0e6b7c1e", "index": {"url": "z_a44f0ac069e85531_test_system_monitor_py.html", "file": "tests\\test_system_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_system_monitor_batch2_90_coverage_py": {"hash": "31b33719859d97d5bda02b08b16f270c", "index": {"url": "z_a44f0ac069e85531_test_system_monitor_batch2_90_coverage_py.html", "file": "tests\\test_system_monitor_batch2_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 174, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_system_monitor_batch6_90_coverage_py": {"hash": "9b4d4908d03485f392d071ced38d15cc", "index": {"url": "z_a44f0ac069e85531_test_system_monitor_batch6_90_coverage_py.html", "file": "tests\\test_system_monitor_batch6_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 183, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_system_monitor_simple_py": {"hash": "a3a15f08262f99c31e6cb5d8424b8b74", "index": {"url": "z_a44f0ac069e85531_test_system_monitor_simple_py.html", "file": "tests\\test_system_monitor_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 236, "n_excluded": 0, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trade_executor_py": {"hash": "85a7076dce1b6a083f9b921a4c01f505", "index": {"url": "z_a44f0ac069e85531_test_trade_executor_py.html", "file": "tests\\test_trade_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 348, "n_excluded": 0, "n_missing": 290, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trade_executor_90_percent_py": {"hash": "32e783d246880378db88d154f4b1045d", "index": {"url": "z_a44f0ac069e85531_test_trade_executor_90_percent_py.html", "file": "tests\\test_trade_executor_90_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trade_executor_simple_py": {"hash": "fd33546b3fd2ca432fcde0b2b5917da8", "index": {"url": "z_a44f0ac069e85531_test_trade_executor_simple_py.html", "file": "tests\\test_trade_executor_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_90_coverage_py": {"hash": "9960aa71dc573e57a27ce2d82a2100f7", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_90_coverage_py.html", "file": "tests\\test_trend_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_additional_py": {"hash": "8c4627753b9bba0368a00a0f6c331203", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_additional_py.html", "file": "tests\\test_trend_analyzer_additional.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_analyzer_py": {"hash": "5cfef51c3edfa3929a10f001bf3982e0", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_analyzer_py.html", "file": "tests\\test_trend_analyzer_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_analyzer_90_coverage_py": {"hash": "89ad735e365f01c4eaa37984cc78ce25", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_analyzer_90_coverage_py.html", "file": "tests\\test_trend_analyzer_analyzer_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_comprehensive_py": {"hash": "bd716bfc5521258ddadaeb17b02f4c04", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_comprehensive_py.html", "file": "tests\\test_trend_analyzer_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_config_validation_py": {"hash": "4492f230f7eb42b51ec553f2d6069637", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_config_validation_py.html", "file": "tests\\test_trend_analyzer_config_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_direct_py": {"hash": "33b52468790f05ce9384494457797ebb", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_direct_py.html", "file": "tests\\test_trend_analyzer_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_isolated_py": {"hash": "43909cd81a179c82fc518af745e145a5", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_isolated_py.html", "file": "tests\\test_trend_analyzer_isolated.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_minimal_py": {"hash": "1943659745b8c70cd0bf5728eeac4029", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_minimal_py.html", "file": "tests\\test_trend_analyzer_minimal.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_trend_analyzer_simple_py": {"hash": "6fd6dac4d62c3bd24284d2d6f3d80e86", "index": {"url": "z_a44f0ac069e85531_test_trend_analyzer_simple_py.html", "file": "tests\\test_trend_analyzer_simple.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_comprehensive_py": {"hash": "0660cd311dbe27bc0485d58f5b7b7d50", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_comprehensive_py.html", "file": "tests\\test_volatility_forecaster_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 248, "n_excluded": 0, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_config_validation_py": {"hash": "cb42f24795a4078d46c07c85ecad11a2", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_config_validation_py.html", "file": "tests\\test_volatility_forecaster_config_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_py": {"hash": "6aeed9b4f1a80157af5dac464a56ef97", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_py.html", "file": "tests\\test_volatility_forecaster_garch_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_coverage_py": {"hash": "0a5deb1c399144c1892b61a528ccc6d1", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_coverage_py.html", "file": "tests\\test_volatility_forecaster_garch_model_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_plus_py": {"hash": "af6e2dcebece642718c8ce680bf4b93a", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_90_plus_py.html", "file": "tests\\test_volatility_forecaster_garch_model_90_plus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 130, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_direct_py": {"hash": "15f2a1818c90ed0e6651ebb3a51c9ae0", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_direct_py.html", "file": "tests\\test_volatility_forecaster_garch_model_direct.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_final_90_py": {"hash": "46519a9b351fc42d41e167ad41b1b561", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_final_90_py.html", "file": "tests\\test_volatility_forecaster_garch_model_final_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_full_py": {"hash": "5d7edfd30c322d10a58edd8b08952943", "index": {"url": "z_a44f0ac069e85531_test_volatility_forecaster_garch_model_full_py.html", "file": "tests\\test_volatility_forecaster_garch_model_full.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b0b94eca26fbf402___init___py": {"hash": "d0ff8811e2b0611214bf2bf82e4888ee", "index": {"url": "z_b0b94eca26fbf402___init___py.html", "file": "tests\\test_volatility_indices\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b0b94eca26fbf402_test_analyzer_py": {"hash": "789969ee60725c680dfec3724194069f", "index": {"url": "z_b0b94eca26fbf402_test_analyzer_py.html", "file": "tests\\test_volatility_indices\\test_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 126, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b0b94eca26fbf402_test_client_py": {"hash": "bcac6e0a1a8ad18cc5da3f4d06999498", "index": {"url": "z_b0b94eca26fbf402_test_client_py.html", "file": "tests\\test_volatility_indices\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b0b94eca26fbf402_test_models_py": {"hash": "ec81b03c8746e4fb671ddb2616871f96", "index": {"url": "z_b0b94eca26fbf402_test_models_py.html", "file": "tests\\test_volatility_indices\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_indices_analyzer_batch3_90_coverage_py": {"hash": "636554b43220cb8a3d6855684ad1bd49", "index": {"url": "z_a44f0ac069e85531_test_volatility_indices_analyzer_batch3_90_coverage_py.html", "file": "tests\\test_volatility_indices_analyzer_batch3_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 132, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_indices_models_90_percent_py": {"hash": "6dbe109a54ccafd84e16fa0eae460c81", "index": {"url": "z_a44f0ac069e85531_test_volatility_indices_models_90_percent_py.html", "file": "tests\\test_volatility_indices_models_90_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volatility_indices_models_phase5h_py": {"hash": "1958e47da019d2fd0faf0ea3f2b6eda4", "index": {"url": "z_a44f0ac069e85531_test_volatility_indices_models_phase5h_py.html", "file": "tests\\test_volatility_indices_models_phase5h.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_296e2f3da3f4e87a___init___py": {"hash": "ee8c8a17c24defd06c12c219575e0047", "index": {"url": "z_296e2f3da3f4e87a___init___py.html", "file": "tests\\test_volume_profile\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_296e2f3da3f4e87a_test_calculator_py": {"hash": "ceefed77cae764b6a03c4137e007522c", "index": {"url": "z_296e2f3da3f4e87a_test_calculator_py.html", "file": "tests\\test_volume_profile\\test_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_296e2f3da3f4e87a_test_client_py": {"hash": "62216be877814e1fe46fb51a9d3ba845", "index": {"url": "z_296e2f3da3f4e87a_test_client_py.html", "file": "tests\\test_volume_profile\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_296e2f3da3f4e87a_test_models_py": {"hash": "2b500a0079070a5381e57960835223c9", "index": {"url": "z_296e2f3da3f4e87a_test_models_py.html", "file": "tests\\test_volume_profile\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_init_batch8_90_coverage_py": {"hash": "3195ea648a61247326db2deb4446c2bf", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_init_batch8_90_coverage_py.html", "file": "tests\\test_volume_profile_init_batch8_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 249, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_models_100_percent_py": {"hash": "0ec371b7def3eb8d3d361b209c8e75cc", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_models_100_percent_py.html", "file": "tests\\test_volume_profile_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_models_90_py": {"hash": "0dcc3cb0a368b86bc5470b78d8fd6aa8", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_models_90_py.html", "file": "tests\\test_volume_profile_models_90.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_models_batch3_90_coverage_py": {"hash": "7604b518ea0ba29cb3484e61f6a00c39", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_models_batch3_90_coverage_py.html", "file": "tests\\test_volume_profile_models_batch3_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_models_batch5_90_coverage_py": {"hash": "8ad528e5ee3e31591bbfd02dae5d6bab", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_models_batch5_90_coverage_py.html", "file": "tests\\test_volume_profile_models_batch5_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_volume_profile_models_phase5i_py": {"hash": "2bac4dbc1f0ffe25c12f80d0ba759a26", "index": {"url": "z_a44f0ac069e85531_test_volume_profile_models_phase5i_py.html", "file": "tests\\test_volume_profile_models_phase5i.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_75fcbe42f7da93f2___init___py": {"hash": "36637c429fdc6161362ce389806e03cc", "index": {"url": "z_75fcbe42f7da93f2___init___py.html", "file": "tests\\test_vwap\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_75fcbe42f7da93f2_test_calculator_py": {"hash": "ec23a5a7c5a18d5f053a5cf1b5b2315c", "index": {"url": "z_75fcbe42f7da93f2_test_calculator_py.html", "file": "tests\\test_vwap\\test_calculator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_75fcbe42f7da93f2_test_client_py": {"hash": "f770f512fbb5fccd3b42630c6ec5d25a", "index": {"url": "z_75fcbe42f7da93f2_test_client_py.html", "file": "tests\\test_vwap\\test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 143, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_75fcbe42f7da93f2_test_models_py": {"hash": "570812d136d5d60e6b73e0ecc3655e3a", "index": {"url": "z_75fcbe42f7da93f2_test_models_py.html", "file": "tests\\test_vwap\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_vwap_init_batch8_90_coverage_py": {"hash": "a3f08ddc7f233ec2c29f38544843afe3", "index": {"url": "z_a44f0ac069e85531_test_vwap_init_batch8_90_coverage_py.html", "file": "tests\\test_vwap_init_batch8_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 0, "n_missing": 214, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_vwap_models_100_percent_py": {"hash": "5753e5204b5b25cf589ef5897d3ab902", "index": {"url": "z_a44f0ac069e85531_test_vwap_models_100_percent_py.html", "file": "tests\\test_vwap_models_100_percent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_vwap_models_batch4_90_coverage_py": {"hash": "4259583bafa53414273da96f8c61a163", "index": {"url": "z_a44f0ac069e85531_test_vwap_models_batch4_90_coverage_py.html", "file": "tests\\test_vwap_models_batch4_90_coverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_vwap_models_phase5j_py": {"hash": "66c90a5ae76515fbc838414755a4b6ab", "index": {"url": "z_a44f0ac069e85531_test_vwap_models_phase5j_py.html", "file": "tests\\test_vwap_models_phase5j.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}