<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\analysis_constants.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\analysis_constants.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">59 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">59<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_21758df60ac2cdd3___main___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_b50040b513dcffe1___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 23:14 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># --- Analysis Constants Module ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">This module contains constants for all analysis modules to avoid circular imports.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Union</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="com"># Import the config class</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">config_settings</span> <span class="key">import</span> <span class="nam">get_config</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="com"># Get the config instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="nam">config</span> <span class="op">=</span> <span class="nam">get_config</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">getLogger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># --- Trend Analyzer Constants ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">TREND_MA_SHORT</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'trend_ma_short'</span><span class="op">,</span> <span class="num">50</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">TREND_MA_LONG</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'trend_ma_long'</span><span class="op">,</span> <span class="num">200</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">TREND_MA_TYPE</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'trend_ma_type'</span><span class="op">,</span> <span class="str">'EMA'</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="com"># --- Pattern Recognizer Constants ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="com"># Weights must sum to 1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="nam">_w_vol</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_conf_weight_vol'</span><span class="op">,</span> <span class="num">0.33</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="nam">_w_trend</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_conf_weight_trend'</span><span class="op">,</span> <span class="num">0.34</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="nam">_w_sr</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_conf_weight_sr'</span><span class="op">,</span> <span class="num">0.33</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="nam">_total_weight</span> <span class="op">=</span> <span class="nam">_w_vol</span> <span class="op">+</span> <span class="nam">_w_trend</span> <span class="op">+</span> <span class="nam">_w_sr</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="nam">PATTERN_CONFIDENCE_WEIGHT_VOL</span> <span class="op">=</span> <span class="nam">_w_vol</span> <span class="op">/</span> <span class="nam">_total_weight</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="nam">PATTERN_CONFIDENCE_WEIGHT_TREND</span> <span class="op">=</span> <span class="nam">_w_trend</span> <span class="op">/</span> <span class="nam">_total_weight</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="nam">PATTERN_CONFIDENCE_WEIGHT_SR</span> <span class="op">=</span> <span class="nam">_w_sr</span> <span class="op">/</span> <span class="nam">_total_weight</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="nam">PATTERN_VOLUME_FACTOR_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_vol_factor'</span><span class="op">,</span> <span class="num">1.5</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">PATTERN_VOLUME_AVG_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_vol_avg_factor'</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="nam">PATTERN_CONFIDENCE_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_confidence_threshold'</span><span class="op">,</span> <span class="num">0.6</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="nam">PATTERN_LOOKBACK_BARS</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_lookback_bars'</span><span class="op">,</span> <span class="num">3</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="nam">PATTERN_TREND_ALIGNMENT_PENALTY</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_trend_penalty'</span><span class="op">,</span> <span class="num">0.2</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="nam">PATTERN_SR_PROXIMITY_ATR_FACTOR</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_sr_atr_factor'</span><span class="op">,</span> <span class="num">0.25</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="nam">PATTERN_RELEVANT_PATTERN_LIST</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_relevant_list'</span><span class="op">,</span> <span class="str">'CDL_ENGULFING,CDL_DOJI_10_0.1,CDL_HAMMER,CDL_INVERTEDHAMMER,CDL_HARAMI,CDL_PIERCING,CDL_MORNINGSTAR,CDL_EVENINGSTAR'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="nam">PATTERN_RELEVANT_PATTERNS</span> <span class="op">=</span> <span class="nam">sorted</span><span class="op">(</span><span class="op">[</span><span class="nam">p</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span> <span class="key">for</span> <span class="nam">p</span> <span class="key">in</span> <span class="nam">PATTERN_RELEVANT_PATTERN_LIST</span><span class="op">.</span><span class="nam">split</span><span class="op">(</span><span class="str">','</span><span class="op">)</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="nam">PATTERN_REVERSAL_PATTERN_KEYWORDS</span> <span class="op">=</span> <span class="nam">sorted</span><span class="op">(</span><span class="op">[</span><span class="nam">k</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span> <span class="key">for</span> <span class="nam">k</span> <span class="key">in</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_reversal_keywords'</span><span class="op">,</span> <span class="str">'HAMMER,ENGULFING,DOJI,STAR,PIERCING'</span><span class="op">)</span><span class="op">.</span><span class="nam">split</span><span class="op">(</span><span class="str">','</span><span class="op">)</span> <span class="key">if</span> <span class="nam">k</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t"><span class="nam">PATTERN_VOL_MA_PERIOD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_vol_ma_period'</span><span class="op">,</span> <span class="num">20</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="nam">PATTERN_ATR_PERIOD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_atr_period'</span><span class="op">,</span> <span class="num">14</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t"><span class="nam">PATTERN_ATR_MA_PERIOD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_atr_ma_period'</span><span class="op">,</span> <span class="num">50</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t"><span class="nam">PATTERN_ATR_MA_TYPE</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_atr_ma_type'</span><span class="op">,</span> <span class="str">'EMA'</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t"><span class="nam">PATTERN_ATR_MULT</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'pattern_atr_mult'</span><span class="op">,</span> <span class="num">2.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="com"># --- Sentiment Analyzer Constants ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t"><span class="nam">SENTIMENT_ENABLE_ANALYSIS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">enable_sentiment_analysis</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'enable_sentiment_analysis'</span><span class="op">)</span> <span class="key">else</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t"><span class="nam">SENTIMENT_ALPHA_VANTAGE_API_KEY</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">alpha_vantage_api_key</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'alpha_vantage_api_key'</span><span class="op">)</span> <span class="key">else</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t"><span class="nam">SENTIMENT_METHOD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">sentiment_method</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'sentiment_method'</span><span class="op">)</span> <span class="key">else</span> <span class="op">(</span><span class="str">'alphavantage'</span> <span class="key">if</span> <span class="nam">SENTIMENT_ALPHA_VANTAGE_API_KEY</span> <span class="key">else</span> <span class="str">'vader'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t"><span class="nam">SENTIMENT_AV_NEWS_LIMIT</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">av_news_limit</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'av_news_limit'</span><span class="op">)</span> <span class="key">else</span> <span class="num">25</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="nam">SENTIMENT_AV_TOPICS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">av_topics</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'av_topics'</span><span class="op">)</span> <span class="key">else</span> <span class="str">"financial_markets,economy_macro"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t"><span class="nam">SENTIMENT_NEWSAPI_KEY</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">newsapi_key</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'newsapi_key'</span><span class="op">)</span> <span class="key">else</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t"><span class="nam">SENTIMENT_NEWSAPI_SOURCES</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">newsapi_sources</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'newsapi_sources'</span><span class="op">)</span> <span class="key">else</span> <span class="str">''</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t"><span class="nam">SENTIMENT_NEWSAPI_LOOKBACK_HOURS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">newsapi_lookback_hours</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'newsapi_lookback_hours'</span><span class="op">)</span> <span class="key">else</span> <span class="num">72</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t"><span class="nam">SENTIMENT_NEWSAPI_PAGE_SIZE</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">newsapi_page_size</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'newsapi_page_size'</span><span class="op">)</span> <span class="key">else</span> <span class="num">25</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t"><span class="nam">SENTIMENT_VADER_POS_THRESHOLD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">sentiment_vader_pos_threshold</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'sentiment_vader_pos_threshold'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.05</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t"><span class="nam">SENTIMENT_VADER_NEG_THRESHOLD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">sentiment_vader_neg_threshold</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'sentiment_vader_neg_threshold'</span><span class="op">)</span> <span class="key">else</span> <span class="op">-</span><span class="num">0.05</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t"><span class="nam">SENTIMENT_TRANSFORMER_MODEL_NAME</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">sentiment_transformer_model</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'sentiment_transformer_model'</span><span class="op">)</span> <span class="key">else</span> <span class="str">'ProsusAI/finbert'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t"><span class="nam">SENTIMENT_TRANSFORMER_DEVICE</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">transformer_device</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'transformer_device'</span><span class="op">)</span> <span class="key">else</span> <span class="op">-</span><span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t"><span class="nam">SENTIMENT_CACHE_SECONDS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">sentiment_cache_seconds</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'sentiment_cache_seconds'</span><span class="op">)</span> <span class="key">else</span> <span class="num">86400</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t"><span class="com"># --- Volatility Forecaster Constants ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="nam">GARCH_P</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_p</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_p'</span><span class="op">)</span> <span class="key">else</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="nam">GARCH_Q</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_q</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_q'</span><span class="op">)</span> <span class="key">else</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="nam">GARCH_DIST</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_dist</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_dist'</span><span class="op">)</span> <span class="key">else</span> <span class="str">'skewt'</span>  <span class="com"># Common distributions: 'normal', 't', 'skewt'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="nam">GARCH_LOOKBACK_BARS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_lookback_bars</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_lookback_bars'</span><span class="op">)</span> <span class="key">else</span> <span class="num">200</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="com"># Thresholds for status (Annualized Std Dev)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t"><span class="nam">GARCH_VOL_LOW_THRESHOLD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_vol_low_threshold</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_vol_low_threshold'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.10</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="nam">GARCH_VOL_HIGH_THRESHOLD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_vol_high_threshold</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_vol_high_threshold'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.25</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t"><span class="com"># Annualization factor (e.g., for daily returns: sqrt(252), for hourly: sqrt(252*24))</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t"><span class="nam">GARCH_ANNUALIZATION_FACTOR</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">garch_annualization_factor</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'garch_annualization_factor'</span><span class="op">)</span> <span class="key">else</span> <span class="nam">np</span><span class="op">.</span><span class="nam">sqrt</span><span class="op">(</span><span class="num">252</span> <span class="op">*</span> <span class="num">24</span> <span class="op">*</span> <span class="num">12</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t"><span class="com"># --- Macro Analyzer Constants ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t"><span class="nam">MACRO_ENABLE_ANALYSIS</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'enable_macro_analysis'</span><span class="op">,</span> <span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t"><span class="com"># VIX Thresholds</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t"><span class="nam">MACRO_VIX_RISK_OFF_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_risk_off_vix_threshold'</span><span class="op">,</span> <span class="num">25.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t"><span class="nam">MACRO_VIX_RISK_ON_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_risk_on_vix_threshold'</span><span class="op">,</span> <span class="num">18.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t"><span class="com"># Rate Trend Interpretation</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t"><span class="nam">MACRO_RATE_TREND_RISING_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_rate_rising_threshold'</span><span class="op">,</span> <span class="num">2</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t"><span class="nam">MACRO_RATE_TREND_FALLING_THRESHOLD</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_rate_falling_threshold'</span><span class="op">,</span> <span class="num">2</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t"><span class="com"># Event Window</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t"><span class="nam">MACRO_EVENT_WINDOW_HOURS</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_event_window_hours'</span><span class="op">,</span> <span class="num">2.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t"><span class="nam">MACRO_EVENT_INCLUDE_MEDIUM</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_event_include_medium'</span><span class="op">,</span> <span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t"><span class="nam">MACRO_CACHE_SECONDS</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'macro_context_cache_seconds'</span><span class="op">,</span> <span class="num">60</span> <span class="op">*</span> <span class="num">5</span><span class="op">)</span>  <span class="com"># Cache context for 5 mins</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_21758df60ac2cdd3___main___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_b50040b513dcffe1___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 23:14 -0500
        </p>
    </div>
</footer>
</body>
</html>
