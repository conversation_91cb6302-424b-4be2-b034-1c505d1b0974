<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\gemini_client.py: 19%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\gemini_client.py</b>:
            <span class="pc_cov">19%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">121 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">23<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">98<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_8cfda85c8b792357_producer_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_54e5a98f26b615b9___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:07 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># --- Core Python Libraries ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">import</span> <span class="nam">re</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="com"># Import types for type hinting</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Any</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="com"># --- Third-Party Libraries ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">tenacity</span> <span class="key">import</span> <span class="nam">retry</span><span class="op">,</span> <span class="nam">stop_after_attempt</span><span class="op">,</span> <span class="nam">wait_exponential</span><span class="op">,</span> <span class="nam">retry_if_exception_type</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">import</span> <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">    <span class="key">import</span> <span class="nam">google</span><span class="op">.</span><span class="nam">generativeai</span> <span class="key">as</span> <span class="nam">genai</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="com"># Import specific types needed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="key">from</span> <span class="nam">google</span><span class="op">.</span><span class="nam">generativeai</span><span class="op">.</span><span class="nam">types</span> <span class="key">import</span> <span class="nam">GenerationConfig</span> <span class="com"># Removed SafetySettingDict here</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">GEMINI_AVAILABLE</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="key">except</span> <span class="nam">ImportError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"[WARNING] google-generativeai not found. AI features disabled."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="nam">GEMINI_AVAILABLE</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="com"># Define dummy types if library is missing to avoid NameErrors later</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="key">class</span> <span class="nam">GenerationConfig</span><span class="op">:</span> <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="com"># SafetySettingDict = Dict # No longer needed for the hint</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="com"># --- Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="com"># Import the config class</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">config_settings</span> <span class="key">import</span> <span class="nam">get_config</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="com"># Get the config instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="nam">config</span> <span class="op">=</span> <span class="nam">get_config</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="com"># Define module-level constants</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="nam">GEMINI_AVAILABLE</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">gemini_available</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="com"># --- Gemini Functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="com"># ...(format_dataframe_for_prompt and generate_forex_prompt remain the same)...</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="key">def</span> <span class="nam">format_dataframe_for_prompt</span><span class="op">(</span><span class="nam">df</span><span class="op">:</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">,</span> <span class="nam">symbol_digits</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">max_bars</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">50</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="str">"""Formats DataFrame for Gemini prompt.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="str">        df: DataFrame containing OHLCV and indicator data</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="str">        symbol_digits: Number of decimal places for price formatting</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t"><span class="str">        max_bars: Maximum number of bars to include in the output</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t"><span class="str">        str: Formatted string representation of the DataFrame</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="key">if</span> <span class="nam">df</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span> <span class="key">return</span> <span class="str">"No data."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="com"># Ensure max_bars doesn't exceed available rows</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">actual_max_bars</span> <span class="op">=</span> <span class="nam">min</span><span class="op">(</span><span class="nam">max_bars</span><span class="op">,</span> <span class="nam">len</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">df_tail</span> <span class="op">=</span> <span class="nam">df</span><span class="op">.</span><span class="nam">tail</span><span class="op">(</span><span class="nam">actual_max_bars</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="nam">formatted_rows</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="op">;</span> <span class="nam">price_format</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">.</span><span class="op">{</span><span class="nam">symbol_digits</span><span class="op">}</span><span class="fst">f</span><span class="fst">"</span><span class="op">;</span> <span class="nam">indicator_format</span> <span class="op">=</span> <span class="str">".2f"</span><span class="op">;</span> <span class="nam">atr_format</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">.</span><span class="op">{</span><span class="nam">symbol_digits</span><span class="op">}</span><span class="fst">f</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="com"># Define column names based on config</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">rsi_col</span><span class="op">=</span><span class="fst">f'</span><span class="fst">RSI_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">rsi_period</span><span class="op">}</span><span class="fst">'</span><span class="op">;</span> <span class="nam">macd_col</span><span class="op">=</span><span class="fst">f'</span><span class="fst">MACD_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_fast</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_slow</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_signal</span><span class="op">}</span><span class="fst">'</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">    <span class="nam">macd_h_col</span><span class="op">=</span><span class="fst">f'</span><span class="fst">MACDh_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_fast</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_slow</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_signal</span><span class="op">}</span><span class="fst">'</span><span class="op">;</span> <span class="nam">macd_s_col</span><span class="op">=</span><span class="fst">f'</span><span class="fst">MACDs_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_fast</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_slow</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">macd_signal</span><span class="op">}</span><span class="fst">'</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="nam">atr_col</span><span class="op">=</span><span class="str">'ATR'</span> <span class="com"># Assuming ATR is renamed consistently in indicators.py</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="key">for</span> <span class="nam">row</span> <span class="key">in</span> <span class="nam">df_tail</span><span class="op">.</span><span class="nam">itertuples</span><span class="op">(</span><span class="nam">index</span><span class="op">=</span><span class="key">True</span><span class="op">)</span><span class="op">:</span> <span class="com"># index=True to get the timestamp</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="com"># Use getattr for safety, provide default NaN</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">            <span class="nam">o</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="str">'Open'</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="nam">h</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="str">'High'</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">            <span class="nam">l</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">            <span class="nam">c</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="nam">v</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span> <span class="com"># Default volume to 0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">            <span class="nam">row_str</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">T:</span><span class="op">{</span><span class="nam">row</span><span class="op">.</span><span class="nam">Index</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">'%Y-%m-%d %H:%M'</span><span class="op">)</span><span class="op">}</span><span class="fst">, O:</span><span class="op">{</span><span class="nam">o</span><span class="op">:</span><span class="op">{</span><span class="nam">price_format</span><span class="op">}</span><span class="op">}</span><span class="fst">, H:</span><span class="op">{</span><span class="nam">h</span><span class="op">:</span><span class="op">{</span><span class="nam">price_format</span><span class="op">}</span><span class="op">}</span><span class="fst">, L:</span><span class="op">{</span><span class="nam">l</span><span class="op">:</span><span class="op">{</span><span class="nam">price_format</span><span class="op">}</span><span class="op">}</span><span class="fst">, C:</span><span class="op">{</span><span class="nam">c</span><span class="op">:</span><span class="op">{</span><span class="nam">price_format</span><span class="op">}</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="com"># Safely convert volume</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">                <span class="nam">vol_int</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">float</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span><span class="op">)</span> <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="key">else</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">            <span class="key">except</span> <span class="op">(</span><span class="nam">ValueError</span><span class="op">,</span> <span class="nam">TypeError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">                <span class="nam">vol_int</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, V:</span><span class="op">{</span><span class="nam">vol_int</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">            <span class="com"># Add indicators if they exist and are not NaN</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">            <span class="nam">rsi_val</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="nam">rsi_col</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">            <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">rsi_val</span><span class="op">)</span><span class="op">:</span> <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, RSI:</span><span class="op">{</span><span class="nam">rsi_val</span><span class="op">:</span><span class="op">{</span><span class="nam">indicator_format</span><span class="op">}</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">            <span class="nam">macd_val</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="nam">macd_col</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">            <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">macd_val</span><span class="op">)</span><span class="op">:</span> <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, MACD:</span><span class="op">{</span><span class="nam">macd_val</span><span class="op">:</span><span class="fst">.5f</span><span class="op">}</span><span class="fst">"</span> <span class="com"># Use fixed precision for MACD</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">            <span class="nam">macdh_val</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="nam">macd_h_col</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">macdh_val</span><span class="op">)</span><span class="op">:</span> <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, MACDH:</span><span class="op">{</span><span class="nam">macdh_val</span><span class="op">:</span><span class="fst">.5f</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">            <span class="nam">macds_val</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="nam">macd_s_col</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">macds_val</span><span class="op">)</span><span class="op">:</span> <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, MACDS:</span><span class="op">{</span><span class="nam">macds_val</span><span class="op">:</span><span class="fst">.5f</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="nam">atr_val</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">row</span><span class="op">,</span> <span class="nam">atr_col</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="key">if</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">notna</span><span class="op">(</span><span class="nam">atr_val</span><span class="op">)</span><span class="op">:</span> <span class="nam">row_str</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">, ATR:</span><span class="op">{</span><span class="nam">atr_val</span><span class="op">:</span><span class="op">{</span><span class="nam">atr_format</span><span class="op">}</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="nam">formatted_rows</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">row_str</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="key">except</span> <span class="nam">AttributeError</span> <span class="key">as</span> <span class="nam">ae</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="com"># Log attribute errors more specifically if a column is missing unexpectedly</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Attribute error formatting row </span><span class="op">{</span><span class="nam">row</span><span class="op">.</span><span class="nam">Index</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">ae</span><span class="op">}</span><span class="fst">. Check indicator calculation.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">            <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">fmt_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="com"># Log other formatting errors</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">General error formatting row </span><span class="op">{</span><span class="nam">row</span><span class="op">.</span><span class="nam">Index</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">fmt_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">            <span class="key">continue</span> <span class="com"># Skip problematic row</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="key">return</span> <span class="str">"\n"</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">formatted_rows</span><span class="op">)</span> <span class="key">if</span> <span class="nam">formatted_rows</span> <span class="key">else</span> <span class="str">"No rows formatted."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t"><span class="key">def</span> <span class="nam">generate_forex_prompt</span><span class="op">(</span><span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">m5_data_str</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">h4_data_str</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">                     <span class="nam">position_status</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">performance_summary</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">news_context</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">strategy_context</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">    <span class="str">"""Generates the prompt for Gemini AI including strategy context.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t"><span class="str">        symbol: The symbol to analyze</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t"><span class="str">        m5_data_str: Formatted M5 data string</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t"><span class="str">        h4_data_str: Formatted H4 data string</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t"><span class="str">        position_status: Current position status</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t"><span class="str">        performance_summary: Performance summary string</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t"><span class="str">        news_context: News context string</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t"><span class="str">        strategy_context: Strategy context string</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t"><span class="str">        str: Formatted prompt for Gemini AI</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="com"># Limit strategy context length to avoid overly long prompts</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="nam">max_strat_len</span><span class="op">=</span><span class="num">1000</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">    <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">strategy_context</span><span class="op">)</span><span class="op">></span><span class="nam">max_strat_len</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="nam">strategy_context</span><span class="op">=</span><span class="nam">strategy_context</span><span class="op">[</span><span class="op">:</span><span class="nam">max_strat_len</span><span class="op">]</span><span class="op">+</span><span class="str">"..."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">    <span class="com"># Use config constants for bar counts</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="nam">prompt</span> <span class="op">=</span> <span class="fst">f"""</span><span class="fst">**Analyze </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> data and provide ONLY: BUY, SELL, or HOLD.**</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t"><span class="fst">**Rules &amp; Context:**</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t"><span class="fst">1. Output: Single word (`BUY`/`SELL`/`HOLD`). No explanations.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t"><span class="fst">2. Goal: M5 direction confirmed by H4 trend.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t"><span class="fst">3. M5 TA: RSI, MACD, ATR focus. Patterns/divergences.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t"><span class="fst">4. H4 Trend: Confirm M5 signal. Avoid contradictions.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="fst">5. News: High importance. Can override technicals. Note impact/time/deviations.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t"><span class="fst">6. KB Context: Use for strategy guidance. `</span><span class="op">{</span><span class="nam">strategy_context</span><span class="op">}</span><span class="fst">`</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t"><span class="fst">7. Position: Respect status (</span><span class="op">{</span><span class="nam">position_status</span><span class="op">}</span><span class="fst">). Avoid opposing unless strong reversal. HOLD if in pos.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t"><span class="fst">8. Performance: Minor context. `</span><span class="op">{</span><span class="nam">performance_summary</span><span class="op">}</span><span class="fst">`</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t"><span class="fst">9. HOLD if: Mixed signals, conflicts, news overrides, in position.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t"><span class="fst">**Info:** Symbol=</span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">, Position=</span><span class="op">{</span><span class="nam">position_status</span><span class="op">}</span><span class="fst">, News=</span><span class="op">{</span><span class="nam">news_context</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t"><span class="fst">**H4 Data (</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">gemini_h4_bars</span><span class="op">}</span><span class="fst"> bars):** </span><span class="op">{</span><span class="nam">h4_data_str</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t"><span class="fst">**M5 Data (</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">gemini_m5_bars</span><span class="op">}</span><span class="fst"> bars):** </span><span class="op">{</span><span class="nam">m5_data_str</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t"><span class="fst">**Signal:**</span><span class="fst">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">    <span class="key">return</span> <span class="nam">prompt</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t"><span class="op">@</span><span class="nam">retry</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">    <span class="nam">stop</span><span class="op">=</span><span class="nam">stop_after_attempt</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Retry up to 3 times</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">    <span class="nam">wait</span><span class="op">=</span><span class="nam">wait_exponential</span><span class="op">(</span><span class="nam">multiplier</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">min</span><span class="op">=</span><span class="num">2</span><span class="op">,</span> <span class="nam">max</span><span class="op">=</span><span class="num">10</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Exponential backoff: 2s, 4s, 8s</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">    <span class="nam">retry</span><span class="op">=</span><span class="nam">retry_if_exception_type</span><span class="op">(</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">Timeout</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">ConnectionError</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">HTTPError</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">    <span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">    <span class="nam">reraise</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t"><span class="key">def</span> <span class="nam">get_gemini_signal</span><span class="op">(</span><span class="nam">prompt</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">model_name</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">genai_model_instance</span><span class="op">:</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">    <span class="str">"""Gets trading signal from Gemini AI model instance.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t"><span class="str">        prompt: The prompt to send to Gemini</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t"><span class="str">        model_name: The name of the Gemini model</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t"><span class="str">        genai_model_instance: The Gemini model instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t"><span class="str">        symbol: The symbol to get a signal for</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t"><span class="str">        adapter: Logger adapter for logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t"><span class="str">        str: The trading signal (BUY, SELL, or HOLD)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Getting Gemini signal for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">config</span><span class="op">.</span><span class="nam">gemini_available</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">genai_model_instance</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Gemini library/model instance not available for signal generation."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="key">return</span> <span class="str">"HOLD"</span> <span class="com"># Return HOLD if Gemini is not configured/available</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="com"># Define safety settings to block harmful content minimally</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">        <span class="com"># Use the corrected type hint: List[Dict[str, str]]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">        <span class="nam">safety_settings</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">str</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">            <span class="op">{</span><span class="str">"category"</span><span class="op">:</span> <span class="str">"HARM_CATEGORY_HARASSMENT"</span><span class="op">,</span> <span class="str">"threshold"</span><span class="op">:</span> <span class="str">"BLOCK_NONE"</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="op">{</span><span class="str">"category"</span><span class="op">:</span> <span class="str">"HARM_CATEGORY_HATE_SPEECH"</span><span class="op">,</span> <span class="str">"threshold"</span><span class="op">:</span> <span class="str">"BLOCK_NONE"</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">            <span class="op">{</span><span class="str">"category"</span><span class="op">:</span> <span class="str">"HARM_CATEGORY_SEXUALLY_EXPLICIT"</span><span class="op">,</span> <span class="str">"threshold"</span><span class="op">:</span> <span class="str">"BLOCK_NONE"</span><span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">            <span class="op">{</span><span class="str">"category"</span><span class="op">:</span> <span class="str">"HARM_CATEGORY_DANGEROUS_CONTENT"</span><span class="op">,</span> <span class="str">"threshold"</span><span class="op">:</span> <span class="str">"BLOCK_NONE"</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">        <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="com"># Define generation configuration</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="nam">generation_config</span> <span class="op">=</span> <span class="nam">GenerationConfig</span><span class="op">(</span><span class="nam">temperature</span><span class="op">=</span><span class="num">0.2</span><span class="op">,</span> <span class="nam">max_output_tokens</span><span class="op">=</span><span class="num">10</span><span class="op">)</span> <span class="com"># Low temp for deterministic output</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Sending prompt to Gemini model '</span><span class="op">{</span><span class="nam">model_name</span><span class="op">}</span><span class="fst">' for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">        <span class="com"># adapter.debug(f"Prompt for {symbol}:\n---\n{prompt}\n---") # Uncomment to log full prompt if needed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">        <span class="nam">response</span> <span class="op">=</span> <span class="nam">genai_model_instance</span><span class="op">.</span><span class="nam">generate_content</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">            <span class="nam">prompt</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">            <span class="nam">generation_config</span><span class="op">=</span><span class="nam">generation_config</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">            <span class="nam">safety_settings</span><span class="op">=</span><span class="nam">safety_settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="nam">signal_text</span> <span class="op">=</span> <span class="str">""</span><span class="op">;</span> <span class="nam">reason</span> <span class="op">=</span> <span class="str">"None"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">            <span class="com"># Check for blocked prompt or empty candidates</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">            <span class="key">if</span> <span class="nam">response</span><span class="op">.</span><span class="nam">prompt_feedback</span> <span class="key">and</span> <span class="nam">response</span><span class="op">.</span><span class="nam">prompt_feedback</span><span class="op">.</span><span class="nam">block_reason</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">                <span class="nam">reason</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Blocked (</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">prompt_feedback</span><span class="op">.</span><span class="nam">block_reason</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="key">elif</span> <span class="key">not</span> <span class="nam">response</span><span class="op">.</span><span class="nam">candidates</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">                 <span class="nam">reason</span> <span class="op">=</span> <span class="str">"No candidates returned"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">                <span class="com"># Safely access the first candidate and its content parts</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">                <span class="nam">candidate</span> <span class="op">=</span> <span class="nam">response</span><span class="op">.</span><span class="nam">candidates</span><span class="op">[</span><span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">                <span class="key">if</span> <span class="nam">candidate</span><span class="op">.</span><span class="nam">content</span> <span class="key">and</span> <span class="nam">candidate</span><span class="op">.</span><span class="nam">content</span><span class="op">.</span><span class="nam">parts</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">                    <span class="nam">signal_text</span> <span class="op">=</span> <span class="nam">candidate</span><span class="op">.</span><span class="nam">content</span><span class="op">.</span><span class="nam">parts</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">text</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">                <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">                    <span class="nam">reason</span> <span class="op">=</span> <span class="str">"Empty content/parts in candidate"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">        <span class="key">except</span> <span class="op">(</span><span class="nam">AttributeError</span><span class="op">,</span> <span class="nam">IndexError</span><span class="op">,</span> <span class="nam">Exception</span><span class="op">)</span> <span class="key">as</span> <span class="nam">parse_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">            <span class="nam">reason</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Response Parsing Error (</span><span class="op">{</span><span class="nam">parse_err</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error parsing Gemini response for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">parse_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Raw Gemini Response: </span><span class="op">{</span><span class="nam">response</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span> <span class="com"># Log raw response on parsing error</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">        <span class="com"># Handle errors or blocked responses</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">        <span class="key">if</span> <span class="nam">reason</span> <span class="op">!=</span> <span class="str">"None"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Gemini response issue for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">reason</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">            <span class="key">return</span> <span class="str">"HOLD"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">        <span class="com"># Extract BUY/SELL/HOLD using regex for robustness</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">        <span class="nam">match</span> <span class="op">=</span> <span class="nam">re</span><span class="op">.</span><span class="nam">search</span><span class="op">(</span><span class="str">r"\b(BUY|SELL|HOLD)\b"</span><span class="op">,</span> <span class="nam">signal_text</span><span class="op">)</span> <span class="com"># Use word boundaries</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">        <span class="key">if</span> <span class="nam">match</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">            <span class="nam">signal</span> <span class="op">=</span> <span class="nam">match</span><span class="op">.</span><span class="nam">group</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Gemini Signal Received: </span><span class="op">{</span><span class="nam">signal</span><span class="op">}</span><span class="fst"> for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="key">return</span> <span class="nam">signal</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">            <span class="com"># Log the unexpected output before returning HOLD</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Gemini returned non-standard output '</span><span class="op">{</span><span class="nam">signal_text</span><span class="op">}</span><span class="fst">' for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">. Defaulting to HOLD.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">            <span class="key">return</span> <span class="str">"HOLD"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">        <span class="com"># Catch potential API errors or other exceptions during the call</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Gemini API call or processing error for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="key">return</span> <span class="str">"HOLD"</span> <span class="com"># Default to HOLD on any exception</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t"><span class="com"># --- Add setup_gemini function ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t"><span class="op">@</span><span class="nam">retry</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">    <span class="nam">stop</span><span class="op">=</span><span class="nam">stop_after_attempt</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Retry up to 3 times</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">    <span class="nam">wait</span><span class="op">=</span><span class="nam">wait_exponential</span><span class="op">(</span><span class="nam">multiplier</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">min</span><span class="op">=</span><span class="num">2</span><span class="op">,</span> <span class="nam">max</span><span class="op">=</span><span class="num">10</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Exponential backoff: 2s, 4s, 8s</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">    <span class="nam">retry</span><span class="op">=</span><span class="nam">retry_if_exception_type</span><span class="op">(</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">Timeout</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">ConnectionError</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">        <span class="nam">requests</span><span class="op">.</span><span class="nam">exceptions</span><span class="op">.</span><span class="nam">HTTPError</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">    <span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t"><span class="key">def</span> <span class="nam">setup_gemini</span><span class="op">(</span><span class="nam">api_key</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">model_name</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Any</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">    <span class="str">"""Configures Gemini and returns the model instance.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t"><span class="str">        api_key: The Gemini API key</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t"><span class="str">        model_name: The name of the Gemini model to use</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t"><span class="str">        adapter: Logger adapter for logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t"><span class="str">        Any: The Gemini model instance, or None if setup fails</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">config</span><span class="op">.</span><span class="nam">gemini_available</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Gemini library not available. Cannot setup Gemini."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">api_key</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Gemini API key not provided. Cannot setup Gemini."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">model_name</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Gemini model name not provided. Cannot setup Gemini."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Configuring Gemini with API key and model: </span><span class="op">{</span><span class="nam">model_name</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">        <span class="nam">genai</span><span class="op">.</span><span class="nam">configure</span><span class="op">(</span><span class="nam">api_key</span><span class="op">=</span><span class="nam">api_key</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">        <span class="nam">model_instance</span> <span class="op">=</span> <span class="nam">genai</span><span class="op">.</span><span class="nam">GenerativeModel</span><span class="op">(</span><span class="nam">model_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Gemini configured successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">        <span class="key">return</span> <span class="nam">model_instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Gemini configuration failed: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_8cfda85c8b792357_producer_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_54e5a98f26b615b9___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:07 -0500
        </p>
    </div>
</footer>
</body>
</html>
