<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\mt5_client.py: 8%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\mt5_client.py</b>:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">411 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">32<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">379<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_6696b9cca54af58b_model_monitoring_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_21758df60ac2cdd3_mt5_constants_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 23:00 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># --- Core Python Libraries ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">import</span> <span class="nam">time</span> <span class="key">as</span> <span class="nam">time_sleep</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">timezone</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">uuid</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Union</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="com"># --- Third-Party Libraries ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">MetaTrader5</span> <span class="key">as</span> <span class="nam">mt5</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">tenacity</span> <span class="key">import</span> <span class="nam">retry</span><span class="op">,</span> <span class="nam">stop_after_attempt</span><span class="op">,</span> <span class="nam">wait_exponential</span><span class="op">,</span> <span class="nam">retry_if_exception_type</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="com"># --- Local Modules ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="com"># Import MT5 constants from our constants module</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">mt5_constants</span> <span class="key">import</span> <span class="op">*</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># --- Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="com"># Import the config class</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">config_settings</span> <span class="key">import</span> <span class="nam">get_config</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="com"># Get the config instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="nam">config</span> <span class="op">=</span> <span class="nam">get_config</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="com"># --- Event Bus Integration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="com"># Import event bus components (with fallback if not available)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">event_bus</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">        <span class="nam">MarketDataProducer</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="nam">OrderProducer</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">        <span class="nam">TradeProducer</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="nam">get_event_bus_config</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="nam">TimeFrame</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">mt5_event_producer</span> <span class="key">import</span> <span class="nam">MT5EventProducer</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="key">except</span> <span class="nam">ImportError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="com"># Initialize event producers if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="nam">_market_data_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="nam">_order_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="nam">_trade_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t"><span class="nam">_mt5_event_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="key">def</span> <span class="nam">_init_event_producers</span><span class="op">(</span><span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="str">"""Initialize event producers if event bus is available and enabled."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="key">global</span> <span class="nam">_market_data_producer</span><span class="op">,</span> <span class="nam">_order_producer</span><span class="op">,</span> <span class="nam">_trade_producer</span><span class="op">,</span> <span class="nam">_mt5_event_producer</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">EVENT_BUS_AVAILABLE</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Event bus not available. Skipping event producer initialization."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">        <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">        <span class="nam">event_bus_config</span> <span class="op">=</span> <span class="nam">get_event_bus_config</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">event_bus_config</span><span class="op">.</span><span class="nam">enabled</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Event bus is disabled in configuration. Skipping event producer initialization."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Initializing event producers..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="nam">_market_data_producer</span> <span class="op">=</span> <span class="nam">MarketDataProducer</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">        <span class="nam">_order_producer</span> <span class="op">=</span> <span class="nam">OrderProducer</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="nam">_trade_producer</span> <span class="op">=</span> <span class="nam">TradeProducer</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">        <span class="nam">_mt5_event_producer</span> <span class="op">=</span> <span class="nam">MT5EventProducer</span><span class="op">(</span><span class="nam">logger</span><span class="op">=</span><span class="nam">adapter</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Event producers initialized successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error initializing event producers: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">        <span class="nam">_market_data_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="nam">_order_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">        <span class="nam">_trade_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">        <span class="nam">_mt5_event_producer</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="com"># --- MT5 Client Singleton ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="nam">_mt5_client_instance</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="key">def</span> <span class="nam">get_mt5_client</span><span class="op">(</span><span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="str">'mt5'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="str">    Get the MT5 client instance.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t"><span class="str">        adapter: Logger adapter for logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t"><span class="str">        The MT5 client instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="key">global</span> <span class="nam">_mt5_client_instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="key">if</span> <span class="nam">_mt5_client_instance</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">        <span class="nam">_mt5_client_instance</span> <span class="op">=</span> <span class="nam">mt5</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"MT5 client instance created"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">    <span class="key">return</span> <span class="nam">_mt5_client_instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t"><span class="com"># --- MT5 Functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t"><span class="op">@</span><span class="nam">retry</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">    <span class="nam">stop</span><span class="op">=</span><span class="nam">stop_after_attempt</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Retry up to 3 times</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="nam">wait</span><span class="op">=</span><span class="nam">wait_exponential</span><span class="op">(</span><span class="nam">multiplier</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">min</span><span class="op">=</span><span class="num">2</span><span class="op">,</span> <span class="nam">max</span><span class="op">=</span><span class="num">10</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Exponential backoff: 2s, 4s, 8s</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t"><span class="key">def</span> <span class="nam">initialize_mt5</span><span class="op">(</span><span class="nam">login</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">password</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">server</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">path</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="str">"""Initializes connection to MetaTrader 5."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Attempting to initialize MT5 connection..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="nam">mt5</span><span class="op">.</span><span class="nam">shutdown</span><span class="op">(</span><span class="op">)</span><span class="op">;</span> <span class="nam">time_sleep</span><span class="op">.</span><span class="nam">sleep</span><span class="op">(</span><span class="num">0.5</span><span class="op">)</span> <span class="com"># Ensure clean state</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="nam">init_params</span> <span class="op">=</span> <span class="op">{</span><span class="str">'login'</span><span class="op">:</span> <span class="nam">login</span><span class="op">,</span> <span class="str">'password'</span><span class="op">:</span> <span class="nam">password</span><span class="op">,</span> <span class="str">'server'</span><span class="op">:</span> <span class="nam">server</span><span class="op">,</span> <span class="str">'timeout'</span><span class="op">:</span> <span class="num">20000</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">    <span class="key">if</span> <span class="nam">path</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="nam">init_params</span><span class="op">[</span><span class="str">'path'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">path</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Using MT5 path: </span><span class="op">{</span><span class="nam">path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"No specific MT5 path provided."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">initialize</span><span class="op">(</span><span class="op">**</span><span class="nam">init_params</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">        <span class="nam">error</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">last_error</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">MT5 initialize() failed: </span><span class="op">{</span><span class="nam">error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">            <span class="nam">mt5</span><span class="op">.</span><span class="nam">shutdown</span><span class="op">(</span><span class="op">)</span> <span class="com"># Attempt shutdown on failure</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">            <span class="key">pass</span> <span class="com"># Ignore errors during shutdown after failure</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="com"># Retry on specific MT5 errors that might be transient</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">        <span class="key">if</span> <span class="nam">error</span><span class="op">[</span><span class="num">0</span><span class="op">]</span> <span class="key">in</span> <span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="num">2</span><span class="op">,</span> <span class="num">3</span><span class="op">,</span> <span class="num">4</span><span class="op">,</span> <span class="num">5</span><span class="op">,</span> <span class="num">128</span><span class="op">,</span> <span class="num">129</span><span class="op">,</span> <span class="num">130</span><span class="op">,</span> <span class="num">131</span><span class="op">,</span> <span class="num">132</span><span class="op">,</span> <span class="num">133</span><span class="op">,</span> <span class="num">134</span><span class="op">,</span> <span class="num">135</span><span class="op">,</span> <span class="num">136</span><span class="op">,</span> <span class="num">137</span><span class="op">,</span> <span class="num">138</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Retrying MT5 initialization due to potentially transient error code </span><span class="op">{</span><span class="nam">error</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">            <span class="key">raise</span> <span class="nam">Exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Transient MT5 initialization error: </span><span class="op">{</span><span class="nam">error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span> <span class="com"># Return False on failure</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"MT5 initialized successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="nam">term_info</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">terminal_info</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">    <span class="nam">acc_info</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">account_info</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">    <span class="key">if</span> <span class="nam">term_info</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Terminal: Connected=</span><span class="op">{</span><span class="nam">term_info</span><span class="op">.</span><span class="nam">connected</span><span class="op">}</span><span class="fst">, DLLs Allowed=</span><span class="op">{</span><span class="nam">term_info</span><span class="op">.</span><span class="nam">dlls_allowed</span><span class="op">}</span><span class="fst">, Build=</span><span class="op">{</span><span class="nam">term_info</span><span class="op">.</span><span class="nam">build</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Could not retrieve MT5 TerminalInfo after successful initialization."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">    <span class="key">if</span> <span class="nam">acc_info</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Account: Login=</span><span class="op">{</span><span class="nam">acc_info</span><span class="op">.</span><span class="nam">login</span><span class="op">}</span><span class="fst">, Server=</span><span class="op">{</span><span class="nam">acc_info</span><span class="op">.</span><span class="nam">server</span><span class="op">}</span><span class="fst">, Bal=</span><span class="op">{</span><span class="nam">acc_info</span><span class="op">.</span><span class="nam">balance</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> </span><span class="op">{</span><span class="nam">acc_info</span><span class="op">.</span><span class="nam">currency</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">        <span class="com"># Set the magic_number in the config based on the login ID after successful connection</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span> <span class="op">=</span> <span class="nam">acc_info</span><span class="op">.</span><span class="nam">login</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Set magic_number to account login: </span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Could not retrieve MT5 AccountInfo after successful initialization."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">MT5 Version: </span><span class="op">{</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">version</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="com"># Initialize event producers if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="nam">_init_event_producers</span><span class="op">(</span><span class="nam">adapter</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">    <span class="com"># Start MT5 event producer if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="key">and</span> <span class="nam">_mt5_event_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">            <span class="com"># Get symbols from config</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">symbols</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">symbols</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'symbols'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">config</span><span class="op">.</span><span class="nam">symbols</span> <span class="key">else</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">            <span class="com"># Get timeframes from config</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">            <span class="nam">timeframes</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">            <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'timeframe'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">config</span><span class="op">.</span><span class="nam">timeframe</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">                <span class="nam">timeframes</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">_get_timeframe_str</span><span class="op">(</span><span class="nam">config</span><span class="op">.</span><span class="nam">timeframe</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'timeframe_h1'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">config</span><span class="op">.</span><span class="nam">timeframe_h1</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">                <span class="nam">timeframes</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">_get_timeframe_str</span><span class="op">(</span><span class="nam">config</span><span class="op">.</span><span class="nam">timeframe_h1</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">            <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'timeframe_h4'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">config</span><span class="op">.</span><span class="nam">timeframe_h4</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">                <span class="nam">timeframes</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">_get_timeframe_str</span><span class="op">(</span><span class="nam">config</span><span class="op">.</span><span class="nam">timeframe_h4</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">            <span class="com"># Start market data streaming</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">            <span class="key">if</span> <span class="nam">symbols</span> <span class="key">and</span> <span class="nam">timeframes</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Starting market data streaming for </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">symbols</span><span class="op">)</span><span class="op">}</span><span class="fst"> symbols and </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">timeframes</span><span class="op">)</span><span class="op">}</span><span class="fst"> timeframes...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">                <span class="nam">_mt5_event_producer</span><span class="op">.</span><span class="nam">start_market_data_streaming</span><span class="op">(</span><span class="nam">symbols</span><span class="op">,</span> <span class="nam">timeframes</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">            <span class="com"># Start order/trade monitoring</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Starting order/trade monitoring..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="nam">_mt5_event_producer</span><span class="op">.</span><span class="nam">start_order_trade_monitoring</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"MT5 event producer started successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error starting MT5 event producer: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">    <span class="key">return</span> <span class="key">True</span> <span class="com"># Return True only after successful initialization</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t"><span class="key">def</span> <span class="nam">_get_timeframe_str</span><span class="op">(</span><span class="nam">timeframe_enum</span><span class="op">:</span> <span class="nam">int</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">    <span class="str">"""Convert MT5 timeframe enum to string representation."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">    <span class="nam">timeframe_map</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M1</span><span class="op">:</span> <span class="str">"M1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M5</span><span class="op">:</span> <span class="str">"M5"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M15</span><span class="op">:</span> <span class="str">"M15"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M30</span><span class="op">:</span> <span class="str">"M30"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_H1</span><span class="op">:</span> <span class="str">"H1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_H4</span><span class="op">:</span> <span class="str">"H4"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_D1</span><span class="op">:</span> <span class="str">"D1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_W1</span><span class="op">:</span> <span class="str">"W1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_MN1</span><span class="op">:</span> <span class="str">"MN1"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">    <span class="key">return</span> <span class="nam">timeframe_map</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">timeframe_enum</span><span class="op">,</span> <span class="str">"M5"</span><span class="op">)</span>  <span class="com"># Default to M5 if unknown</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t"><span class="op">@</span><span class="nam">retry</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">    <span class="nam">stop</span><span class="op">=</span><span class="nam">stop_after_attempt</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Retry up to 3 times</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="nam">wait</span><span class="op">=</span><span class="nam">wait_exponential</span><span class="op">(</span><span class="nam">multiplier</span><span class="op">=</span><span class="num">1</span><span class="op">,</span> <span class="nam">min</span><span class="op">=</span><span class="num">2</span><span class="op">,</span> <span class="nam">max</span><span class="op">=</span><span class="num">10</span><span class="op">)</span><span class="op">,</span>  <span class="com"># Exponential backoff: 2s, 4s, 8s</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t"><span class="key">def</span> <span class="nam">get_historical_data</span><span class="op">(</span><span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">timeframe</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">,</span> <span class="nam">count</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">200</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">    <span class="str">"""Fetches historical bar data AND ENSURES UPPERCASE OHLCV COLUMNS."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Fetching </span><span class="op">{</span><span class="nam">count</span><span class="op">}</span><span class="fst"> bars for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> timeframe </span><span class="op">{</span><span class="nam">timeframe</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="nam">rates</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">copy_rates_from_pos</span><span class="op">(</span><span class="nam">symbol</span><span class="op">,</span> <span class="nam">timeframe</span><span class="op">,</span> <span class="num">0</span><span class="op">,</span> <span class="nam">count</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="key">if</span> <span class="nam">rates</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="nam">error</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">last_error</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed rates fetch for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="com"># Retry on specific MT5 errors that might be transient</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">            <span class="key">if</span> <span class="nam">error</span><span class="op">[</span><span class="num">0</span><span class="op">]</span> <span class="key">in</span> <span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="num">2</span><span class="op">,</span> <span class="num">3</span><span class="op">,</span> <span class="num">4</span><span class="op">,</span> <span class="num">5</span><span class="op">,</span> <span class="num">128</span><span class="op">,</span> <span class="num">129</span><span class="op">,</span> <span class="num">130</span><span class="op">,</span> <span class="num">131</span><span class="op">,</span> <span class="num">132</span><span class="op">,</span> <span class="num">133</span><span class="op">,</span> <span class="num">134</span><span class="op">,</span> <span class="num">135</span><span class="op">,</span> <span class="num">136</span><span class="op">,</span> <span class="num">137</span><span class="op">,</span> <span class="num">138</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Retrying due to potentially transient MT5 error code </span><span class="op">{</span><span class="nam">error</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">                <span class="key">raise</span> <span class="nam">Exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Transient MT5 error: </span><span class="op">{</span><span class="nam">error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">rates</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">No rates returned for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">            <span class="key">return</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">(</span><span class="op">)</span> <span class="com"># Return empty DataFrame instead of None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">        <span class="nam">df</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">(</span><span class="nam">rates</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">        <span class="nam">df</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_datetime</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">,</span> <span class="nam">unit</span><span class="op">=</span><span class="str">'s'</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">        <span class="com"># Create a copy with time as column for event publishing</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">        <span class="nam">df_with_time</span> <span class="op">=</span> <span class="nam">df</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="com"># Set time as index for the returned DataFrame (maintain backward compatibility)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">set_index</span><span class="op">(</span><span class="str">'time'</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">        <span class="nam">rename_map</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">            <span class="str">'open'</span><span class="op">:</span>  <span class="str">'Open'</span><span class="op">,</span> <span class="str">'high'</span><span class="op">:</span>  <span class="str">'High'</span><span class="op">,</span> <span class="str">'low'</span><span class="op">:</span>   <span class="str">'Low'</span><span class="op">,</span> <span class="str">'close'</span><span class="op">:</span> <span class="str">'Close'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">            <span class="str">'tick_volume'</span><span class="op">:</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="str">'real_volume'</span><span class="op">:</span> <span class="str">'RealVolume'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">rename</span><span class="op">(</span><span class="nam">columns</span><span class="op">=</span><span class="nam">rename_map</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'ignore'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">        <span class="nam">df_with_time</span><span class="op">.</span><span class="nam">rename</span><span class="op">(</span><span class="nam">columns</span><span class="op">=</span><span class="nam">rename_map</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'ignore'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">        <span class="nam">required_cols</span> <span class="op">=</span> <span class="op">[</span><span class="str">'Open'</span><span class="op">,</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">,</span> <span class="str">'Volume'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">        <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">required_cols</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">            <span class="key">if</span> <span class="nam">col</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">                <span class="nam">df</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">                <span class="nam">df_with_time</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Added missing standard column '</span><span class="op">{</span><span class="nam">col</span><span class="op">}</span><span class="fst">' as NaN for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> TF </span><span class="op">{</span><span class="nam">timeframe</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">            <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="op">[</span><span class="str">'Open'</span><span class="op">,</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">                <span class="nam">df</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">                <span class="nam">df_with_time</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df_with_time</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">            <span class="key">if</span> <span class="str">'Volume'</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">                 <span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span><span class="op">.</span><span class="nam">fillna</span><span class="op">(</span><span class="num">0</span><span class="op">)</span><span class="op">.</span><span class="nam">astype</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">int64</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">                 <span class="nam">df_with_time</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df_with_time</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span><span class="op">.</span><span class="nam">fillna</span><span class="op">(</span><span class="num">0</span><span class="op">)</span><span class="op">.</span><span class="nam">astype</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">int64</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">type_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">             <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error converting OHLCV types for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">type_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Retrieved and standardized </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span><span class="op">}</span><span class="fst"> bars for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> TF </span><span class="op">{</span><span class="nam">timeframe</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">        <span class="com"># Publish to event bus if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="key">and</span> <span class="nam">_market_data_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">                <span class="com"># Convert timeframe to string</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">                <span class="nam">timeframe_str</span> <span class="op">=</span> <span class="nam">_get_timeframe_str</span><span class="op">(</span><span class="nam">timeframe</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">                <span class="com"># Prepare data for event bus</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">                <span class="nam">candles</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">                <span class="key">for</span> <span class="nam">_</span><span class="op">,</span> <span class="nam">row</span> <span class="key">in</span> <span class="nam">df_with_time</span><span class="op">.</span><span class="nam">iterrows</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">                    <span class="nam">candle</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">                        <span class="str">'open'</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'Open'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">                        <span class="str">'high'</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'High'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">                        <span class="str">'low'</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'Low'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">                        <span class="str">'close'</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'Close'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">                        <span class="str">'volume'</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span><span class="op">)</span> <span class="key">if</span> <span class="str">'Volume'</span> <span class="key">in</span> <span class="nam">row</span> <span class="key">else</span> <span class="num">0.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">                        <span class="str">'time'</span><span class="op">:</span> <span class="nam">row</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">row</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">,</span> <span class="nam">datetime</span><span class="op">)</span> <span class="key">else</span> <span class="nam">row</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">                    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">                    <span class="nam">candles</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">candle</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">                <span class="com"># Publish to event bus</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">                <span class="nam">_market_data_producer</span><span class="op">.</span><span class="nam">publish_ohlcv</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">                    <span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">                    <span class="nam">timeframe</span><span class="op">=</span><span class="nam">timeframe_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">                    <span class="nam">data</span><span class="op">=</span><span class="nam">candles</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">                    <span class="nam">source</span><span class="op">=</span><span class="str">"mt5_client"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Published </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">candles</span><span class="op">)</span><span class="op">}</span><span class="fst"> </span><span class="op">{</span><span class="nam">timeframe_str</span><span class="op">}</span><span class="fst"> candles for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> to event bus</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error publishing market data to event bus: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">        <span class="key">return</span> <span class="nam">df</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error in get_historical_data for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">        <span class="com"># Re-raise to trigger retry</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">        <span class="key">if</span> <span class="str">"Transient MT5 error"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">            <span class="key">raise</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t"><span class="key">def</span> <span class="nam">place_trade</span><span class="op">(</span><span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">volume</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="nam">trade_type</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">price</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="nam">atr_value</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">                <span class="nam">point</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="nam">digits</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">                <span class="nam">garch_info</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span> <span class="nam">hmm_info</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t"><span class="str">    Places a trade order with SL/TP calculated using ATR.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t"><span class="str">    If enable_adaptive_sltp is True, uses GARCH/HMM context to adjust multipliers.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t"><span class="str">    Respects dynamic Stops/Freeze Levels and normalizes prices.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Preparing trade </span><span class="op">{</span><span class="nam">trade_type</span><span class="op">}</span><span class="fst"> for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">    <span class="com"># --- Initial Validations ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">    <span class="key">if</span> <span class="nam">volume</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid volume (</span><span class="op">{</span><span class="nam">volume</span><span class="op">}</span><span class="fst">) for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">    <span class="key">if</span> <span class="nam">point</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid point </span><span class="op">{</span><span class="nam">point</span><span class="op">}</span><span class="fst"> for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">    <span class="key">if</span> <span class="nam">atr_value</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">atr_value</span><span class="op">)</span> <span class="key">or</span> <span class="nam">atr_value</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid ATR value (</span><span class="op">{</span><span class="nam">atr_value</span><span class="op">}</span><span class="fst">) for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">    <span class="key">if</span> <span class="nam">price</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid trade price (</span><span class="op">{</span><span class="nam">price</span><span class="op">}</span><span class="fst">) for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">    <span class="com"># --- Get Symbol Info at Runtime ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">    <span class="nam">symbol_info</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">symbol_info</span><span class="op">(</span><span class="nam">symbol</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">symbol_info</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Cannot get symbol info for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">. Aborting.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">    <span class="nam">tick_size</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">symbol_info</span><span class="op">,</span> <span class="str">'trade_tick_size'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">    <span class="nam">stops_level_points</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">symbol_info</span><span class="op">,</span> <span class="str">'trade_stops_level'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">    <span class="nam">freeze_level_points</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">symbol_info</span><span class="op">,</span> <span class="str">'trade_freeze_level'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">    <span class="nam">spread_points</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">symbol_info</span><span class="op">,</span> <span class="str">'spread'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">    <span class="key">if</span> <span class="nam">tick_size</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">tick_size</span><span class="op">,</span> <span class="op">(</span><span class="nam">int</span><span class="op">,</span> <span class="nam">float</span><span class="op">)</span><span class="op">)</span> <span class="key">or</span> <span class="nam">tick_size</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid tick_size (</span><span class="op">{</span><span class="nam">tick_size</span><span class="op">}</span><span class="fst">) reported for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">. Aborting.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">         <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">[</span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">] TickSize: </span><span class="op">{</span><span class="nam">tick_size</span><span class="op">}</span><span class="fst">, Point: </span><span class="op">{</span><span class="nam">point</span><span class="op">}</span><span class="fst">, Digits: </span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">, StopsLevelPts: </span><span class="op">{</span><span class="nam">stops_level_points</span><span class="op">}</span><span class="fst">, FreezeLevelPts: </span><span class="op">{</span><span class="nam">freeze_level_points</span><span class="op">}</span><span class="fst">, SpreadPts: </span><span class="op">{</span><span class="nam">spread_points</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">    <span class="com"># --- === Determine SL/TP Multipliers === ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">    <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">    <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span> <span class="op">*</span> <span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">    <span class="key">if</span> <span class="nam">config</span><span class="op">.</span><span class="nam">enable_adaptive_sltp</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Applying Adaptive SL/TP logic inside place_trade..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">            <span class="nam">base_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">base_sl_atr_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'base_sl_atr_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">            <span class="nam">base_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">base_tp_atr_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'base_tp_atr_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="nam">base_sl_mult</span> <span class="op">*</span> <span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">            <span class="nam">garch_factor</span> <span class="op">=</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">            <span class="key">if</span> <span class="nam">garch_info</span> <span class="key">and</span> <span class="nam">garch_info</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'status'</span><span class="op">)</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">garch_info</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'error'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">                <span class="nam">status</span> <span class="op">=</span> <span class="nam">garch_info</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">                <span class="com"># Get the GARCH factor from config or use default</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">                <span class="nam">garch_factor_attr</span> <span class="op">=</span> <span class="fst">f'</span><span class="fst">sltp_factor_garch_</span><span class="op">{</span><span class="nam">status</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">                <span class="nam">garch_factor</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">garch_factor_attr</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">garch_factor_attr</span><span class="op">)</span> <span class="key">else</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">GARCH Status: </span><span class="op">{</span><span class="nam">status</span><span class="op">}</span><span class="fst">, Factor: </span><span class="op">{</span><span class="nam">garch_factor</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">            <span class="key">else</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"No valid GARCH status for adaptive SL/TP factor."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">            <span class="nam">hmm_sl_factor</span> <span class="op">=</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">            <span class="nam">hmm_tp_factor</span> <span class="op">=</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">            <span class="key">if</span> <span class="nam">hmm_info</span> <span class="key">and</span> <span class="nam">hmm_info</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'state_label'</span><span class="op">)</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">hmm_info</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'error'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">                <span class="nam">label</span> <span class="op">=</span> <span class="nam">hmm_info</span><span class="op">[</span><span class="str">'state_label'</span><span class="op">]</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">' '</span><span class="op">,</span> <span class="str">'_'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">                <span class="com"># Get the HMM factors from config or use defaults</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t">                <span class="nam">hmm_sl_factor_attr</span> <span class="op">=</span> <span class="fst">f'</span><span class="fst">sl_factor_hmm_</span><span class="op">{</span><span class="nam">label</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t">                <span class="nam">hmm_tp_factor_attr</span> <span class="op">=</span> <span class="fst">f'</span><span class="fst">tp_factor_hmm_</span><span class="op">{</span><span class="nam">label</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t">                <span class="nam">hmm_sl_factor</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">hmm_sl_factor_attr</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">hmm_sl_factor_attr</span><span class="op">)</span> <span class="key">else</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">                <span class="nam">hmm_tp_factor</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">hmm_tp_factor_attr</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">hmm_tp_factor_attr</span><span class="op">)</span> <span class="key">else</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">HMM State: </span><span class="op">{</span><span class="nam">label</span><span class="op">}</span><span class="fst">, SL Factor: </span><span class="op">{</span><span class="nam">hmm_sl_factor</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, TP Factor: </span><span class="op">{</span><span class="nam">hmm_tp_factor</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">            <span class="key">else</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"No valid HMM state for adaptive SL/TP factors."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">            <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">0.1</span><span class="op">,</span> <span class="nam">base_sl_mult</span> <span class="op">*</span> <span class="nam">hmm_sl_factor</span> <span class="op">*</span> <span class="nam">garch_factor</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">            <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">0.1</span><span class="op">,</span> <span class="nam">base_tp_mult</span> <span class="op">*</span> <span class="nam">hmm_tp_factor</span> <span class="op">*</span> <span class="nam">garch_factor</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">            <span class="nam">min_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">adaptive_min_sl_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'adaptive_min_sl_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">            <span class="nam">max_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">adaptive_max_sl_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'adaptive_max_sl_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="num">5.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">            <span class="nam">min_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">adaptive_min_tp_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'adaptive_min_tp_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">            <span class="nam">max_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">adaptive_max_tp_mult</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'adaptive_max_tp_mult'</span><span class="op">)</span> <span class="key">else</span> <span class="num">10.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">            <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min_sl_mult</span><span class="op">,</span> <span class="nam">min</span><span class="op">(</span><span class="nam">active_sl_mult</span><span class="op">,</span> <span class="nam">max_sl_mult</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">            <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min_tp_mult</span><span class="op">,</span> <span class="nam">min</span><span class="op">(</span><span class="nam">active_tp_mult</span><span class="op">,</span> <span class="nam">max_tp_mult</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adaptive Multipliers Used: SL=</span><span class="op">{</span><span class="nam">active_sl_mult</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, TP=</span><span class="op">{</span><span class="nam">active_tp_mult</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">        <span class="key">except</span> <span class="nam">ValueError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">             <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error parsing adaptive SL/TP factors from .env: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">. Using default fixed multipliers.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">             <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">             <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span> <span class="op">*</span> <span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e_adapt</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">             <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Unexpected error in adaptive SL/TP logic: </span><span class="op">{</span><span class="nam">e_adapt</span><span class="op">}</span><span class="fst">. Using default fixed multipliers.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">             <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">             <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span> <span class="op">*</span> <span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">         <span class="nam">active_sl_mult</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">atr_multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">         <span class="nam">active_tp_mult</span> <span class="op">=</span> <span class="nam">active_sl_mult</span> <span class="op">*</span> <span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Using Fixed Multipliers: SL=</span><span class="op">{</span><span class="nam">active_sl_mult</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, TP based on RR=</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">risk_reward_ratio</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">    <span class="com"># --- SL/TP Calculation ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">    <span class="nam">sl_dist_price</span> <span class="op">=</span> <span class="nam">atr_value</span> <span class="op">*</span> <span class="nam">active_sl_mult</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">    <span class="nam">tp_dist_price</span> <span class="op">=</span> <span class="nam">atr_value</span> <span class="op">*</span> <span class="nam">active_tp_mult</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">    <span class="nam">order_type</span> <span class="op">=</span> <span class="key">None</span><span class="op">;</span> <span class="nam">sl</span> <span class="op">=</span> <span class="num">0.0</span><span class="op">;</span> <span class="nam">tp</span> <span class="op">=</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">    <span class="key">if</span> <span class="nam">trade_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">        <span class="nam">order_type</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">        <span class="nam">sl</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">-</span> <span class="nam">sl_dist_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">        <span class="nam">tp</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">+</span> <span class="nam">tp_dist_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">    <span class="key">elif</span> <span class="nam">trade_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_SELL</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">        <span class="nam">order_type</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_SELL</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">        <span class="nam">sl</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">+</span> <span class="nam">sl_dist_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">        <span class="nam">tp</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">-</span> <span class="nam">tp_dist_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t">    <span class="key">else</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid trade type: </span><span class="op">{</span><span class="nam">trade_type</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">    <span class="nam">calculated_sl</span> <span class="op">=</span> <span class="nam">round</span><span class="op">(</span><span class="nam">sl</span><span class="op">,</span> <span class="nam">digits</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">    <span class="nam">calculated_tp</span> <span class="op">=</span> <span class="nam">round</span><span class="op">(</span><span class="nam">tp</span><span class="op">,</span> <span class="nam">digits</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">calculated_sl</span><span class="op">)</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">calculated_tp</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Non-finite SL/TP (</span><span class="op">{</span><span class="nam">calculated_sl</span><span class="op">}</span><span class="fst">/</span><span class="op">{</span><span class="nam">calculated_tp</span><span class="op">}</span><span class="fst">) after applying multipliers for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">    <span class="key">if</span> <span class="nam">calculated_sl</span> <span class="op">==</span> <span class="nam">price</span> <span class="key">or</span> <span class="nam">calculated_tp</span> <span class="op">==</span> <span class="nam">price</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Calculated SL or TP is equal to entry price for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">. Check ATR/Multipliers.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">    <span class="com"># --- Minimum Distance Adjustment ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">    <span class="nam">final_sl</span> <span class="op">=</span> <span class="nam">calculated_sl</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">    <span class="nam">final_tp</span> <span class="op">=</span> <span class="nam">calculated_tp</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">    <span class="nam">buffer_points</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">trade_stop_buffer_points</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'trade_stop_buffer_points'</span><span class="op">)</span> <span class="key">else</span> <span class="num">3</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">    <span class="nam">required_distance_points</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">stops_level_points</span><span class="op">,</span> <span class="nam">freeze_level_points</span><span class="op">)</span> <span class="op">+</span> <span class="nam">spread_points</span> <span class="op">+</span> <span class="nam">buffer_points</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">    <span class="nam">min_distance_price</span> <span class="op">=</span> <span class="nam">required_distance_points</span> <span class="op">*</span> <span class="nam">point</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">[</span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">] Min required distance points: </span><span class="op">{</span><span class="nam">required_distance_points</span><span class="op">}</span><span class="fst"> = </span><span class="op">{</span><span class="nam">min_distance_price</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst"> price units</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">    <span class="key">if</span> <span class="nam">order_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">price</span> <span class="op">-</span> <span class="nam">calculated_sl</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">min_distance_price</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">            <span class="nam">final_sl</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">-</span> <span class="nam">min_distance_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adjusting BUY SL due to min distance. Old: </span><span class="op">{</span><span class="nam">calculated_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, New: </span><span class="op">{</span><span class="nam">final_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">calculated_tp</span> <span class="op">-</span> <span class="nam">price</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">min_distance_price</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">             <span class="nam">final_tp</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">+</span> <span class="nam">min_distance_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">             <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adjusting BUY TP due to min distance. Old: </span><span class="op">{</span><span class="nam">calculated_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, New: </span><span class="op">{</span><span class="nam">final_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">    <span class="key">elif</span> <span class="nam">order_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_SELL</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">calculated_sl</span> <span class="op">-</span> <span class="nam">price</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">min_distance_price</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">            <span class="nam">final_sl</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">+</span> <span class="nam">min_distance_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adjusting SELL SL due to min distance. Old: </span><span class="op">{</span><span class="nam">calculated_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, New: </span><span class="op">{</span><span class="nam">final_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">price</span> <span class="op">-</span> <span class="nam">calculated_tp</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">min_distance_price</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">             <span class="nam">final_tp</span> <span class="op">=</span> <span class="nam">price</span> <span class="op">-</span> <span class="nam">min_distance_price</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">             <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adjusting SELL TP due to min distance. Old: </span><span class="op">{</span><span class="nam">calculated_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, New: </span><span class="op">{</span><span class="nam">final_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">    <span class="com"># --- Normalize final SL and TP to Tick Size ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">final_sl</span><span class="op">)</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">final_tp</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Intermediate SL (</span><span class="op">{</span><span class="nam">final_sl</span><span class="op">}</span><span class="fst">) or TP (</span><span class="op">{</span><span class="nam">final_tp</span><span class="op">}</span><span class="fst">) became non-finite before normalization. Aborting.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t">    <span class="nam">normalized_sl</span> <span class="op">=</span> <span class="nam">round</span><span class="op">(</span><span class="nam">round</span><span class="op">(</span><span class="nam">final_sl</span> <span class="op">/</span> <span class="nam">tick_size</span><span class="op">)</span> <span class="op">*</span> <span class="nam">tick_size</span><span class="op">,</span> <span class="nam">digits</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">    <span class="nam">normalized_tp</span> <span class="op">=</span> <span class="nam">round</span><span class="op">(</span><span class="nam">round</span><span class="op">(</span><span class="nam">final_tp</span> <span class="op">/</span> <span class="nam">tick_size</span><span class="op">)</span> <span class="op">*</span> <span class="nam">tick_size</span><span class="op">,</span> <span class="nam">digits</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">[</span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">] Calc SL/TP: </span><span class="op">{</span><span class="nam">calculated_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">/</span><span class="op">{</span><span class="nam">calculated_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">. Final Normalized SL/TP: </span><span class="op">{</span><span class="nam">normalized_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">/</span><span class="op">{</span><span class="nam">normalized_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">    <span class="key">if</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">normalized_sl</span> <span class="op">-</span> <span class="nam">price</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">tick_size</span> <span class="op">/</span> <span class="num">2</span> <span class="key">or</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">normalized_tp</span> <span class="op">-</span> <span class="nam">price</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">tick_size</span> <span class="op">/</span> <span class="num">2</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Normalized SL (</span><span class="op">{</span><span class="nam">normalized_sl</span><span class="op">}</span><span class="fst">) or TP (</span><span class="op">{</span><span class="nam">normalized_tp</span><span class="op">}</span><span class="fst">) is too close to price (</span><span class="op">{</span><span class="nam">price</span><span class="op">}</span><span class="fst">) after normalization. Aborting trade.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">         <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">    <span class="com"># --- Create Request ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">    <span class="nam">trade_desc_part</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="str">'BUY'</span> <span class="key">if</span> <span class="nam">order_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span> <span class="key">else</span> <span class="str">'SELL'</span><span class="op">}</span><span class="fst"> </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> </span><span class="op">{</span><span class="nam">volume</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> @ </span><span class="op">{</span><span class="nam">price</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">    <span class="nam">final_trade_desc</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">trade_desc_part</span><span class="op">}</span><span class="fst">, SL=</span><span class="op">{</span><span class="nam">normalized_sl</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, TP=</span><span class="op">{</span><span class="nam">normalized_tp</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Prepared request: </span><span class="op">{</span><span class="nam">final_trade_desc</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">    <span class="nam">request</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t">        <span class="str">"action"</span><span class="op">:</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TRADE_ACTION_DEAL</span><span class="op">,</span> <span class="str">"symbol"</span><span class="op">:</span> <span class="nam">symbol</span><span class="op">,</span> <span class="str">"volume"</span><span class="op">:</span> <span class="nam">volume</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">        <span class="str">"type"</span><span class="op">:</span> <span class="nam">order_type</span><span class="op">,</span> <span class="str">"price"</span><span class="op">:</span> <span class="nam">price</span><span class="op">,</span> <span class="str">"sl"</span><span class="op">:</span> <span class="nam">normalized_sl</span><span class="op">,</span> <span class="str">"tp"</span><span class="op">:</span> <span class="nam">normalized_tp</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">        <span class="str">"deviation"</span><span class="op">:</span> <span class="num">20</span><span class="op">,</span> <span class="str">"magic"</span><span class="op">:</span> <span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">,</span> <span class="str">"comment"</span><span class="op">:</span> <span class="str">"PGB V3 Adaptive"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">        <span class="str">"type_time"</span><span class="op">:</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TIME_GTC</span><span class="op">,</span> <span class="str">"type_filling"</span><span class="op">:</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_FILLING_FOK</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Trade request dict: </span><span class="op">{</span><span class="nam">request</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">    <span class="com"># --- Order Check ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Checking order request for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">    <span class="nam">check_result</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">        <span class="nam">check_result</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">order_check</span><span class="op">(</span><span class="nam">request</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Exception during order_check: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">    <span class="key">if</span> <span class="nam">check_result</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">check_result</span><span class="op">,</span> <span class="str">'retcode'</span><span class="op">)</span> <span class="key">or</span> <span class="nam">check_result</span><span class="op">.</span><span class="nam">retcode</span> <span class="op">!=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">        <span class="nam">err_comment</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">check_result</span><span class="op">,</span> <span class="str">'comment'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span> <span class="key">if</span> <span class="nam">check_result</span> <span class="key">else</span> <span class="str">'N/A'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">        <span class="nam">err_code</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">check_result</span><span class="op">,</span> <span class="str">'retcode'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span> <span class="key">if</span> <span class="nam">check_result</span> <span class="key">else</span> <span class="str">'N/A'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">        <span class="nam">last_mt5_error</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">last_error</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Order check FAILED </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">err_comment</span><span class="op">}</span><span class="fst"> (Code: </span><span class="op">{</span><span class="nam">err_code</span><span class="op">}</span><span class="fst">) MT5 LastError: </span><span class="op">{</span><span class="nam">last_mt5_error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed Check Request Details: </span><span class="op">{</span><span class="nam">request</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed Check Result Object: </span><span class="op">{</span><span class="nam">check_result</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Order check OK for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: Code=</span><span class="op">{</span><span class="nam">check_result</span><span class="op">.</span><span class="nam">retcode</span><span class="op">}</span><span class="fst">, Comment='</span><span class="op">{</span><span class="nam">check_result</span><span class="op">.</span><span class="nam">comment</span><span class="op">}</span><span class="fst">'</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">    <span class="com"># --- Order Send ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">    <span class="key">if</span> <span class="nam">config</span><span class="op">.</span><span class="nam">dry_run</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">DRY RUN: Would place </span><span class="op">{</span><span class="nam">final_trade_desc</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">        <span class="key">return</span> <span class="num">999999</span> <span class="com"># Return placeholder for dry run success</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Sending order for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t">    <span class="nam">result</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">order_send</span><span class="op">(</span><span class="nam">request</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Exception during order_send: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t476" href="#t476">476</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t477" href="#t477">477</a></span><span class="t">    <span class="key">if</span> <span class="nam">result</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">result</span><span class="op">,</span> <span class="str">'retcode'</span><span class="op">)</span> <span class="key">or</span> <span class="nam">result</span><span class="op">.</span><span class="nam">retcode</span> <span class="op">!=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TRADE_RETCODE_DONE</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t478" href="#t478">478</a></span><span class="t">        <span class="nam">err_comment</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">result</span><span class="op">,</span> <span class="str">'comment'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span> <span class="key">if</span> <span class="nam">result</span> <span class="key">else</span> <span class="str">'N/A'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t479" href="#t479">479</a></span><span class="t">        <span class="nam">err_code</span> <span class="op">=</span> <span class="nam">getattr</span><span class="op">(</span><span class="nam">result</span><span class="op">,</span> <span class="str">'retcode'</span><span class="op">,</span> <span class="str">'N/A'</span><span class="op">)</span> <span class="key">if</span> <span class="nam">result</span> <span class="key">else</span> <span class="str">'N/A'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t480" href="#t480">480</a></span><span class="t">        <span class="nam">last_mt5_error</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">last_error</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t481" href="#t481">481</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Order send FAILED </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: '</span><span class="op">{</span><span class="nam">err_comment</span><span class="op">}</span><span class="fst">' (Code: </span><span class="op">{</span><span class="nam">err_code</span><span class="op">}</span><span class="fst">) MT5 LastError: </span><span class="op">{</span><span class="nam">last_mt5_error</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t482" href="#t482">482</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed Send Request Details: </span><span class="op">{</span><span class="nam">request</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t483" href="#t483">483</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed Send Result Object: </span><span class="op">{</span><span class="nam">result</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t484" href="#t484">484</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t485" href="#t485">485</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t486" href="#t486">486</a></span><span class="t">    <span class="com"># Success</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t487" href="#t487">487</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Order SENT OK </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: Order=</span><span class="op">{</span><span class="nam">result</span><span class="op">.</span><span class="nam">order</span><span class="op">}</span><span class="fst">, Deal=</span><span class="op">{</span><span class="nam">result</span><span class="op">.</span><span class="nam">deal</span><span class="op">}</span><span class="fst">, Price=</span><span class="op">{</span><span class="nam">result</span><span class="op">.</span><span class="nam">price</span><span class="op">:</span><span class="fst">.</span><span class="op">{</span><span class="nam">digits</span><span class="op">}</span><span class="fst">f</span><span class="op">}</span><span class="fst">, Vol=</span><span class="op">{</span><span class="nam">result</span><span class="op">.</span><span class="nam">volume</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t488" href="#t488">488</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t489" href="#t489">489</a></span><span class="t">    <span class="com"># Publish to event bus if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t490" href="#t490">490</a></span><span class="t">    <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="key">and</span> <span class="nam">_order_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t491" href="#t491">491</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t492" href="#t492">492</a></span><span class="t">            <span class="com"># Convert order type to string</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t493" href="#t493">493</a></span><span class="t">            <span class="nam">order_type_str</span> <span class="op">=</span> <span class="str">"buy"</span> <span class="key">if</span> <span class="nam">order_type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span> <span class="key">else</span> <span class="str">"sell"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t494" href="#t494">494</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t495" href="#t495">495</a></span><span class="t">            <span class="com"># Publish order event</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t496" href="#t496">496</a></span><span class="t">            <span class="nam">_order_producer</span><span class="op">.</span><span class="nam">publish_order</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t497" href="#t497">497</a></span><span class="t">                <span class="nam">order_id</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">order</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t498" href="#t498">498</a></span><span class="t">                <span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t499" href="#t499">499</a></span><span class="t">                <span class="nam">order_type</span><span class="op">=</span><span class="nam">order_type_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t500" href="#t500">500</a></span><span class="t">                <span class="nam">volume</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">volume</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t501" href="#t501">501</a></span><span class="t">                <span class="nam">price</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">price</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t502" href="#t502">502</a></span><span class="t">                <span class="nam">status</span><span class="op">=</span><span class="str">"filled"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t503" href="#t503">503</a></span><span class="t">                <span class="nam">sl</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">normalized_sl</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t504" href="#t504">504</a></span><span class="t">                <span class="nam">tp</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">normalized_tp</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t505" href="#t505">505</a></span><span class="t">                <span class="nam">comment</span><span class="op">=</span><span class="nam">request</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"comment"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t506" href="#t506">506</a></span><span class="t">                <span class="nam">magic</span><span class="op">=</span><span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t507" href="#t507">507</a></span><span class="t">                <span class="nam">execution_time</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t508" href="#t508">508</a></span><span class="t">                <span class="nam">additional_info</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t509" href="#t509">509</a></span><span class="t">                    <span class="str">"deal_id"</span><span class="op">:</span> <span class="nam">str</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">deal</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t510" href="#t510">510</a></span><span class="t">                    <span class="str">"atr_value"</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">atr_value</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t511" href="#t511">511</a></span><span class="t">                    <span class="str">"garch_info"</span><span class="op">:</span> <span class="nam">garch_info</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t512" href="#t512">512</a></span><span class="t">                    <span class="str">"hmm_info"</span><span class="op">:</span> <span class="nam">hmm_info</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t513" href="#t513">513</a></span><span class="t">                <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t514" href="#t514">514</a></span><span class="t">                <span class="nam">source</span><span class="op">=</span><span class="str">"mt5_client"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t515" href="#t515">515</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t516" href="#t516">516</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t517" href="#t517">517</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Published order event for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> to event bus</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t518" href="#t518">518</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t519" href="#t519">519</a></span><span class="t">            <span class="com"># Publish trade event</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t520" href="#t520">520</a></span><span class="t">            <span class="key">if</span> <span class="nam">_trade_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t521" href="#t521">521</a></span><span class="t">                <span class="nam">_trade_producer</span><span class="op">.</span><span class="nam">publish_trade</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t522" href="#t522">522</a></span><span class="t">                    <span class="nam">trade_id</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">deal</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t523" href="#t523">523</a></span><span class="t">                    <span class="nam">order_id</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">order</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t524" href="#t524">524</a></span><span class="t">                    <span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t525" href="#t525">525</a></span><span class="t">                    <span class="nam">trade_type</span><span class="op">=</span><span class="nam">order_type_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t526" href="#t526">526</a></span><span class="t">                    <span class="nam">volume</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">volume</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t527" href="#t527">527</a></span><span class="t">                    <span class="nam">price</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">price</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t528" href="#t528">528</a></span><span class="t">                    <span class="nam">status</span><span class="op">=</span><span class="str">"open"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t529" href="#t529">529</a></span><span class="t">                    <span class="nam">open_time</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t530" href="#t530">530</a></span><span class="t">                    <span class="nam">profit</span><span class="op">=</span><span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t531" href="#t531">531</a></span><span class="t">                    <span class="nam">close_time</span><span class="op">=</span><span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t532" href="#t532">532</a></span><span class="t">                    <span class="nam">sl</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">normalized_sl</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t533" href="#t533">533</a></span><span class="t">                    <span class="nam">tp</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">normalized_tp</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t534" href="#t534">534</a></span><span class="t">                    <span class="nam">comment</span><span class="op">=</span><span class="nam">request</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">"comment"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t535" href="#t535">535</a></span><span class="t">                    <span class="nam">magic</span><span class="op">=</span><span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t536" href="#t536">536</a></span><span class="t">                    <span class="nam">additional_info</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t537" href="#t537">537</a></span><span class="t">                        <span class="str">"atr_value"</span><span class="op">:</span> <span class="nam">float</span><span class="op">(</span><span class="nam">atr_value</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t538" href="#t538">538</a></span><span class="t">                        <span class="str">"garch_info"</span><span class="op">:</span> <span class="nam">garch_info</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t539" href="#t539">539</a></span><span class="t">                        <span class="str">"hmm_info"</span><span class="op">:</span> <span class="nam">hmm_info</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t540" href="#t540">540</a></span><span class="t">                    <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t541" href="#t541">541</a></span><span class="t">                    <span class="nam">source</span><span class="op">=</span><span class="str">"mt5_client"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t542" href="#t542">542</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t543" href="#t543">543</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t544" href="#t544">544</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Published trade event for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> to event bus</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t545" href="#t545">545</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t546" href="#t546">546</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error publishing order/trade events to event bus: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t547" href="#t547">547</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t548" href="#t548">548</a></span><span class="t">    <span class="key">return</span> <span class="nam">int</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">order</span><span class="op">)</span> <span class="com"># Return the order ticket</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t549" href="#t549">549</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t550" href="#t550">550</a></span><span class="t"><span class="key">def</span> <span class="nam">close_existing_positions</span><span class="op">(</span><span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">int</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t551" href="#t551">551</a></span><span class="t">    <span class="str">"""Closes all open positions for the given symbol with the bot's magic number.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t552" href="#t552">552</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t553" href="#t553">553</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t554" href="#t554">554</a></span><span class="t"><span class="str">        symbol: The symbol to close positions for</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t555" href="#t555">555</a></span><span class="t"><span class="str">        adapter: Logger adapter for logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t556" href="#t556">556</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t557" href="#t557">557</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t558" href="#t558">558</a></span><span class="t"><span class="str">        Optional[int]: Number of positions closed, or None if an error occurred</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t559" href="#t559">559</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t560" href="#t560">560</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Closing positions check </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="nam">positions</span><span class="op">=</span><span class="key">None</span><span class="op">;</span> <span class="nam">closed</span><span class="op">=</span><span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t561" href="#t561">561</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t562" href="#t562">562</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">symbol_select</span><span class="op">(</span><span class="nam">symbol</span><span class="op">,</span><span class="key">True</span><span class="op">)</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Select fail </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> pre-pos check.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t563" href="#t563">563</a></span><span class="t">        <span class="key">else</span><span class="op">:</span> <span class="nam">time_sleep</span><span class="op">.</span><span class="nam">sleep</span><span class="op">(</span><span class="num">0.1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t564" href="#t564">564</a></span><span class="t">        <span class="nam">positions</span><span class="op">=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">positions_get</span><span class="op">(</span><span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t565" href="#t565">565</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">positions_get fail </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t566" href="#t566">566</a></span><span class="t">    <span class="key">if</span> <span class="nam">positions</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">positions</span><span class="op">:</span> <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t567" href="#t567">567</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t568" href="#t568">568</a></span><span class="t">    <span class="key">for</span> <span class="nam">p</span> <span class="key">in</span> <span class="nam">positions</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t569" href="#t569">569</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">all</span><span class="op">(</span><span class="nam">hasattr</span><span class="op">(</span><span class="nam">p</span><span class="op">,</span><span class="nam">a</span><span class="op">)</span> <span class="key">for</span> <span class="nam">a</span> <span class="key">in</span> <span class="op">[</span><span class="str">'magic'</span><span class="op">,</span><span class="str">'ticket'</span><span class="op">,</span><span class="str">'type'</span><span class="op">,</span><span class="str">'volume'</span><span class="op">]</span><span class="op">)</span><span class="op">:</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t570" href="#t570">570</a></span><span class="t">        <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">magic</span><span class="op">==</span><span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t571" href="#t571">571</a></span><span class="t">            <span class="nam">p_type</span><span class="op">=</span><span class="str">'BUY'</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">type</span><span class="op">==</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">POSITION_TYPE_BUY</span> <span class="key">else</span> <span class="str">'SELL'</span><span class="op">;</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Found open </span><span class="op">{</span><span class="nam">p_type</span><span class="op">}</span><span class="fst"> pos </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">. Closing...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t572" href="#t572">572</a></span><span class="t">            <span class="nam">close_type</span><span class="op">=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_SELL</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">type</span><span class="op">==</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">POSITION_TYPE_BUY</span> <span class="key">else</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TYPE_BUY</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t573" href="#t573">573</a></span><span class="t">            <span class="nam">tick</span><span class="op">=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">symbol_info_tick</span><span class="op">(</span><span class="nam">symbol</span><span class="op">)</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t574" href="#t574">574</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">tick</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">No tick </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> to close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t575" href="#t575">575</a></span><span class="t">            <span class="nam">close_price</span><span class="op">=</span><span class="nam">tick</span><span class="op">.</span><span class="nam">bid</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">type</span><span class="op">==</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">POSITION_TYPE_BUY</span> <span class="key">else</span> <span class="nam">tick</span><span class="op">.</span><span class="nam">ask</span><span class="op">;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t576" href="#t576">576</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t577" href="#t577">577</a></span><span class="t">            <span class="key">if</span> <span class="nam">close_price</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t578" href="#t578">578</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid close price </span><span class="op">{</span><span class="nam">close_price</span><span class="op">}</span><span class="fst"> for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t579" href="#t579">579</a></span><span class="t">                <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t580" href="#t580">580</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t581" href="#t581">581</a></span><span class="t">            <span class="nam">req</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t582" href="#t582">582</a></span><span class="t">             <span class="str">"action"</span><span class="op">:</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">TRADE_ACTION_DEAL</span><span class="op">,</span> <span class="str">"symbol"</span><span class="op">:</span><span class="nam">symbol</span><span class="op">,</span> <span class="str">"volume"</span><span class="op">:</span><span class="nam">p</span><span class="op">.</span><span class="nam">volume</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t583" href="#t583">583</a></span><span class="t">             <span class="str">"type"</span><span class="op">:</span><span class="nam">close_type</span><span class="op">,</span> <span class="str">"position"</span><span class="op">:</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">,</span> <span class="str">"price"</span><span class="op">:</span><span class="nam">close_price</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t584" href="#t584">584</a></span><span class="t">             <span class="str">"deviation"</span><span class="op">:</span><span class="num">20</span><span class="op">,</span> <span class="str">"magic"</span><span class="op">:</span><span class="nam">config</span><span class="op">.</span><span class="nam">magic_number</span><span class="op">,</span> <span class="str">"comment"</span><span class="op">:</span><span class="str">"PGB Close Position v1.1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t585" href="#t585">585</a></span><span class="t">             <span class="str">"sl"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span> <span class="str">"tp"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span> <span class="com"># SL/TP should be 0.0 when closing</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t586" href="#t586">586</a></span><span class="t">             <span class="str">"type_time"</span><span class="op">:</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_TIME_GTC</span><span class="op">,</span> <span class="str">"type_filling"</span><span class="op">:</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">ORDER_FILLING_IOC</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t587" href="#t587">587</a></span><span class="t">             <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t588" href="#t588">588</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t589" href="#t589">589</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">check</span><span class="op">=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">order_check</span><span class="op">(</span><span class="nam">request</span><span class="op">=</span><span class="nam">req</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t590" href="#t590">590</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Exception during order_check for close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t591" href="#t591">591</a></span><span class="t">            <span class="key">if</span> <span class="nam">check</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">check</span><span class="op">.</span><span class="nam">retcode</span><span class="op">!=</span><span class="num">0</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Check FAIL close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">check</span><span class="op">.</span><span class="nam">comment</span> <span class="key">if</span> <span class="nam">check</span> <span class="key">else</span> <span class="str">'None'</span><span class="op">}</span><span class="fst"> (Code: </span><span class="op">{</span><span class="nam">check</span><span class="op">.</span><span class="nam">retcode</span> <span class="key">if</span> <span class="nam">check</span> <span class="key">else</span> <span class="str">'N/A'</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t592" href="#t592">592</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Check OK close pos </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t593" href="#t593">593</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t594" href="#t594">594</a></span><span class="t">            <span class="key">if</span> <span class="nam">config</span><span class="op">.</span><span class="nam">dry_run</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">DRY RUN: Would close position </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst"> for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="nam">closed</span><span class="op">+=</span><span class="num">1</span><span class="op">;</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t595" href="#t595">595</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t596" href="#t596">596</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">res</span><span class="op">=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">order_send</span><span class="op">(</span><span class="nam">request</span><span class="op">=</span><span class="nam">req</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t597" href="#t597">597</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">order_send exc close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span><span class="op">;</span> <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t598" href="#t598">598</a></span><span class="t">            <span class="key">if</span> <span class="nam">res</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">res</span><span class="op">.</span><span class="nam">retcode</span><span class="op">!=</span><span class="nam">mt5</span><span class="op">.</span><span class="nam">TRADE_RETCODE_DONE</span><span class="op">:</span> <span class="nam">err</span><span class="op">=</span><span class="nam">res</span><span class="op">.</span><span class="nam">comment</span> <span class="key">if</span> <span class="nam">res</span> <span class="key">else</span> <span class="str">'None'</span><span class="op">;</span> <span class="nam">code</span><span class="op">=</span><span class="nam">res</span><span class="op">.</span><span class="nam">retcode</span> <span class="key">if</span> <span class="nam">res</span> <span class="key">else</span> <span class="str">'N/A'</span><span class="op">;</span> <span class="nam">desc</span><span class="op">=</span><span class="nam">res</span><span class="op">.</span><span class="nam">comment</span> <span class="key">if</span> <span class="nam">res</span> <span class="key">else</span> <span class="str">'N/A'</span><span class="op">;</span> <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Send FAIL close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">err</span><span class="op">}</span><span class="fst"> (Code:</span><span class="op">{</span><span class="nam">code</span><span class="op">}</span><span class="fst">) </span><span class="op">{</span><span class="nam">desc</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t599" href="#t599">599</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t600" href="#t600">600</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">OK sent close </span><span class="op">{</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">}</span><span class="fst">. Deal: </span><span class="op">{</span><span class="nam">res</span><span class="op">.</span><span class="nam">deal</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t601" href="#t601">601</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t602" href="#t602">602</a></span><span class="t">                <span class="com"># Publish to event bus if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t603" href="#t603">603</a></span><span class="t">                <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="key">and</span> <span class="nam">_trade_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t604" href="#t604">604</a></span><span class="t">                    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t605" href="#t605">605</a></span><span class="t">                        <span class="com"># Convert position type to string</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t606" href="#t606">606</a></span><span class="t">                        <span class="nam">position_type_str</span> <span class="op">=</span> <span class="str">"buy"</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">type</span> <span class="op">==</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">POSITION_TYPE_BUY</span> <span class="key">else</span> <span class="str">"sell"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t607" href="#t607">607</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t608" href="#t608">608</a></span><span class="t">                        <span class="com"># Publish trade event</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t609" href="#t609">609</a></span><span class="t">                        <span class="nam">_trade_producer</span><span class="op">.</span><span class="nam">publish_trade</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t610" href="#t610">610</a></span><span class="t">                            <span class="nam">trade_id</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">res</span><span class="op">.</span><span class="nam">deal</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t611" href="#t611">611</a></span><span class="t">                            <span class="nam">order_id</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">ticket</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t612" href="#t612">612</a></span><span class="t">                            <span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t613" href="#t613">613</a></span><span class="t">                            <span class="nam">trade_type</span><span class="op">=</span><span class="nam">position_type_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t614" href="#t614">614</a></span><span class="t">                            <span class="nam">volume</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">volume</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t615" href="#t615">615</a></span><span class="t">                            <span class="nam">price</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">res</span><span class="op">.</span><span class="nam">price</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t616" href="#t616">616</a></span><span class="t">                            <span class="nam">status</span><span class="op">=</span><span class="str">"closed"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t617" href="#t617">617</a></span><span class="t">                            <span class="nam">open_time</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">fromtimestamp</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">time</span><span class="op">,</span> <span class="nam">tz</span><span class="op">=</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t618" href="#t618">618</a></span><span class="t">                            <span class="nam">profit</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">profit</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t619" href="#t619">619</a></span><span class="t">                            <span class="nam">close_time</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t620" href="#t620">620</a></span><span class="t">                            <span class="nam">sl</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">sl</span><span class="op">)</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">sl</span> <span class="key">else</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t621" href="#t621">621</a></span><span class="t">                            <span class="nam">tp</span><span class="op">=</span><span class="nam">float</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">tp</span><span class="op">)</span> <span class="key">if</span> <span class="nam">p</span><span class="op">.</span><span class="nam">tp</span> <span class="key">else</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t622" href="#t622">622</a></span><span class="t">                            <span class="nam">comment</span><span class="op">=</span><span class="nam">p</span><span class="op">.</span><span class="nam">comment</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t623" href="#t623">623</a></span><span class="t">                            <span class="nam">magic</span><span class="op">=</span><span class="nam">p</span><span class="op">.</span><span class="nam">magic</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t624" href="#t624">624</a></span><span class="t">                            <span class="nam">additional_info</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t625" href="#t625">625</a></span><span class="t">                                <span class="str">"close_deal_id"</span><span class="op">:</span> <span class="nam">str</span><span class="op">(</span><span class="nam">res</span><span class="op">.</span><span class="nam">deal</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t626" href="#t626">626</a></span><span class="t">                                <span class="str">"close_order_id"</span><span class="op">:</span> <span class="nam">str</span><span class="op">(</span><span class="nam">res</span><span class="op">.</span><span class="nam">order</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t627" href="#t627">627</a></span><span class="t">                            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t628" href="#t628">628</a></span><span class="t">                            <span class="nam">source</span><span class="op">=</span><span class="str">"mt5_client"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t629" href="#t629">629</a></span><span class="t">                        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t630" href="#t630">630</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t631" href="#t631">631</a></span><span class="t">                        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Published closed trade event for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> to event bus</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t632" href="#t632">632</a></span><span class="t">                    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t633" href="#t633">633</a></span><span class="t">                        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error publishing closed trade event to event bus: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t634" href="#t634">634</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t635" href="#t635">635</a></span><span class="t">                <span class="com"># Optionally add deal ticket to processed set here if needed immediately</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t636" href="#t636">636</a></span><span class="t">                <span class="com"># from .bot_orchestrator import processed_deal_tickets # Avoid circular import if possible</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t637" href="#t637">637</a></span><span class="t">                <span class="com"># processed_deal_tickets.add(int(res.deal))</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t638" href="#t638">638</a></span><span class="t">                <span class="nam">closed</span><span class="op">+=</span><span class="num">1</span><span class="op">;</span> <span class="nam">time_sleep</span><span class="op">.</span><span class="nam">sleep</span><span class="op">(</span><span class="num">0.5</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t639" href="#t639">639</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t640" href="#t640">640</a></span><span class="t">    <span class="key">return</span> <span class="nam">closed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t641" href="#t641">641</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t642" href="#t642">642</a></span><span class="t"><span class="key">def</span> <span class="nam">calculate_sleep_time</span><span class="op">(</span><span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">timeframe_enum</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t643" href="#t643">643</a></span><span class="t">    <span class="str">"""Calculates sleep time until the next bar.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t644" href="#t644">644</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t645" href="#t645">645</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t646" href="#t646">646</a></span><span class="t"><span class="str">        symbol: The symbol to calculate sleep time for</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t647" href="#t647">647</a></span><span class="t"><span class="str">        timeframe_enum: The timeframe enum value</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t648" href="#t648">648</a></span><span class="t"><span class="str">        adapter: Logger adapter for logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t649" href="#t649">649</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t650" href="#t650">650</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t651" href="#t651">651</a></span><span class="t"><span class="str">        int: Sleep time in seconds</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t652" href="#t652">652</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t653" href="#t653">653</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Calculating sleep time..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t654" href="#t654">654</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t655" href="#t655">655</a></span><span class="t">        <span class="nam">tf_map</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t656" href="#t656">656</a></span><span class="t">            <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M1</span><span class="op">:</span> <span class="num">60</span><span class="op">,</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M5</span><span class="op">:</span> <span class="num">300</span><span class="op">,</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M15</span><span class="op">:</span> <span class="num">900</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t657" href="#t657">657</a></span><span class="t">            <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_M30</span><span class="op">:</span> <span class="num">1800</span><span class="op">,</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_H1</span><span class="op">:</span> <span class="num">3600</span><span class="op">,</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_H4</span><span class="op">:</span> <span class="num">14400</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t658" href="#t658">658</a></span><span class="t">            <span class="nam">mt5</span><span class="op">.</span><span class="nam">TIMEFRAME_D1</span><span class="op">:</span> <span class="num">86400</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t659" href="#t659">659</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t660" href="#t660">660</a></span><span class="t">        <span class="nam">interval</span> <span class="op">=</span> <span class="nam">tf_map</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">timeframe_enum</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t661" href="#t661">661</a></span><span class="t">        <span class="key">if</span> <span class="nam">interval</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t662" href="#t662">662</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Unsupported TF </span><span class="op">{</span><span class="nam">timeframe_enum</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t663" href="#t663">663</a></span><span class="t">            <span class="key">return</span> <span class="num">60</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t664" href="#t664">664</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t665" href="#t665">665</a></span><span class="t">        <span class="nam">tick</span> <span class="op">=</span> <span class="nam">mt5</span><span class="op">.</span><span class="nam">symbol_info_tick</span><span class="op">(</span><span class="nam">symbol</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t666" href="#t666">666</a></span><span class="t">        <span class="key">if</span> <span class="nam">tick</span> <span class="key">and</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">tick</span><span class="op">,</span> <span class="str">'time'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">tick</span><span class="op">.</span><span class="nam">time</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t667" href="#t667">667</a></span><span class="t">            <span class="nam">now_ts</span> <span class="op">=</span> <span class="nam">tick</span><span class="op">.</span><span class="nam">time</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t668" href="#t668">668</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Using tick time </span><span class="op">{</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">fromtimestamp</span><span class="op">(</span><span class="nam">now_ts</span><span class="op">,</span> <span class="nam">tz</span><span class="op">=</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t669" href="#t669">669</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t670" href="#t670">670</a></span><span class="t">            <span class="nam">now_ts</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">.</span><span class="nam">timestamp</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t671" href="#t671">671</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">No tick for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">. Using local UTC </span><span class="op">{</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">fromtimestamp</span><span class="op">(</span><span class="nam">now_ts</span><span class="op">,</span> <span class="nam">tz</span><span class="op">=</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t672" href="#t672">672</a></span><span class="t">            <span class="key">if</span> <span class="nam">tick</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span> <span class="com"># Specific check for test case compatibility</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t673" href="#t673">673</a></span><span class="t">                 <span class="key">return</span> <span class="num">60</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t674" href="#t674">674</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t675" href="#t675">675</a></span><span class="t">        <span class="nam">next_start</span> <span class="op">=</span> <span class="op">(</span><span class="op">(</span><span class="nam">now_ts</span> <span class="op">//</span> <span class="nam">interval</span><span class="op">)</span> <span class="op">*</span> <span class="nam">interval</span><span class="op">)</span> <span class="op">+</span> <span class="nam">interval</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t676" href="#t676">676</a></span><span class="t">        <span class="nam">sleep</span> <span class="op">=</span> <span class="nam">next_start</span> <span class="op">-</span> <span class="nam">now_ts</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t677" href="#t677">677</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t678" href="#t678">678</a></span><span class="t">        <span class="com"># Buffer adjustment (moved from original run_bot loop)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t679" href="#t679">679</a></span><span class="t">        <span class="nam">buffer</span> <span class="op">=</span> <span class="num">5</span> <span class="com"># seconds buffer</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t680" href="#t680">680</a></span><span class="t">        <span class="nam">sleep_adjusted</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="nam">sleep</span> <span class="op">-</span> <span class="nam">buffer</span><span class="op">)</span> <span class="com"># Ensure at least 1s sleep</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t681" href="#t681">681</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t682" href="#t682">682</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t683" href="#t683">683</a></span><span class="t">            <span class="fst">f"</span><span class="fst">Raw sleep: </span><span class="op">{</span><span class="nam">sleep</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">s. Adjusted sleep: </span><span class="op">{</span><span class="nam">sleep_adjusted</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">s until ~</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t684" href="#t684">684</a></span><span class="t">            <span class="fst">f"</span><span class="op">{</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">fromtimestamp</span><span class="op">(</span><span class="nam">next_start</span><span class="op">,</span> <span class="nam">tz</span><span class="op">=</span><span class="nam">timezone</span><span class="op">.</span><span class="nam">utc</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t685" href="#t685">685</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t686" href="#t686">686</a></span><span class="t">        <span class="key">return</span> <span class="nam">int</span><span class="op">(</span><span class="nam">sleep_adjusted</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t687" href="#t687">687</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t688" href="#t688">688</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t689" href="#t689">689</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Sleep calc error </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t690" href="#t690">690</a></span><span class="t">        <span class="key">return</span> <span class="num">60</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t691" href="#t691">691</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t692" href="#t692">692</a></span><span class="t"><span class="key">def</span> <span class="nam">shutdown_mt5</span><span class="op">(</span><span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t693" href="#t693">693</a></span><span class="t">    <span class="str">"""Shuts down the MetaTrader 5 connection and event producers."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t694" href="#t694">694</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Shutting down MT5 connection..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t695" href="#t695">695</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t696" href="#t696">696</a></span><span class="t">    <span class="com"># Stop event producers if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t697" href="#t697">697</a></span><span class="t">    <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span> <span class="key">and</span> <span class="nam">_mt5_event_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t698" href="#t698">698</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t699" href="#t699">699</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Stopping MT5 event producer..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t700" href="#t700">700</a></span><span class="t">            <span class="nam">_mt5_event_producer</span><span class="op">.</span><span class="nam">stop</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t701" href="#t701">701</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"MT5 event producer stopped successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t702" href="#t702">702</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t703" href="#t703">703</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error stopping MT5 event producer: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t704" href="#t704">704</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t705" href="#t705">705</a></span><span class="t">    <span class="com"># Flush event producers if available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t706" href="#t706">706</a></span><span class="t">    <span class="key">if</span> <span class="nam">EVENT_BUS_AVAILABLE</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t707" href="#t707">707</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t708" href="#t708">708</a></span><span class="t">            <span class="key">if</span> <span class="nam">_market_data_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t709" href="#t709">709</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Flushing market data producer..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t710" href="#t710">710</a></span><span class="t">                <span class="nam">_market_data_producer</span><span class="op">.</span><span class="nam">flush</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t711" href="#t711">711</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t712" href="#t712">712</a></span><span class="t">            <span class="key">if</span> <span class="nam">_order_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t713" href="#t713">713</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Flushing order producer..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t714" href="#t714">714</a></span><span class="t">                <span class="nam">_order_producer</span><span class="op">.</span><span class="nam">flush</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t715" href="#t715">715</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t716" href="#t716">716</a></span><span class="t">            <span class="key">if</span> <span class="nam">_trade_producer</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t717" href="#t717">717</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="str">"Flushing trade producer..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t718" href="#t718">718</a></span><span class="t">                <span class="nam">_trade_producer</span><span class="op">.</span><span class="nam">flush</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t719" href="#t719">719</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t720" href="#t720">720</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error flushing event producers: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t721" href="#t721">721</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t722" href="#t722">722</a></span><span class="t">    <span class="com"># Shutdown MT5</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t723" href="#t723">723</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t724" href="#t724">724</a></span><span class="t">        <span class="nam">mt5</span><span class="op">.</span><span class="nam">shutdown</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t725" href="#t725">725</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"MT5 shutdown successful."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t726" href="#t726">726</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t727" href="#t727">727</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error during MT5 shutdown: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_6696b9cca54af58b_model_monitoring_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_21758df60ac2cdd3_mt5_constants_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 23:00 -0500
        </p>
    </div>
</footer>
</body>
</html>
