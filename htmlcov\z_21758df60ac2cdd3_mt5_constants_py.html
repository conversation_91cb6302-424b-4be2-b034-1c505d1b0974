<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\mt5_constants.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\mt5_constants.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">103 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">103<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_21758df60ac2cdd3_mt5_client_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_21758df60ac2cdd3_mt5_event_producer_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 21:24 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># --- MT5 Constants Module ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">This module contains constants from MetaTrader5 to avoid direct imports in other modules.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="com"># --- Deal Entry Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="nam">DEAL_ENTRY_IN</span> <span class="op">=</span> <span class="num">0</span>      <span class="com"># Entry in</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="nam">DEAL_ENTRY_OUT</span> <span class="op">=</span> <span class="num">1</span>     <span class="com"># Entry out</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="nam">DEAL_ENTRY_INOUT</span> <span class="op">=</span> <span class="num">2</span>   <span class="com"># Reverse</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="nam">DEAL_ENTRY_OUT_BY</span> <span class="op">=</span> <span class="num">3</span>  <span class="com"># Close by</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="com"># --- Deal Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="nam">DEAL_TYPE_BUY</span> <span class="op">=</span> <span class="num">0</span>      <span class="com"># Buy</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="nam">DEAL_TYPE_SELL</span> <span class="op">=</span> <span class="num">1</span>     <span class="com"># Sell</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">DEAL_TYPE_BALANCE</span> <span class="op">=</span> <span class="num">2</span>  <span class="com"># Balance</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">DEAL_TYPE_CREDIT</span> <span class="op">=</span> <span class="num">3</span>   <span class="com"># Credit</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="nam">DEAL_TYPE_CHARGE</span> <span class="op">=</span> <span class="num">4</span>   <span class="com"># Charge</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="nam">DEAL_TYPE_CORRECTION</span> <span class="op">=</span> <span class="num">5</span>  <span class="com"># Correction</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">DEAL_TYPE_BONUS</span> <span class="op">=</span> <span class="num">6</span>    <span class="com"># Bonus</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">DEAL_TYPE_COMMISSION</span> <span class="op">=</span> <span class="num">7</span>  <span class="com"># Commission</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">DEAL_TYPE_COMMISSION_DAILY</span> <span class="op">=</span> <span class="num">8</span>  <span class="com"># Daily commission</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="nam">DEAL_TYPE_COMMISSION_MONTHLY</span> <span class="op">=</span> <span class="num">9</span>  <span class="com"># Monthly commission</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="nam">DEAL_TYPE_COMMISSION_AGENT_DAILY</span> <span class="op">=</span> <span class="num">10</span>  <span class="com"># Daily agent commission</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="nam">DEAL_TYPE_COMMISSION_AGENT_MONTHLY</span> <span class="op">=</span> <span class="num">11</span>  <span class="com"># Monthly agent commission</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="nam">DEAL_TYPE_INTEREST</span> <span class="op">=</span> <span class="num">12</span>  <span class="com"># Interest</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="nam">DEAL_TYPE_BUY_CANCELED</span> <span class="op">=</span> <span class="num">13</span>  <span class="com"># Canceled buy deal</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="nam">DEAL_TYPE_SELL_CANCELED</span> <span class="op">=</span> <span class="num">14</span>  <span class="com"># Canceled sell deal</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="nam">DEAL_TYPE_DIVIDEND</span> <span class="op">=</span> <span class="num">15</span>  <span class="com"># Dividend</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="nam">DEAL_TYPE_DIVIDEND_FRANKED</span> <span class="op">=</span> <span class="num">16</span>  <span class="com"># Franked dividend</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="nam">DEAL_TYPE_TAX</span> <span class="op">=</span> <span class="num">17</span>     <span class="com"># Tax</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="com"># --- Order Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="nam">ORDER_TYPE_BUY</span> <span class="op">=</span> <span class="num">0</span>     <span class="com"># Market Buy order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">ORDER_TYPE_SELL</span> <span class="op">=</span> <span class="num">1</span>    <span class="com"># Market Sell order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="nam">ORDER_TYPE_BUY_LIMIT</span> <span class="op">=</span> <span class="num">2</span>  <span class="com"># Buy Limit pending order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="nam">ORDER_TYPE_SELL_LIMIT</span> <span class="op">=</span> <span class="num">3</span>  <span class="com"># Sell Limit pending order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="nam">ORDER_TYPE_BUY_STOP</span> <span class="op">=</span> <span class="num">4</span>  <span class="com"># Buy Stop pending order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="nam">ORDER_TYPE_SELL_STOP</span> <span class="op">=</span> <span class="num">5</span>  <span class="com"># Sell Stop pending order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="nam">ORDER_TYPE_BUY_STOP_LIMIT</span> <span class="op">=</span> <span class="num">6</span>  <span class="com"># Upon reaching the price, a pending Buy Limit order is placed at the StopLimit price</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="nam">ORDER_TYPE_SELL_STOP_LIMIT</span> <span class="op">=</span> <span class="num">7</span>  <span class="com"># Upon reaching the price, a pending Sell Limit order is placed at the StopLimit price</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="nam">ORDER_TYPE_CLOSE_BY</span> <span class="op">=</span> <span class="num">8</span>  <span class="com"># Order to close a position by an opposite one</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="com"># --- Order State ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t"><span class="nam">ORDER_STATE_STARTED</span> <span class="op">=</span> <span class="num">0</span>  <span class="com"># Order checked, but not yet accepted by broker</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t"><span class="nam">ORDER_STATE_PLACED</span> <span class="op">=</span> <span class="num">1</span>  <span class="com"># Order accepted</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="nam">ORDER_STATE_CANCELED</span> <span class="op">=</span> <span class="num">2</span>  <span class="com"># Order canceled by client</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t"><span class="nam">ORDER_STATE_PARTIAL</span> <span class="op">=</span> <span class="num">3</span>  <span class="com"># Order partially executed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t"><span class="nam">ORDER_STATE_FILLED</span> <span class="op">=</span> <span class="num">4</span>  <span class="com"># Order fully executed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t"><span class="nam">ORDER_STATE_REJECTED</span> <span class="op">=</span> <span class="num">5</span>  <span class="com"># Order rejected</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t"><span class="nam">ORDER_STATE_EXPIRED</span> <span class="op">=</span> <span class="num">6</span>  <span class="com"># Order expired</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="nam">ORDER_STATE_REQUEST_ADD</span> <span class="op">=</span> <span class="num">7</span>  <span class="com"># Order is being registered (placing)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t"><span class="nam">ORDER_STATE_REQUEST_MODIFY</span> <span class="op">=</span> <span class="num">8</span>  <span class="com"># Order is being modified (modifying)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t"><span class="nam">ORDER_STATE_REQUEST_CANCEL</span> <span class="op">=</span> <span class="num">9</span>  <span class="com"># Order is being deleted (deleting)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t"><span class="com"># --- Order Filling Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t"><span class="nam">ORDER_FILLING_FOK</span> <span class="op">=</span> <span class="num">0</span>  <span class="com"># Fill or Kill</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="nam">ORDER_FILLING_IOC</span> <span class="op">=</span> <span class="num">1</span>  <span class="com"># Immediate or Cancel</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t"><span class="nam">ORDER_FILLING_RETURN</span> <span class="op">=</span> <span class="num">2</span>  <span class="com"># Return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t"><span class="com"># --- Order Time Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t"><span class="nam">ORDER_TIME_GTC</span> <span class="op">=</span> <span class="num">0</span>     <span class="com"># Good till canceled</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t"><span class="nam">ORDER_TIME_DAY</span> <span class="op">=</span> <span class="num">1</span>     <span class="com"># Good till day</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t"><span class="nam">ORDER_TIME_SPECIFIED</span> <span class="op">=</span> <span class="num">2</span>  <span class="com"># Good till specified</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t"><span class="nam">ORDER_TIME_SPECIFIED_DAY</span> <span class="op">=</span> <span class="num">3</span>  <span class="com"># Good till specified day</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t"><span class="com"># --- Position Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t"><span class="nam">POSITION_TYPE_BUY</span> <span class="op">=</span> <span class="num">0</span>  <span class="com"># Buy</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t"><span class="nam">POSITION_TYPE_SELL</span> <span class="op">=</span> <span class="num">1</span>  <span class="com"># Sell</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t"><span class="com"># --- Trade Action Types ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="nam">TRADE_ACTION_DEAL</span> <span class="op">=</span> <span class="num">1</span>  <span class="com"># Place a trade order for an immediate execution</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="nam">TRADE_ACTION_PENDING</span> <span class="op">=</span> <span class="num">5</span>  <span class="com"># Place a trade order for the pending execution</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="nam">TRADE_ACTION_SLTP</span> <span class="op">=</span> <span class="num">6</span>  <span class="com"># Modify Stop Loss and Take Profit for an opened position</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="nam">TRADE_ACTION_MODIFY</span> <span class="op">=</span> <span class="num">7</span>  <span class="com"># Modify the parameters of the previously placed trade order</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t"><span class="nam">TRADE_ACTION_REMOVE</span> <span class="op">=</span> <span class="num">8</span>  <span class="com"># Delete the pending order previously placed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="nam">TRADE_ACTION_CLOSE_BY</span> <span class="op">=</span> <span class="num">10</span>  <span class="com"># Close a position by an opposite one</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="com"># --- Trade Return Codes ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t"><span class="nam">TRADE_RETCODE_DONE</span> <span class="op">=</span> <span class="num">10018</span>  <span class="com"># Request completed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t"><span class="nam">TRADE_RETCODE_DONE_PARTIAL</span> <span class="op">=</span> <span class="num">10019</span>  <span class="com"># Request completed partially</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t"><span class="nam">TRADE_RETCODE_ERROR</span> <span class="op">=</span> <span class="num">10004</span>  <span class="com"># Request processing error</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t"><span class="nam">TRADE_RETCODE_TIMEOUT</span> <span class="op">=</span> <span class="num">10008</span>  <span class="com"># Request canceled by timeout</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t"><span class="nam">TRADE_RETCODE_REJECT</span> <span class="op">=</span> <span class="num">10009</span>  <span class="com"># Request rejected</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID</span> <span class="op">=</span> <span class="num">10013</span>  <span class="com"># Invalid request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID_VOLUME</span> <span class="op">=</span> <span class="num">10014</span>  <span class="com"># Invalid volume in the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID_PRICE</span> <span class="op">=</span> <span class="num">10015</span>  <span class="com"># Invalid price in the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID_STOPS</span> <span class="op">=</span> <span class="num">10016</span>  <span class="com"># Invalid stops in the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t"><span class="nam">TRADE_RETCODE_MARKET_CLOSED</span> <span class="op">=</span> <span class="num">10018</span>  <span class="com"># Market is closed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t"><span class="nam">TRADE_RETCODE_NO_MONEY</span> <span class="op">=</span> <span class="num">10019</span>  <span class="com"># There is not enough money to complete the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t"><span class="nam">TRADE_RETCODE_PRICE_CHANGED</span> <span class="op">=</span> <span class="num">10020</span>  <span class="com"># Prices changed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t"><span class="nam">TRADE_RETCODE_PRICE_OFF</span> <span class="op">=</span> <span class="num">10021</span>  <span class="com"># There are no quotes to process the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID_EXPIRATION</span> <span class="op">=</span> <span class="num">10022</span>  <span class="com"># Invalid order expiration date in the request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t"><span class="nam">TRADE_RETCODE_ORDER_CHANGED</span> <span class="op">=</span> <span class="num">10023</span>  <span class="com"># Order state changed</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t"><span class="nam">TRADE_RETCODE_TOO_MANY_REQUESTS</span> <span class="op">=</span> <span class="num">10024</span>  <span class="com"># Too frequent requests</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t"><span class="nam">TRADE_RETCODE_NO_CHANGES</span> <span class="op">=</span> <span class="num">10025</span>  <span class="com"># No changes in request</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t"><span class="nam">TRADE_RETCODE_SERVER_DISABLES_AT</span> <span class="op">=</span> <span class="num">10026</span>  <span class="com"># Autotrading disabled by server</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t"><span class="nam">TRADE_RETCODE_CLIENT_DISABLES_AT</span> <span class="op">=</span> <span class="num">10027</span>  <span class="com"># Autotrading disabled by client terminal</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t"><span class="nam">TRADE_RETCODE_LOCKED</span> <span class="op">=</span> <span class="num">10028</span>  <span class="com"># Request locked for processing</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t"><span class="nam">TRADE_RETCODE_FROZEN</span> <span class="op">=</span> <span class="num">10029</span>  <span class="com"># Order or position frozen</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t"><span class="nam">TRADE_RETCODE_INVALID_FILL</span> <span class="op">=</span> <span class="num">10030</span>  <span class="com"># Invalid order filling type</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t"><span class="nam">TRADE_RETCODE_CONNECTION</span> <span class="op">=</span> <span class="num">10031</span>  <span class="com"># No connection with the trade server</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t"><span class="nam">TRADE_RETCODE_ONLY_REAL</span> <span class="op">=</span> <span class="num">10032</span>  <span class="com"># Operation is allowed only for real accounts</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t"><span class="nam">TRADE_RETCODE_LIMIT_ORDERS</span> <span class="op">=</span> <span class="num">10033</span>  <span class="com"># The number of pending orders has reached the limit</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t"><span class="nam">TRADE_RETCODE_LIMIT_VOLUME</span> <span class="op">=</span> <span class="num">10034</span>  <span class="com"># The volume of orders and positions has reached the limit</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t"><span class="com"># --- Timeframes ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t"><span class="nam">TIMEFRAME_M1</span> <span class="op">=</span> <span class="num">1</span>       <span class="com"># 1 minute</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t"><span class="nam">TIMEFRAME_M2</span> <span class="op">=</span> <span class="num">2</span>       <span class="com"># 2 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t"><span class="nam">TIMEFRAME_M3</span> <span class="op">=</span> <span class="num">3</span>       <span class="com"># 3 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t"><span class="nam">TIMEFRAME_M4</span> <span class="op">=</span> <span class="num">4</span>       <span class="com"># 4 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t"><span class="nam">TIMEFRAME_M5</span> <span class="op">=</span> <span class="num">5</span>       <span class="com"># 5 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t"><span class="nam">TIMEFRAME_M6</span> <span class="op">=</span> <span class="num">6</span>       <span class="com"># 6 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t"><span class="nam">TIMEFRAME_M10</span> <span class="op">=</span> <span class="num">10</span>     <span class="com"># 10 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t"><span class="nam">TIMEFRAME_M12</span> <span class="op">=</span> <span class="num">12</span>     <span class="com"># 12 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t"><span class="nam">TIMEFRAME_M15</span> <span class="op">=</span> <span class="num">15</span>     <span class="com"># 15 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t"><span class="nam">TIMEFRAME_M20</span> <span class="op">=</span> <span class="num">20</span>     <span class="com"># 20 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t"><span class="nam">TIMEFRAME_M30</span> <span class="op">=</span> <span class="num">30</span>     <span class="com"># 30 minutes</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t"><span class="nam">TIMEFRAME_H1</span> <span class="op">=</span> <span class="num">16385</span>   <span class="com"># 1 hour</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t"><span class="nam">TIMEFRAME_H2</span> <span class="op">=</span> <span class="num">16386</span>   <span class="com"># 2 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t"><span class="nam">TIMEFRAME_H3</span> <span class="op">=</span> <span class="num">16387</span>   <span class="com"># 3 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t"><span class="nam">TIMEFRAME_H4</span> <span class="op">=</span> <span class="num">16388</span>   <span class="com"># 4 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t"><span class="nam">TIMEFRAME_H6</span> <span class="op">=</span> <span class="num">16390</span>   <span class="com"># 6 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t"><span class="nam">TIMEFRAME_H8</span> <span class="op">=</span> <span class="num">16392</span>   <span class="com"># 8 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t"><span class="nam">TIMEFRAME_H12</span> <span class="op">=</span> <span class="num">16396</span>  <span class="com"># 12 hours</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t"><span class="nam">TIMEFRAME_D1</span> <span class="op">=</span> <span class="num">16408</span>   <span class="com"># 1 day</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t"><span class="nam">TIMEFRAME_W1</span> <span class="op">=</span> <span class="num">32769</span>   <span class="com"># 1 week</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t"><span class="nam">TIMEFRAME_MN1</span> <span class="op">=</span> <span class="num">49153</span>  <span class="com"># 1 month</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_21758df60ac2cdd3_mt5_client_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_21758df60ac2cdd3_mt5_event_producer_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 21:24 -0500
        </p>
    </div>
</footer>
</body>
</html>
