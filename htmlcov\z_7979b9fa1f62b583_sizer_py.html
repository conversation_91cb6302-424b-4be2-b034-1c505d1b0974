<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\position_sizer\sizer.py: 11%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\position_sizer\sizer.py</b>:
            <span class="pc_cov">11%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">212 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">24<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">188<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_7979b9fa1f62b583___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_688d3285f67a444f___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 19:57 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">import</span> <span class="nam">math</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span> <span class="com"># Needed for reading performance log for Kelly</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span> <span class="com"># Needed for Kelly calculations</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">timedelta</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">Union</span><span class="op">,</span> <span class="nam">List</span> <span class="com"># For type hints</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="com"># Import the config class</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">config_loader</span> <span class="key">import</span> <span class="nam">get_config</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="com"># Get the config instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="nam">config</span> <span class="op">=</span> <span class="nam">get_config</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">getLogger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="com"># --- Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># Use config values with defaults</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">DEFAULT_METHOD</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">position_sizing_method</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'position_sizing_method'</span><span class="op">)</span> <span class="key">else</span> <span class="str">'elder'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="com"># Elder specific</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="nam">DEFAULT_ELDER_RISK_PERCENT</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">elder_risk_percent</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'elder_risk_percent'</span><span class="op">)</span> <span class="key">else</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="com"># Kelly specific</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="nam">ENABLE_KELLY_SIZING</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">enable_kelly_sizing</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'enable_kelly_sizing'</span><span class="op">)</span> <span class="key">else</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="nam">KELLY_FRACTION</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">kelly_fraction</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'kelly_fraction'</span><span class="op">)</span> <span class="key">else</span> <span class="num">0.25</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="nam">KELLY_PERF_LOG_PATH</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">kelly_perf_log_path</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'kelly_perf_log_path'</span><span class="op">)</span> <span class="key">else</span> <span class="str">'trading_performance.log'</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="nam">KELLY_MIN_TRADES</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">kelly_min_trades</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'kelly_min_trades'</span><span class="op">)</span> <span class="key">else</span> <span class="num">30</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="nam">KELLY_USE_ADAPTIVE_PARAMS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">.</span><span class="nam">kelly_use_adaptive_params</span> <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="str">'kelly_use_adaptive_params'</span><span class="op">)</span> <span class="key">else</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="com"># Ensure Kelly fraction is valid</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="key">if</span> <span class="key">not</span> <span class="op">(</span><span class="num">0</span> <span class="op">&lt;</span> <span class="nam">KELLY_FRACTION</span> <span class="op">&lt;=</span> <span class="num">1.0</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">KELLY_FRACTION (</span><span class="op">{</span><span class="nam">KELLY_FRACTION</span><span class="op">}</span><span class="fst">) out of range (0, 1]. Clamping to [0.01, 1.0].</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">KELLY_FRACTION</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">0.01</span><span class="op">,</span> <span class="nam">min</span><span class="op">(</span><span class="nam">KELLY_FRACTION</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span><span class="op">)</span> <span class="com"># Clamp to reasonable range</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Position Sizer Config: DefaultMethod=</span><span class="op">{</span><span class="nam">DEFAULT_METHOD</span><span class="op">}</span><span class="fst">, ElderRisk=</span><span class="op">{</span><span class="nam">DEFAULT_ELDER_RISK_PERCENT</span><span class="op">}</span><span class="fst">%, KellyEnabled=</span><span class="op">{</span><span class="nam">ENABLE_KELLY_SIZING</span><span class="op">}</span><span class="fst">, KellyFraction=</span><span class="op">{</span><span class="nam">KELLY_FRACTION</span><span class="op">}</span><span class="fst">, KellyLog=</span><span class="op">{</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">basename</span><span class="op">(</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">)</span><span class="op">}</span><span class="fst">, KellyMinTrades=</span><span class="op">{</span><span class="nam">KELLY_MIN_TRADES</span><span class="op">}</span><span class="fst">, KellyAdaptive=</span><span class="op">{</span><span class="nam">KELLY_USE_ADAPTIVE_PARAMS</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t"><span class="com"># --- Helper Function for Kelly Parameter Estimation ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="key">def</span> <span class="nam">_estimate_kelly_parameters_from_log</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">perf_log_path</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="nam">symbol</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="nam">min_trades</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="nam">current_hmm_state</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="nam">use_adaptive</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t"><span class="str">    Estimates Kelly Criterion parameters (p: win rate, b: payoff ratio)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t"><span class="str">    from the performance log, optionally filtered by symbol and HMM regime.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t"><span class="str">        perf_log_path (str): Path to the trading_performance.log CSV file.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t"><span class="str">        symbol (str): The symbol to filter trades for.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t"><span class="str">        min_trades (int): Minimum number of trades required for estimation.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t"><span class="str">        adapter (logging.LoggerAdapter): Logger adapter instance.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t"><span class="str">        current_hmm_state (str | None): The current HMM state label (if use_adaptive=True).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="str">        use_adaptive (bool): Whether to filter trades by the current HMM state.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t"><span class="str">        dict | None: Dictionary with 'p', 'b', 'n_trades', or None if estimation fails.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="nam">log_qualifier</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">"</span> <span class="op">+</span> <span class="op">(</span><span class="fst">f"</span><span class="fst"> (Regime: </span><span class="op">{</span><span class="nam">current_hmm_state</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span> <span class="key">if</span> <span class="nam">use_adaptive</span> <span class="key">and</span> <span class="nam">current_hmm_state</span> <span class="key">else</span> <span class="str">""</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Estimating Kelly params </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst"> from </span><span class="op">{</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">basename</span><span class="op">(</span><span class="nam">perf_log_path</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">perf_log_path</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Performance log not found for Kelly estimation: </span><span class="op">{</span><span class="nam">perf_log_path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">        <span class="com"># --- TODO Enhancement: Implement CSV Chunking for large files ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">        <span class="com"># For now, read the whole file but only necessary columns</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">        <span class="com"># Define columns needed based on whether adaptive is used</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">        <span class="com"># Ensure these names MATCH EXACTLY the header written in metatrader.py __main__</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">        <span class="nam">required_csv_cols</span> <span class="op">=</span> <span class="op">[</span><span class="str">'Symbol'</span><span class="op">,</span> <span class="str">'Profit'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="nam">expected_hmm_col_name</span> <span class="op">=</span> <span class="str">'EntryHmmState'</span> <span class="com"># Name used in the performance log header</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="key">if</span> <span class="nam">use_adaptive</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">            <span class="nam">required_csv_cols</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">expected_hmm_col_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">            <span class="com"># Read only necessary columns, check for header integrity indirectly</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">            <span class="nam">trade_log_df_full</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">read_csv</span><span class="op">(</span><span class="nam">perf_log_path</span><span class="op">,</span> <span class="nam">low_memory</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">encoding</span><span class="op">=</span><span class="str">'utf-8'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">            <span class="com"># Verify required columns exist</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">            <span class="nam">missing_cols</span> <span class="op">=</span> <span class="op">[</span><span class="nam">col</span> <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">required_csv_cols</span> <span class="key">if</span> <span class="nam">col</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">trade_log_df_full</span><span class="op">.</span><span class="nam">columns</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="key">if</span> <span class="nam">missing_cols</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                 <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Missing required columns in </span><span class="op">{</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">basename</span><span class="op">(</span><span class="nam">perf_log_path</span><span class="op">)</span><span class="op">}</span><span class="fst"> for Kelly calc: </span><span class="op">{</span><span class="nam">missing_cols</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                 <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="com"># Select only needed columns after verification</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="nam">trade_log_df</span> <span class="op">=</span> <span class="nam">trade_log_df_full</span><span class="op">[</span><span class="nam">required_csv_cols</span><span class="op">]</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">        <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Performance log file disappeared between check and read: </span><span class="op">{</span><span class="nam">perf_log_path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">read_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error reading performance log </span><span class="op">{</span><span class="nam">perf_log_path</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">read_err</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="nam">exc_info</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">        <span class="com"># Convert Profit column after loading</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="nam">trade_log_df</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">trade_log_df</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="nam">trade_log_df</span><span class="op">.</span><span class="nam">dropna</span><span class="op">(</span><span class="nam">subset</span><span class="op">=</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">,</span> <span class="str">'Symbol'</span><span class="op">]</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span> <span class="com"># Drop rows missing essential info</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="com"># Filter for the specific symbol</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="nam">symbol_trades</span> <span class="op">=</span> <span class="nam">trade_log_df</span><span class="op">[</span><span class="nam">trade_log_df</span><span class="op">[</span><span class="str">'Symbol'</span><span class="op">]</span> <span class="op">==</span> <span class="nam">symbol</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="com"># Filter for specific regime if adaptive mode is enabled</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">        <span class="key">if</span> <span class="nam">use_adaptive</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">            <span class="key">if</span> <span class="nam">current_hmm_state</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adaptive Kelly enabled but no current HMM state provided </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">. Using all symbol trades.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="key">elif</span> <span class="nam">expected_hmm_col_name</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">symbol_trades</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">                 <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Adaptive Kelly enabled but '</span><span class="op">{</span><span class="nam">expected_hmm_col_name</span><span class="op">}</span><span class="fst">' column missing in log </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">. Using all symbol trades.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">                <span class="nam">symbol_trades</span> <span class="op">=</span> <span class="nam">symbol_trades</span><span class="op">[</span><span class="nam">symbol_trades</span><span class="op">[</span><span class="nam">expected_hmm_col_name</span><span class="op">]</span> <span class="op">==</span> <span class="nam">current_hmm_state</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Filtered </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">symbol_trades</span><span class="op">)</span><span class="op">}</span><span class="fst"> trades for regime '</span><span class="op">{</span><span class="nam">current_hmm_state</span><span class="op">}</span><span class="fst">'</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">        <span class="com"># Check minimum trades *after* filtering</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">symbol_trades</span><span class="op">)</span> <span class="op">&lt;</span> <span class="nam">min_trades</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Insufficient trades (</span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">symbol_trades</span><span class="op">)</span><span class="op">}</span><span class="fst"> &lt; </span><span class="op">{</span><span class="nam">min_trades</span><span class="op">}</span><span class="fst">) </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst"> to estimate Kelly params reliably.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="com"># --- Calculate p (win rate) and b (payoff ratio) ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">        <span class="nam">wins</span> <span class="op">=</span> <span class="nam">symbol_trades</span><span class="op">[</span><span class="nam">symbol_trades</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span> <span class="op">></span> <span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">        <span class="nam">losses</span> <span class="op">=</span> <span class="nam">symbol_trades</span><span class="op">[</span><span class="nam">symbol_trades</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span> <span class="op">&lt;</span> <span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">        <span class="nam">n_wins</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">wins</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">        <span class="nam">n_losses</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">losses</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="nam">n_trades</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">symbol_trades</span><span class="op">)</span> <span class="com"># Use length after filtering</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="key">if</span> <span class="nam">n_trades</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span> <span class="key">return</span> <span class="key">None</span> <span class="com"># Should be caught by min_trades</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="nam">p_hat</span> <span class="op">=</span> <span class="nam">n_wins</span> <span class="op">/</span> <span class="nam">n_trades</span> <span class="key">if</span> <span class="nam">n_trades</span> <span class="op">></span> <span class="num">0</span> <span class="key">else</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">        <span class="nam">avg_win</span> <span class="op">=</span> <span class="nam">wins</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">n_wins</span> <span class="op">></span> <span class="num">0</span> <span class="key">else</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="com"># Ensure avg_loss is positive for the ratio calculation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">        <span class="nam">avg_loss</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">abs</span><span class="op">(</span><span class="nam">losses</span><span class="op">[</span><span class="str">'Profit'</span><span class="op">]</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="op">)</span><span class="op">)</span> <span class="key">if</span> <span class="nam">n_losses</span> <span class="op">></span> <span class="num">0</span> <span class="key">else</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="com"># Handle zero or infinite payoff ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">        <span class="key">if</span> <span class="nam">avg_loss</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span> <span class="com"># Effectively zero losses</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">            <span class="key">if</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isclose</span><span class="op">(</span><span class="nam">p_hat</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span><span class="op">:</span> <span class="com"># All wins</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">                 <span class="nam">b_hat</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">inf</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">                 <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly Est: No losses recorded </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">. Payoff ratio infinite.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">            <span class="key">else</span><span class="op">:</span> <span class="com"># p &lt; 1 but no losses? Data issue.</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">                 <span class="nam">b_hat</span> <span class="op">=</span> <span class="num">0</span> <span class="com"># Treat as zero edge or invalid state</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">                 <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly Est: Data inconsistency - win rate &lt; 1 but no losses found </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">                 <span class="key">return</span> <span class="key">None</span> <span class="com"># Indicate estimation failure</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">            <span class="nam">b_hat</span> <span class="op">=</span> <span class="nam">avg_win</span> <span class="op">/</span> <span class="nam">avg_loss</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="com"># --- TODO Enhancement: Return Basis Normalization ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="com"># If accurate risk per trade was logged, normalize Profit before calculating avg_win/avg_loss</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="com"># Example: symbol_trades['NormalizedProfit'] = symbol_trades['Profit'] / symbol_trades['InitialRisk']</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly Params Estimated </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">: p=</span><span class="op">{</span><span class="nam">p_hat</span><span class="op">:</span><span class="fst">.3f</span><span class="op">}</span><span class="fst">, b=</span><span class="op">{</span><span class="nam">b_hat</span><span class="op">:</span><span class="fst">.3f</span><span class="op">}</span><span class="fst"> (from </span><span class="op">{</span><span class="nam">n_trades</span><span class="op">}</span><span class="fst"> trades)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="com"># Return estimated parameters</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">        <span class="key">return</span> <span class="op">{</span><span class="str">'p'</span><span class="op">:</span> <span class="nam">p_hat</span><span class="op">,</span> <span class="str">'b'</span><span class="op">:</span> <span class="nam">b_hat</span><span class="op">,</span> <span class="str">'n_trades'</span><span class="op">:</span> <span class="nam">n_trades</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error estimating Kelly parameters </span><span class="op">{</span><span class="nam">log_qualifier</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t"><span class="com"># --- Sizing Functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t"><span class="key">def</span> <span class="nam">calculate_elder_volume</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">    <span class="nam">account_equity</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">    <span class="nam">risk_percent</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">    <span class="nam">stop_loss_pips</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">    <span class="nam">pip_value_per_lot</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">    <span class="nam">min_volume</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="nam">max_volume</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">    <span class="nam">volume_step</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="nam">adapter</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Make adapter optional</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">    <span class="str">""" Calculates trade volume using a fixed fractional risk model (Elder's rule style). """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">    <span class="com"># Use default logger if no adapter provided</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">    <span class="nam">current_logger</span> <span class="op">=</span> <span class="nam">adapter</span> <span class="key">if</span> <span class="nam">adapter</span> <span class="key">else</span> <span class="nam">logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">    <span class="com"># Input validation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">all</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">arg</span><span class="op">,</span> <span class="op">(</span><span class="nam">int</span><span class="op">,</span> <span class="nam">float</span><span class="op">)</span><span class="op">)</span> <span class="key">for</span> <span class="nam">arg</span> <span class="key">in</span> <span class="op">[</span><span class="nam">account_equity</span><span class="op">,</span> <span class="nam">risk_percent</span><span class="op">,</span> <span class="nam">stop_loss_pips</span><span class="op">,</span> <span class="nam">pip_value_per_lot</span><span class="op">,</span> <span class="nam">min_volume</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">,</span> <span class="nam">volume_step</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="nam">current_logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Invalid input types for calculate_elder_volume."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">    <span class="com"># ... (rest of validation logic using current_logger) ...</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">    <span class="key">if</span> <span class="nam">account_equity</span> <span class="op">&lt;=</span> <span class="num">0</span> <span class="key">or</span> <span class="nam">risk_percent</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">    <span class="key">if</span> <span class="nam">stop_loss_pips</span> <span class="op">&lt;=</span> <span class="num">1e-5</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">    <span class="key">if</span> <span class="nam">pip_value_per_lot</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="op">(</span><span class="num">0</span> <span class="op">&lt;</span> <span class="nam">min_volume</span> <span class="op">&lt;=</span> <span class="nam">max_volume</span> <span class="key">and</span> <span class="nam">volume_step</span> <span class="op">></span> <span class="num">0</span><span class="op">)</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">    <span class="com"># Calculations (same as before)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">    <span class="nam">risk_amount_account_currency</span> <span class="op">=</span> <span class="nam">account_equity</span> <span class="op">*</span> <span class="op">(</span><span class="nam">risk_percent</span> <span class="op">/</span> <span class="num">100.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">    <span class="nam">risk_per_lot_account_currency</span> <span class="op">=</span> <span class="nam">stop_loss_pips</span> <span class="op">*</span> <span class="nam">pip_value_per_lot</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">    <span class="key">if</span> <span class="nam">risk_per_lot_account_currency</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">    <span class="nam">raw_volume</span> <span class="op">=</span> <span class="nam">risk_amount_account_currency</span> <span class="op">/</span> <span class="nam">risk_per_lot_account_currency</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">    <span class="key">if</span> <span class="nam">raw_volume</span> <span class="op">&lt;</span> <span class="nam">min_volume</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">    <span class="com"># Adjust for step</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">    <span class="key">if</span> <span class="nam">volume_step</span> <span class="op">></span> <span class="num">1e-9</span><span class="op">:</span> <span class="nam">stepped_volume</span> <span class="op">=</span> <span class="nam">math</span><span class="op">.</span><span class="nam">floor</span><span class="op">(</span><span class="nam">raw_volume</span> <span class="op">/</span> <span class="nam">volume_step</span><span class="op">)</span> <span class="op">*</span> <span class="nam">volume_step</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">    <span class="key">else</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="nam">stepped_volume</span> <span class="op">=</span> <span class="nam">raw_volume</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">    <span class="nam">stepped_volume</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">0</span><span class="op">,</span> <span class="nam">stepped_volume</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">    <span class="com"># Clamp and final check</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">    <span class="nam">final_volume</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min_volume</span><span class="op">,</span> <span class="nam">min</span><span class="op">(</span><span class="nam">stepped_volume</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">    <span class="key">if</span> <span class="nam">final_volume</span> <span class="op">&lt;</span> <span class="nam">min_volume</span> <span class="op">-</span> <span class="num">1e-9</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">    <span class="key">if</span> <span class="nam">final_volume</span> <span class="op">&lt;</span> <span class="num">1e-9</span><span class="op">:</span> <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="op">...</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">    <span class="key">return</span> <span class="nam">round</span><span class="op">(</span><span class="nam">final_volume</span><span class="op">,</span> <span class="num">8</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t"><span class="key">def</span> <span class="nam">calculate_kelly_volume</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">    <span class="nam">kelly_params</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">    <span class="nam">kelly_fraction</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="com"># The fraction of Kelly to apply (e.g., 0.25)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">    <span class="nam">account_equity</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="com"># Needed to convert fraction risk to volume via Elder framework</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">    <span class="nam">stop_loss_pips</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span> <span class="com"># Needed for risk % calculation within Elder framework</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">    <span class="nam">pip_value_per_lot</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">    <span class="nam">min_volume</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">    <span class="nam">max_volume</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">    <span class="nam">volume_step</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">    <span class="nam">adapter</span><span class="op">:</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t"><span class="str">    Calculates trade volume using estimated Kelly parameters (p, b) and a fraction,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t"><span class="str">    interpreting the result as a risk percentage for the Elder framework.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t"><span class="str">        kelly_params (dict): Dictionary containing 'p' (win rate) and 'b' (payoff ratio).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t"><span class="str">        kelly_fraction (float): Fraction of the optimal Kelly bet to use (e.g., 0.25).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t"><span class="str">        account_equity (float): Current account equity.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t"><span class="str">        stop_loss_pips (float): Stop loss distance in pips.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t"><span class="str">        pip_value_per_lot (float): Value of 1 pip per lot.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t"><span class="str">        min_volume (float): Minimum trade volume.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t"><span class="str">        max_volume (float): Maximum trade volume.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t"><span class="str">        volume_step (float): Volume increment.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t"><span class="str">        adapter (logging.LoggerAdapter): Logger.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t"><span class="str">        float | None: Calculated trade volume in lots, or None if invalid.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">    <span class="nam">p</span> <span class="op">=</span> <span class="nam">kelly_params</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'p'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">    <span class="nam">b</span> <span class="op">=</span> <span class="nam">kelly_params</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'b'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">    <span class="nam">n_trades</span> <span class="op">=</span> <span class="nam">kelly_params</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'n_trades'</span><span class="op">,</span> <span class="num">0</span><span class="op">)</span> <span class="com"># Get number of trades used for estimate</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">    <span class="key">if</span> <span class="nam">p</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">b</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">p</span><span class="op">)</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">b</span><span class="op">)</span> <span class="key">or</span> <span class="nam">b</span> <span class="op">&lt;</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid Kelly parameters received: p=</span><span class="op">{</span><span class="nam">p</span><span class="op">}</span><span class="fst">, b=</span><span class="op">{</span><span class="nam">b</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">    <span class="com"># kelly_fraction already validated in config loading, but double check</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="op">(</span><span class="num">0</span> <span class="op">&lt;</span> <span class="nam">kelly_fraction</span> <span class="op">&lt;=</span> <span class="num">1.0</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid kelly_fraction passed to calculate_kelly_volume: </span><span class="op">{</span><span class="nam">kelly_fraction</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">         <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">    <span class="com"># Calculate optimal Kelly fraction f* = (p*(b+1) - 1) / b</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">    <span class="key">if</span> <span class="nam">b</span> <span class="op">&lt;=</span> <span class="num">1e-9</span><span class="op">:</span> <span class="com"># Handle zero or near-zero payoff ratio</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">         <span class="nam">f_optimal</span> <span class="op">=</span> <span class="op">-</span><span class="nam">np</span><span class="op">.</span><span class="nam">inf</span> <span class="com"># Effectively zero or negative edge</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">    <span class="key">elif</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isinf</span><span class="op">(</span><span class="nam">b</span><span class="op">)</span><span class="op">:</span> <span class="com"># Handle infinite payoff ratio (no losses)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">         <span class="nam">f_optimal</span> <span class="op">=</span> <span class="nam">p</span> <span class="com"># Bet proportion p (theoretically 1 if p=1)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">        <span class="nam">f_optimal</span> <span class="op">=</span> <span class="op">(</span><span class="nam">p</span> <span class="op">*</span> <span class="op">(</span><span class="nam">b</span> <span class="op">+</span> <span class="num">1</span><span class="op">)</span> <span class="op">-</span> <span class="num">1</span><span class="op">)</span> <span class="op">/</span> <span class="nam">b</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Optimal Kelly fraction (f*): </span><span class="op">{</span><span class="nam">f_optimal</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst"> (p=</span><span class="op">{</span><span class="nam">p</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">, b=</span><span class="op">{</span><span class="nam">b</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">, N=</span><span class="op">{</span><span class="nam">n_trades</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">    <span class="com"># If edge is non-positive, recommend no trade</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">    <span class="key">if</span> <span class="nam">f_optimal</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly criterion suggests no trade (f* = </span><span class="op">{</span><span class="nam">f_optimal</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst"> &lt;= 0).</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">    <span class="com"># Apply the fractional Kelly multiplier</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">    <span class="nam">f_actual</span> <span class="op">=</span> <span class="nam">f_optimal</span> <span class="op">*</span> <span class="nam">kelly_fraction</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">    <span class="com"># Clamp actual fraction to prevent excessive leverage (e.g., max 50% capital risk per trade)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">    <span class="nam">f_actual_clamped</span> <span class="op">=</span> <span class="nam">min</span><span class="op">(</span><span class="nam">f_actual</span><span class="op">,</span> <span class="num">0.50</span><span class="op">)</span> <span class="com"># Example clamp at 50% risk max</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">    <span class="key">if</span> <span class="nam">f_actual_clamped</span> <span class="op">&lt;</span> <span class="nam">f_actual</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Clamped actual Kelly fraction from </span><span class="op">{</span><span class="nam">f_actual</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst"> to </span><span class="op">{</span><span class="nam">f_actual_clamped</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">    <span class="nam">f_actual</span> <span class="op">=</span> <span class="nam">f_actual_clamped</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Applying </span><span class="op">{</span><span class="nam">kelly_fraction</span><span class="op">*</span><span class="num">100</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">% of f* -> f_actual = </span><span class="op">{</span><span class="nam">f_actual</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">    <span class="com"># --- Convert Kelly fraction (f_actual) to trade volume via Elder risk % ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">    <span class="nam">risk_percent_from_kelly</span> <span class="op">=</span> <span class="nam">f_actual</span> <span class="op">*</span> <span class="num">100.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Using Kelly-derived risk percentage: </span><span class="op">{</span><span class="nam">risk_percent_from_kelly</span><span class="op">:</span><span class="fst">.3f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">    <span class="com"># Call the robust Elder volume calculation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">    <span class="nam">kelly_derived_volume</span> <span class="op">=</span> <span class="nam">calculate_elder_volume</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">        <span class="nam">account_equity</span><span class="op">=</span><span class="nam">account_equity</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">        <span class="nam">risk_percent</span><span class="op">=</span><span class="nam">risk_percent_from_kelly</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">        <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">stop_loss_pips</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">        <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">pip_value_per_lot</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">        <span class="nam">min_volume</span><span class="op">=</span><span class="nam">min_volume</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">        <span class="nam">max_volume</span><span class="op">=</span><span class="nam">max_volume</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">        <span class="nam">volume_step</span><span class="op">=</span><span class="nam">volume_step</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">        <span class="nam">adapter</span><span class="op">=</span><span class="nam">adapter</span> <span class="com"># Pass adapter down</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">    <span class="key">if</span> <span class="nam">kelly_derived_volume</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly-derived Volume: </span><span class="op">{</span><span class="nam">kelly_derived_volume</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> lots</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">        <span class="com"># This might happen if Kelly % is positive but very small, leading to volume &lt; min_volume</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Kelly fraction resulted in invalid/zero volume via Elder framework."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">    <span class="key">return</span> <span class="nam">kelly_derived_volume</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t"><span class="com"># --- Main Sizing Function (Dispatcher) ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t"><span class="key">def</span> <span class="nam">calculate_position_size</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">    <span class="nam">method</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">    <span class="nam">symbol</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">    <span class="nam">adapter</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">    <span class="nam">current_hmm_state</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">    <span class="op">**</span><span class="nam">kwargs</span><span class="op">:</span> <span class="nam">Any</span> <span class="com"># Use Any for flexible kwargs passing</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t"><span class="str">    Dispatcher function to calculate position size based on the chosen method.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t"><span class="str">    Handles fallback to Elder if Kelly is enabled but fails.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t"><span class="str">        method (str | None): Override sizing method ('elder', 'kelly'). If None, uses default from .env.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t"><span class="str">        symbol (str | None): Required if method='kelly'.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t"><span class="str">        adapter (logging.LoggerAdapter | None): Required for logging, especially for Kelly.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t"><span class="str">        current_hmm_state (str | None): Optional HMM state for adaptive Kelly.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t"><span class="str">        **kwargs: Arguments required by the specific sizing function</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t"><span class="str">                  (account_equity, stop_loss_pips, pip_value_per_lot, min_volume,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t"><span class="str">                   max_volume, volume_step are needed by both).</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t"><span class="str">        float | None: The calculated trade volume, or None if error/invalid.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">    <span class="com"># Use default logger if no adapter is passed (e.g., for testing)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">    <span class="nam">current_logger</span> <span class="op">=</span> <span class="nam">adapter</span> <span class="key">if</span> <span class="nam">adapter</span> <span class="key">else</span> <span class="nam">logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">    <span class="com"># Determine method to use</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">    <span class="nam">sizing_method</span> <span class="op">=</span> <span class="nam">method</span> <span class="key">if</span> <span class="nam">method</span> <span class="key">else</span> <span class="nam">DEFAULT_METHOD</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">    <span class="nam">current_logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Calculating position size using method: </span><span class="op">{</span><span class="nam">sizing_method</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">    <span class="com"># Ensure common required args are present in kwargs</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">    <span class="nam">common_required_args</span> <span class="op">=</span> <span class="op">[</span><span class="str">'account_equity'</span><span class="op">,</span> <span class="str">'stop_loss_pips'</span><span class="op">,</span> <span class="str">'pip_value_per_lot'</span><span class="op">,</span> <span class="str">'min_volume'</span><span class="op">,</span> <span class="str">'max_volume'</span><span class="op">,</span> <span class="str">'volume_step'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">    <span class="nam">missing_args</span> <span class="op">=</span> <span class="op">[</span><span class="nam">k</span> <span class="key">for</span> <span class="nam">k</span> <span class="key">in</span> <span class="nam">common_required_args</span> <span class="key">if</span> <span class="nam">k</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">kwargs</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">    <span class="key">if</span> <span class="nam">missing_args</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">        <span class="nam">current_logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Missing required arguments for ANY sizing method: </span><span class="op">{</span><span class="nam">missing_args</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">    <span class="nam">volume_result</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Initialize volume result</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">    <span class="com"># --- Kelly Sizing Logic ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">    <span class="key">if</span> <span class="nam">sizing_method</span> <span class="op">==</span> <span class="str">'kelly'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">ENABLE_KELLY_SIZING</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t">            <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Kelly sizing selected, but disabled in config. Falling back to Elder."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t">            <span class="nam">sizing_method</span> <span class="op">=</span> <span class="str">'elder'</span> <span class="com"># Fallback trigger</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t">        <span class="key">elif</span> <span class="key">not</span> <span class="nam">symbol</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">             <span class="nam">current_logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Kelly sizing requires 'symbol' argument."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">             <span class="nam">sizing_method</span> <span class="op">=</span> <span class="str">'elder'</span> <span class="com"># Fallback trigger</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">        <span class="key">elif</span> <span class="key">not</span> <span class="nam">adapter</span><span class="op">:</span> <span class="com"># Adapter needed for sub-functions</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">             <span class="nam">current_logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Kelly sizing requires 'adapter' argument."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">             <span class="nam">sizing_method</span> <span class="op">=</span> <span class="str">'elder'</span> <span class="com"># Fallback trigger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">            <span class="com"># Estimate Kelly parameters</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">            <span class="nam">kelly_params</span> <span class="op">=</span> <span class="nam">_estimate_kelly_parameters_from_log</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">                <span class="nam">perf_log_path</span><span class="op">=</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">                <span class="nam">symbol</span><span class="op">=</span><span class="nam">symbol</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">                <span class="nam">min_trades</span><span class="op">=</span><span class="nam">KELLY_MIN_TRADES</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">                <span class="nam">adapter</span><span class="op">=</span><span class="nam">adapter</span><span class="op">,</span> <span class="com"># Pass the validated adapter</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">                <span class="nam">current_hmm_state</span><span class="op">=</span><span class="nam">current_hmm_state</span> <span class="key">if</span> <span class="nam">KELLY_USE_ADAPTIVE_PARAMS</span> <span class="key">else</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">                <span class="nam">use_adaptive</span><span class="op">=</span><span class="nam">KELLY_USE_ADAPTIVE_PARAMS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">            <span class="key">if</span> <span class="nam">kelly_params</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">                <span class="nam">volume_result</span> <span class="op">=</span> <span class="nam">calculate_kelly_volume</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">                    <span class="nam">kelly_params</span><span class="op">=</span><span class="nam">kelly_params</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">                    <span class="nam">kelly_fraction</span><span class="op">=</span><span class="nam">KELLY_FRACTION</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">                    <span class="com"># Pass all necessary args from kwargs</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">                    <span class="nam">account_equity</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">                    <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">                    <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">                    <span class="nam">min_volume</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">                    <span class="nam">max_volume</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">                    <span class="nam">volume_step</span><span class="op">=</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">                    <span class="nam">adapter</span><span class="op">=</span><span class="nam">adapter</span> <span class="com"># Pass the validated adapter</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">                <span class="key">if</span> <span class="nam">volume_result</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">                     <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Kelly volume calculation failed. Falling back to Elder."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">                     <span class="nam">sizing_method</span> <span class="op">=</span> <span class="str">'elder'</span> <span class="com"># Fallback trigger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">                 <span class="com"># If volume_result is valid, it will be returned at the end</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">                <span class="nam">current_logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Kelly parameter estimation failed. Falling back to Elder."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">                <span class="nam">sizing_method</span> <span class="op">=</span> <span class="str">'elder'</span> <span class="com"># Fallback trigger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">    <span class="com"># --- Elder Sizing Logic (Default or Fallback) ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">    <span class="com"># This block executes if method was initially 'elder' OR if Kelly failed and method was set to 'elder'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">    <span class="key">if</span> <span class="nam">sizing_method</span> <span class="op">==</span> <span class="str">'elder'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">        <span class="com"># Use default risk % if 'risk_percent' not explicitly passed in kwargs</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">        <span class="nam">risk_percent</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'risk_percent'</span><span class="op">,</span> <span class="nam">DEFAULT_ELDER_RISK_PERCENT</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t">        <span class="nam">volume_result</span> <span class="op">=</span> <span class="nam">calculate_elder_volume</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">            <span class="nam">risk_percent</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">            <span class="nam">kwargs</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">            <span class="nam">adapter</span><span class="op">=</span><span class="nam">adapter</span> <span class="com"># Pass adapter</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">        <span class="com"># If Elder fallback also fails, volume_result will be None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">    <span class="com"># --- Final Return ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">    <span class="key">if</span> <span class="nam">volume_result</span> <span class="key">is</span> <span class="key">None</span> <span class="key">and</span> <span class="nam">sizing_method</span> <span class="op">!=</span> <span class="str">'elder'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">         <span class="com"># Safety net if somehow a non-elder method was specified but failed without fallback</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">         <span class="nam">current_logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Position sizing failed for method '</span><span class="op">{</span><span class="nam">sizing_method</span><span class="op">}</span><span class="fst">' and did not fall back to Elder.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">    <span class="key">return</span> <span class="nam">volume_result</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">    <span class="com"># --- Setup Basic Logging for Testing ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">    <span class="com"># Note: If run standalone, uses basic logger. If imported, uses configured logger.</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">getLogger</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">hasHandlers</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">         <span class="nam">logging</span><span class="op">.</span><span class="nam">basicConfig</span><span class="op">(</span><span class="nam">level</span><span class="op">=</span><span class="nam">logging</span><span class="op">.</span><span class="nam">INFO</span><span class="op">,</span> <span class="nam">format</span><span class="op">=</span><span class="str">'%(asctime)s - %(levelname)s - %(name)s - %(message)s'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">    <span class="com"># Example Args Dictionary</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">    <span class="nam">args_main</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">        <span class="str">'account_equity'</span><span class="op">:</span> <span class="num">10000.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">        <span class="str">'stop_loss_pips'</span><span class="op">:</span> <span class="num">30.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">        <span class="str">'pip_value_per_lot'</span><span class="op">:</span> <span class="num">10.0</span><span class="op">,</span> <span class="com"># Example for EURUSD std lot on USD account</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">        <span class="str">'min_volume'</span><span class="op">:</span> <span class="num">0.01</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">        <span class="str">'max_volume'</span><span class="op">:</span> <span class="num">100.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t">        <span class="str">'volume_step'</span><span class="op">:</span> <span class="num">0.01</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">        <span class="str">'risk_percent'</span><span class="op">:</span> <span class="num">1.5</span> <span class="com"># Optional override for Elder</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t">    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t">    <span class="nam">test_symbol</span> <span class="op">=</span> <span class="str">'EURUSD'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t">    <span class="com"># Create a dummy adapter for testing standalone if needed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t">    <span class="nam">test_adapter</span> <span class="op">=</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">LoggerAdapter</span><span class="op">(</span><span class="nam">logger</span><span class="op">,</span> <span class="op">{</span><span class="str">'instance_id'</span><span class="op">:</span> <span class="str">'sizer_test'</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Testing Dispatcher ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Default Method from config: </span><span class="op">{</span><span class="nam">DEFAULT_METHOD</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Kelly Enabled from config: </span><span class="op">{</span><span class="nam">ENABLE_KELLY_SIZING</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">    <span class="com"># Test Default Method</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Testing Default Method ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">    <span class="nam">vol_default</span> <span class="op">=</span> <span class="nam">calculate_position_size</span><span class="op">(</span><span class="nam">symbol</span><span class="op">=</span><span class="nam">test_symbol</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">=</span><span class="nam">test_adapter</span><span class="op">,</span> <span class="nam">account_equity</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span> <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span> <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span> <span class="nam">min_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">volume_step</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span> <span class="nam">risk_percent</span><span class="op">=</span><span class="nam">args_main</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'risk_percent'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">    <span class="key">if</span> <span class="nam">vol_default</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Volume (Default Method '</span><span class="op">{</span><span class="nam">DEFAULT_METHOD</span><span class="op">}</span><span class="fst">'): </span><span class="op">{</span><span class="nam">vol_default</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> lots</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">    <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Volume calculation failed (Default Method)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">    <span class="com"># Test Forced Elder</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Testing Forced Elder ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t">    <span class="nam">vol_elder</span> <span class="op">=</span> <span class="nam">calculate_position_size</span><span class="op">(</span><span class="nam">method</span><span class="op">=</span><span class="str">'elder'</span><span class="op">,</span> <span class="nam">symbol</span><span class="op">=</span><span class="nam">test_symbol</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">=</span><span class="nam">test_adapter</span><span class="op">,</span> <span class="nam">account_equity</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span> <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span> <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span> <span class="nam">min_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">volume_step</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span> <span class="nam">risk_percent</span><span class="op">=</span><span class="nam">args_main</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'risk_percent'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">    <span class="key">if</span> <span class="nam">vol_elder</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Volume (Forced Elder, 1.5% risk): </span><span class="op">{</span><span class="nam">vol_elder</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> lots</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">    <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Volume calculation failed (Forced Elder)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">    <span class="com"># Test Forced Kelly (only proceeds if enabled and log exists/is valid)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Testing Forced Kelly (Requires Log &amp; Enable Flag) ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">    <span class="key">if</span> <span class="nam">ENABLE_KELLY_SIZING</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">        <span class="com"># Create dummy performance log if it doesn't exist for testing</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">)</span> <span class="key">or</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">getsize</span><span class="op">(</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">)</span> <span class="op">&lt;</span> <span class="num">50</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">             <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Creating dummy performance log for Kelly testing..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t">             <span class="nam">dummy_header</span> <span class="op">=</span> <span class="str">"DealTicket,OrderTicket,Symbol,Type,EntryReason,Volume,Price,Profit,EntryTime,CloseTime,EntryMarketOverlap,EntryMajorSessions,EntryTrendH1,EntryTrendH4,EntryPatternM5,EntryConfidenceM5,EntryPatternH4,EntryConfidenceH4,EntryGarchStatus,EntryGarchVol,EntryHmmState,EntryHaTrend,EntryHaColor\n"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">             <span class="com"># Generate some plausible win/loss data</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">             <span class="nam">profits</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">random</span><span class="op">.</span><span class="nam">choice</span><span class="op">(</span><span class="op">[</span><span class="num">50</span><span class="op">,</span> <span class="op">-</span><span class="num">25</span><span class="op">]</span><span class="op">,</span> <span class="nam">size</span><span class="op">=</span><span class="num">40</span><span class="op">,</span> <span class="nam">p</span><span class="op">=</span><span class="op">[</span><span class="num">0.6</span><span class="op">,</span> <span class="num">0.4</span><span class="op">]</span><span class="op">)</span><span class="op">)</span> <span class="com"># 60% win rate, payoff ~2:1</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">             <span class="nam">dummy_rows</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">                 <span class="fst">f"</span><span class="fst">1</span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">,2</span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">,</span><span class="op">{</span><span class="nam">test_symbol</span><span class="op">}</span><span class="fst">,BUY,SL_TP,0.1,1.1,</span><span class="op">{</span><span class="nam">p</span><span class="op">}</span><span class="fst">,</span><span class="op">{</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">-</span><span class="nam">timedelta</span><span class="op">(</span><span class="nam">hours</span><span class="op">=</span><span class="nam">i</span><span class="op">+</span><span class="num">1</span><span class="op">)</span><span class="op">:</span><span class="fst">%Y-%m-%d %H:%M:%S</span><span class="op">}</span><span class="fst">,</span><span class="op">{</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">-</span><span class="nam">timedelta</span><span class="op">(</span><span class="nam">hours</span><span class="op">=</span><span class="nam">i</span><span class="op">)</span><span class="op">:</span><span class="fst">%Y-%m-%d %H:%M:%S</span><span class="op">}</span><span class="fst">,None,London,Up,Up,Hammer,0.8,None,0.0,Med,0.15,State1,Up,Green</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">                 <span class="key">for</span> <span class="nam">i</span><span class="op">,</span> <span class="nam">p</span> <span class="key">in</span> <span class="nam">enumerate</span><span class="op">(</span><span class="nam">profits</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">             <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">             <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">                 <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">,</span> <span class="str">'w'</span><span class="op">,</span> <span class="nam">encoding</span><span class="op">=</span><span class="str">'utf-8'</span><span class="op">,</span> <span class="nam">newline</span><span class="op">=</span><span class="str">''</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">                     <span class="nam">f</span><span class="op">.</span><span class="nam">write</span><span class="op">(</span><span class="nam">dummy_header</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">                     <span class="nam">f</span><span class="op">.</span><span class="nam">write</span><span class="op">(</span><span class="str">"\n"</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">dummy_rows</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">                 <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Dummy performance log created: </span><span class="op">{</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">             <span class="key">except</span> <span class="nam">IOError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">                 <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed to create dummy log: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">        <span class="com"># Test Kelly - Global</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Testing Kelly Global ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">        <span class="nam">vol_kelly_global</span> <span class="op">=</span> <span class="nam">calculate_position_size</span><span class="op">(</span><span class="nam">method</span><span class="op">=</span><span class="str">'kelly'</span><span class="op">,</span> <span class="nam">symbol</span><span class="op">=</span><span class="nam">test_symbol</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">=</span><span class="nam">test_adapter</span><span class="op">,</span> <span class="nam">account_equity</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span> <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span> <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span> <span class="nam">min_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">volume_step</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span> <span class="nam">risk_percent</span><span class="op">=</span><span class="nam">args_main</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'risk_percent'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">        <span class="key">if</span> <span class="nam">vol_kelly_global</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Volume (Kelly Global): </span><span class="op">{</span><span class="nam">vol_kelly_global</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> lots</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">        <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Volume calculation failed (Kelly Global)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t">        <span class="com"># Test Kelly - Adaptive (if enabled)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t">        <span class="key">if</span> <span class="nam">KELLY_USE_ADAPTIVE_PARAMS</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t">              <span class="nam">test_state</span> <span class="op">=</span> <span class="str">'State1'</span> <span class="com"># Example state from dummy log</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t">              <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n--- Testing Kelly Adaptive for state: </span><span class="op">{</span><span class="nam">test_state</span><span class="op">}</span><span class="fst"> ---</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">              <span class="com"># Ensure the dummy log has the 'EntryHmmState' column if testing adaptive</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">              <span class="key">if</span> <span class="str">'EntryHmmState'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">read_csv</span><span class="op">(</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">,</span> <span class="nam">nrows</span><span class="op">=</span><span class="num">0</span><span class="op">)</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">                   <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Cannot test adaptive Kelly: '</span><span class="op">{</span><span class="nam">KELLY_PERF_LOG_PATH</span><span class="op">}</span><span class="fst">' missing 'EntryHmmState' column.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t476" href="#t476">476</a></span><span class="t">              <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t477" href="#t477">477</a></span><span class="t">                   <span class="nam">vol_kelly_adaptive</span> <span class="op">=</span> <span class="nam">calculate_position_size</span><span class="op">(</span><span class="nam">method</span><span class="op">=</span><span class="str">'kelly'</span><span class="op">,</span> <span class="nam">symbol</span><span class="op">=</span><span class="nam">test_symbol</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">=</span><span class="nam">test_adapter</span><span class="op">,</span> <span class="nam">current_hmm_state</span><span class="op">=</span><span class="nam">test_state</span><span class="op">,</span> <span class="nam">account_equity</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'account_equity'</span><span class="op">]</span><span class="op">,</span> <span class="nam">stop_loss_pips</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'stop_loss_pips'</span><span class="op">]</span><span class="op">,</span> <span class="nam">pip_value_per_lot</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'pip_value_per_lot'</span><span class="op">]</span><span class="op">,</span> <span class="nam">min_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'min_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">max_volume</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'max_volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">volume_step</span><span class="op">=</span><span class="nam">args_main</span><span class="op">[</span><span class="str">'volume_step'</span><span class="op">]</span><span class="op">,</span> <span class="nam">risk_percent</span><span class="op">=</span><span class="nam">args_main</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'risk_percent'</span><span class="op">,</span> <span class="nam">DEFAULT_ELDER_RISK_PERCENT</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t478" href="#t478">478</a></span><span class="t">                   <span class="key">if</span> <span class="nam">vol_kelly_adaptive</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Volume (Kelly Adaptive '</span><span class="op">{</span><span class="nam">test_state</span><span class="op">}</span><span class="fst">'): </span><span class="op">{</span><span class="nam">vol_kelly_adaptive</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst"> lots</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t479" href="#t479">479</a></span><span class="t">                   <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Volume calculation failed (Kelly Adaptive '</span><span class="op">{</span><span class="nam">test_state</span><span class="op">}</span><span class="fst">').</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t480" href="#t480">480</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t481" href="#t481">481</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Adaptive Kelly disabled in config, skipping adaptive test."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t482" href="#t482">482</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t483" href="#t483">483</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t484" href="#t484">484</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Kelly sizing disabled in config, skipping Kelly test."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_7979b9fa1f62b583___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_688d3285f67a444f___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 19:57 -0500
        </p>
    </div>
</footer>
</body>
</html>
