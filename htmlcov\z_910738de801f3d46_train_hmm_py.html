<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\models\train_hmm.py: 13%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\models\train_hmm.py</b>:
            <span class="pc_cov">13%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">192 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">25<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">167<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_6696b9cca54af58b_model_monitoring_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_21758df60ac2cdd3_mt5_client_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:43 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># train_hmm.py (CSV Version - Updated for Dukascopy Format)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">joblib</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="com"># import MetaTrader5 as mt5 # No longer needed for CSV loading</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">hmmlearn</span> <span class="key">import</span> <span class="nam">hmm</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">dotenv</span> <span class="key">import</span> <span class="nam">load_dotenv</span><span class="op">,</span> <span class="nam">find_dotenv</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="com"># --- Basic Setup ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="nam">logging</span><span class="op">.</span><span class="nam">basicConfig</span><span class="op">(</span><span class="nam">level</span><span class="op">=</span><span class="nam">logging</span><span class="op">.</span><span class="nam">INFO</span><span class="op">,</span> <span class="nam">format</span><span class="op">=</span><span class="str">'%(asctime)s - %(levelname)s - %(message)s'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="com"># Assume this script is in the project root directory</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="nam">project_root</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">dirname</span><span class="op">(</span><span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">abspath</span><span class="op">(</span><span class="nam">__file__</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># Define paths relative to the project root</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">models_dir</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">project_root</span><span class="op">,</span> <span class="str">'src'</span><span class="op">,</span> <span class="str">'forex_bot'</span><span class="op">,</span> <span class="str">'models'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="nam">data_dir</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">project_root</span><span class="op">,</span> <span class="str">'data'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="com"># --- Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="com"># load_dotenv(find_dotenv()) # Only needed if script uses other .env variables</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="com"># --- >>> CSV Data Configuration &lt;&lt;&lt; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="nam">DATA_CSV_FILENAME</span> <span class="op">=</span> <span class="str">"USDCNH_Candlestick_1_Hour_BID_01.01.2012-01.01.2025.csv"</span>  <span class="com"># &lt;&lt;&lt; --- IMPORTANT: VERIFY/CHANGE THIS FILENAME --- &lt;&lt;&lt;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="nam">DATA_CSV_PATH</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">data_dir</span><span class="op">,</span> <span class="nam">DATA_CSV_FILENAME</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="com"># --- >>> END CSV CONFIG &lt;&lt;&lt; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="com"># Training Parameters</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="nam">N_STATES</span> <span class="op">=</span> <span class="num">3</span>            <span class="com"># Number of hidden states (e.g., 2=Trending/Ranging, 3=Up/Down/Range) - Tune this!</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="nam">N_ITERATIONS</span> <span class="op">=</span> <span class="num">150</span>      <span class="com"># Iterations for HMM fitting</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="nam">RANDOM_STATE_SEED</span> <span class="op">=</span> <span class="num">42</span>  <span class="com"># For reproducibility</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">MODEL_OUTPUT_DIR</span> <span class="op">=</span> <span class="nam">models_dir</span> <span class="com"># Use calculated models dir</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="nam">MODEL_FILENAME</span> <span class="op">=</span> <span class="str">"hmm_model.joblib"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="nam">MODEL_OUTPUT_PATH</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">MODEL_OUTPUT_DIR</span><span class="op">,</span> <span class="nam">MODEL_FILENAME</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="com"># Feature Calculation Configuration</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="nam">FEATURE_METHOD</span> <span class="op">=</span> <span class="str">'returns'</span> <span class="com"># Currently supported: 'returns'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="com"># VOLATILITY_WINDOW = 20 # Example if adding volatility feature later</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="com"># --- Helper Functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t"><span class="key">def</span> <span class="nam">load_training_data_from_csv</span><span class="op">(</span><span class="nam">csv_path</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="str">"""Loads historical data from CSV, tailored for Dukascopy H1 format."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Loading data from CSV: </span><span class="op">{</span><span class="nam">csv_path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">csv_path</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">CSV file not found: </span><span class="op">{</span><span class="nam">csv_path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="com"># Read CSV, assuming comma separator and header on row 0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">        <span class="nam">df</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">read_csv</span><span class="op">(</span><span class="nam">csv_path</span><span class="op">,</span> <span class="nam">sep</span><span class="op">=</span><span class="str">','</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Initial columns from CSV: </span><span class="op">{</span><span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">.</span><span class="nam">tolist</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">        <span class="com"># --- >>> Specific Timestamp Parsing for 'Gmt time' &lt;&lt;&lt; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="nam">timestamp_col</span> <span class="op">=</span> <span class="str">'Gmt time'</span> <span class="com"># Exact column name from your CSV sample</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="nam">timestamp_format</span> <span class="op">=</span> <span class="str">'%d.%m.%Y %H:%M:%S.%f'</span> <span class="com"># Exact format string from your CSV sample</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="key">if</span> <span class="nam">timestamp_col</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Required timestamp column '</span><span class="op">{</span><span class="nam">timestamp_col</span><span class="op">}</span><span class="fst">' not found in CSV header: </span><span class="op">{</span><span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">.</span><span class="nam">tolist</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Attempting to parse timestamp from '</span><span class="op">{</span><span class="nam">timestamp_col</span><span class="op">}</span><span class="fst">' using format '</span><span class="op">{</span><span class="nam">timestamp_format</span><span class="op">}</span><span class="fst">'...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">            <span class="com"># Use pd.to_datetime with the specific format and set utc=True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">            <span class="nam">df</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_datetime</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="nam">timestamp_col</span><span class="op">]</span><span class="op">,</span> <span class="nam">format</span><span class="op">=</span><span class="nam">timestamp_format</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="nam">failed_parses</span> <span class="op">=</span> <span class="nam">df</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">.</span><span class="nam">isnull</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">sum</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="key">if</span> <span class="nam">failed_parses</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">                 <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Could not parse </span><span class="op">{</span><span class="nam">failed_parses</span><span class="op">}</span><span class="fst"> rows using format '</span><span class="op">{</span><span class="nam">timestamp_format</span><span class="op">}</span><span class="fst">'. Check data consistency.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error during timestamp parsing with specific format: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Check if '</span><span class="op">{</span><span class="nam">timestamp_col</span><span class="op">}</span><span class="fst">' column exists and all rows match format '</span><span class="op">{</span><span class="nam">timestamp_format</span><span class="op">}</span><span class="fst">'.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">        <span class="com"># Drop rows where timestamp parsing failed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">dropna</span><span class="op">(</span><span class="nam">subset</span><span class="op">=</span><span class="op">[</span><span class="str">'time'</span><span class="op">]</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="key">if</span> <span class="nam">df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"DataFrame is empty after timestamp parsing and NaT removal."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">set_index</span><span class="op">(</span><span class="str">'time'</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">sort_index</span><span class="op">(</span><span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span> <span class="com"># Ensure chronological order</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="com"># --- >>> End Timestamp Parsing &lt;&lt;&lt; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="com"># Rename columns (Optional - can be removed if source names match target exactly)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">        <span class="com"># Since your sample header already uses 'Open', 'High', 'Low', 'Close', 'Volume',</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">        <span class="com"># this map primarily acts as a safeguard or handles slight case differences.</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">        <span class="nam">rename_map</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="str">'Gmt time'</span><span class="op">:</span> <span class="str">'OriginalTimestamp'</span><span class="op">,</span> <span class="com"># Keep original timestamp if needed, rename to avoid clash</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="str">'open'</span><span class="op">:</span> <span class="str">'Open'</span><span class="op">,</span> <span class="str">'OpenPrice'</span><span class="op">:</span> <span class="str">'Open'</span><span class="op">,</span> <span class="str">'openprice'</span><span class="op">:</span> <span class="str">'Open'</span><span class="op">,</span> <span class="str">'Open'</span><span class="op">:</span> <span class="str">'Open'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="str">'high'</span><span class="op">:</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'HighPrice'</span><span class="op">:</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'highprice'</span><span class="op">:</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'High'</span><span class="op">:</span> <span class="str">'High'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="str">'low'</span><span class="op">:</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'LowPrice'</span><span class="op">:</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'lowprice'</span><span class="op">:</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">:</span> <span class="str">'Low'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="str">'close'</span><span class="op">:</span> <span class="str">'Close'</span><span class="op">,</span> <span class="str">'ClosePrice'</span><span class="op">:</span> <span class="str">'Close'</span><span class="op">,</span><span class="str">'closeprice'</span><span class="op">:</span> <span class="str">'Close'</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">:</span> <span class="str">'Close'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="str">'volume'</span><span class="op">:</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="str">'TotalVolume'</span><span class="op">:</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="str">'Volume_'</span><span class="op">:</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="str">'vol'</span><span class="op">:</span> <span class="str">'Volume'</span><span class="op">,</span> <span class="str">'Volume'</span><span class="op">:</span> <span class="str">'Volume'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="com"># Apply renaming only for columns that exist in the DataFrame</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">rename</span><span class="op">(</span><span class="nam">columns</span><span class="op">=</span><span class="op">{</span><span class="nam">k</span><span class="op">:</span> <span class="nam">v</span> <span class="key">for</span> <span class="nam">k</span><span class="op">,</span> <span class="nam">v</span> <span class="key">in</span> <span class="nam">rename_map</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">k</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">}</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Columns after renaming attempt: </span><span class="op">{</span><span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">.</span><span class="nam">tolist</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="com"># Ensure required OHLC columns exist (should be fine given your sample)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="nam">required_cols</span> <span class="op">=</span> <span class="op">[</span><span class="str">'Open'</span><span class="op">,</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">all</span><span class="op">(</span><span class="nam">col</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span> <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">required_cols</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">             <span class="nam">missing</span> <span class="op">=</span> <span class="op">[</span><span class="nam">col</span> <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">required_cols</span> <span class="key">if</span> <span class="nam">col</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">CSV missing required OHLC columns after rename attempt: </span><span class="op">{</span><span class="nam">missing</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">        <span class="com"># Convert OHLC columns to numeric, coercing errors</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">        <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">required_cols</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="nam">df</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="nam">col</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="com"># Handle Volume column</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">        <span class="key">if</span> <span class="str">'Volume'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"'Volume' column not found in CSV, adding column with zeros."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">             <span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span> <span class="op">=</span> <span class="num">0.0</span> <span class="com"># Use float initially</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">             <span class="com"># Convert Volume, coercing errors and filling potential NaNs with 0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">             <span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_numeric</span><span class="op">(</span><span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span><span class="op">,</span> <span class="nam">errors</span><span class="op">=</span><span class="str">'coerce'</span><span class="op">)</span><span class="op">.</span><span class="nam">fillna</span><span class="op">(</span><span class="num">0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">        <span class="com"># Drop rows with NaN in essential OHLC columns after conversion</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="nam">initial_rows</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">dropna</span><span class="op">(</span><span class="nam">subset</span><span class="op">=</span><span class="nam">required_cols</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">        <span class="nam">rows_dropped</span> <span class="op">=</span> <span class="nam">initial_rows</span> <span class="op">-</span> <span class="nam">len</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">        <span class="key">if</span> <span class="nam">rows_dropped</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Dropped </span><span class="op">{</span><span class="nam">rows_dropped</span><span class="op">}</span><span class="fst"> rows due to NaN values in OHLC columns.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">        <span class="key">if</span> <span class="nam">df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"DataFrame is empty after processing NaNs."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">        <span class="com"># Ensure Volume is integer type after handling NaNs</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="key">if</span> <span class="str">'Volume'</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">             <span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">df</span><span class="op">[</span><span class="str">'Volume'</span><span class="op">]</span><span class="op">.</span><span class="nam">astype</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">int64</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Loaded and processed </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span><span class="op">}</span><span class="fst"> rows from CSV.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">        <span class="com"># Select only standard columns needed by the rest of the script/bot</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">        <span class="nam">final_cols</span> <span class="op">=</span> <span class="op">[</span><span class="str">'Open'</span><span class="op">,</span> <span class="str">'High'</span><span class="op">,</span> <span class="str">'Low'</span><span class="op">,</span> <span class="str">'Close'</span><span class="op">,</span> <span class="str">'Volume'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">        <span class="com"># Ensure only these columns are returned, dropping any extra ones like 'OriginalTimestamp'</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="nam">df_final</span> <span class="op">=</span> <span class="nam">df</span><span class="op">[</span><span class="nam">final_cols</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="key">return</span> <span class="nam">df_final</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error loading or processing CSV data: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t"><span class="key">def</span> <span class="nam">calculate_features</span><span class="op">(</span><span class="nam">df</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="str">"""Calculates features for HMM training."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Calculating features using method: </span><span class="op">{</span><span class="nam">FEATURE_METHOD</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">    <span class="key">if</span> <span class="nam">df</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Cannot calculate features: Input DataFrame is empty or None."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">    <span class="key">if</span> <span class="nam">FEATURE_METHOD</span> <span class="op">==</span> <span class="str">'returns'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">        <span class="key">if</span> <span class="str">'Close'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">df</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Feature calculation error: 'Close' column missing."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="com"># Use log returns of the 'Close' price</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="com"># Add a small epsilon to prevent log(0) errors if Close price can be zero</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="nam">epsilon</span> <span class="op">=</span> <span class="num">1e-10</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="nam">close_prices</span> <span class="op">=</span> <span class="nam">df</span><span class="op">[</span><span class="str">'Close'</span><span class="op">]</span><span class="op">.</span><span class="nam">clip</span><span class="op">(</span><span class="nam">lower</span><span class="op">=</span><span class="nam">epsilon</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">        <span class="nam">df</span><span class="op">[</span><span class="str">'LogReturn'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">log</span><span class="op">(</span><span class="nam">close_prices</span> <span class="op">/</span> <span class="nam">close_prices</span><span class="op">.</span><span class="nam">shift</span><span class="op">(</span><span class="num">1</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">        <span class="com"># Drop NaN introduced by shift or original NaNs in Close</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">        <span class="nam">df</span><span class="op">.</span><span class="nam">dropna</span><span class="op">(</span><span class="nam">subset</span><span class="op">=</span><span class="op">[</span><span class="str">'LogReturn'</span><span class="op">]</span><span class="op">,</span> <span class="nam">inplace</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="key">if</span> <span class="nam">df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"DataFrame empty after calculating log returns and dropping NaN."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">        <span class="com"># Reshape for hmmlearn: needs (n_samples, n_features)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">        <span class="nam">features</span> <span class="op">=</span> <span class="nam">df</span><span class="op">[</span><span class="str">'LogReturn'</span><span class="op">]</span><span class="op">.</span><span class="nam">values</span><span class="op">.</span><span class="nam">reshape</span><span class="op">(</span><span class="op">-</span><span class="num">1</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Calculated Log Returns. Feature shape: </span><span class="op">{</span><span class="nam">features</span><span class="op">.</span><span class="nam">shape</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="com"># Check for non-finite values introduced during calculation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">np</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">features</span><span class="op">)</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">             <span class="nam">num_non_finite</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">sum</span><span class="op">(</span><span class="op">~</span><span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">features</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Feature calculation resulted in </span><span class="op">{</span><span class="nam">num_non_finite</span><span class="op">}</span><span class="fst"> non-finite values (Inf/NaN). Check input data quality.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">             <span class="com"># Optionally handle or remove non-finite values</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">             <span class="com"># features = features[np.isfinite(features)].reshape(-1, 1) # Example: Remove them</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">             <span class="key">return</span> <span class="key">None</span> <span class="com"># Safer to abort if non-finite values appear</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">        <span class="key">return</span> <span class="nam">features</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">    <span class="com"># Add elif for 'volatility' or other methods here if needed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">    <span class="com"># elif FEATURE_METHOD == 'volatility':</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">    <span class="com">#     # ... calculate rolling volatility ...</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">    <span class="com">#     return features</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Invalid FEATURE_METHOD specified: </span><span class="op">{</span><span class="nam">FEATURE_METHOD</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t"><span class="com"># --- Main Training Logic ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"--- Starting HMM Model Training (from CSV) ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">    <span class="com"># Load data from CSV</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">    <span class="nam">data_df</span> <span class="op">=</span> <span class="nam">load_training_data_from_csv</span><span class="op">(</span><span class="nam">DATA_CSV_PATH</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">    <span class="key">if</span> <span class="nam">data_df</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">data_df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="str">"Failed to load training data from CSV. Aborting."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">    <span class="com"># Calculate features</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">    <span class="nam">training_features</span> <span class="op">=</span> <span class="nam">calculate_features</span><span class="op">(</span><span class="nam">data_df</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span><span class="op">)</span> <span class="com"># Pass copy</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">    <span class="key">if</span> <span class="nam">training_features</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">len</span><span class="op">(</span><span class="nam">training_features</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">        <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="str">"Failed to calculate features for training. Aborting."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">    <span class="com"># Initialize and train HMM model</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Initializing GaussianHMM with n_states=</span><span class="op">{</span><span class="nam">N_STATES</span><span class="op">}</span><span class="fst">, n_iter=</span><span class="op">{</span><span class="nam">N_ITERATIONS</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">    <span class="nam">model</span> <span class="op">=</span> <span class="nam">hmm</span><span class="op">.</span><span class="nam">GaussianHMM</span><span class="op">(</span><span class="nam">n_components</span><span class="op">=</span><span class="nam">N_STATES</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">                            <span class="nam">covariance_type</span><span class="op">=</span><span class="str">"diag"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">                            <span class="nam">n_iter</span><span class="op">=</span><span class="nam">N_ITERATIONS</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">                            <span class="nam">random_state</span><span class="op">=</span><span class="nam">RANDOM_STATE_SEED</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">                            <span class="nam">verbose</span><span class="op">=</span><span class="key">False</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">                            <span class="nam">tol</span><span class="op">=</span><span class="num">1e-3</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">                           <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Fitting HMM model..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="nam">model</span><span class="op">.</span><span class="nam">fit</span><span class="op">(</span><span class="nam">training_features</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"HMM model fitting complete."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">        <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="str">'monitor_'</span><span class="op">)</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">monitor_</span><span class="op">.</span><span class="nam">converged</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">HMM training did NOT converge after </span><span class="op">{</span><span class="nam">N_ITERATIONS</span><span class="op">}</span><span class="fst"> iterations. Consider increasing iterations or checking data.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">        <span class="key">elif</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="str">'monitor_'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">HMM training converged in </span><span class="op">{</span><span class="nam">model</span><span class="op">.</span><span class="nam">monitor_</span><span class="op">.</span><span class="nam">n_iter</span><span class="op">}</span><span class="fst"> iterations.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Convergence status not available in HMM model monitor."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">    <span class="key">except</span> <span class="nam">ValueError</span> <span class="key">as</span> <span class="nam">ve</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">        <span class="com"># Check for common HMM fitting errors like NaN/Inf input</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">        <span class="key">if</span> <span class="str">"contains NaN"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">ve</span><span class="op">)</span> <span class="key">or</span> <span class="str">"contains infinity"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">ve</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"HMM Fitting Error: Input features contain NaN or Infinity. Check feature calculation and source data quality."</span><span class="op">,</span> <span class="nam">exc_info</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">             <span class="nam">logging</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Value error fitting HMM model: </span><span class="op">{</span><span class="nam">ve</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">        <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="str">"HMM Training failed due to invalid input data."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">fit_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Unexpected error fitting HMM model: </span><span class="op">{</span><span class="nam">fit_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">        <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="str">"HMM Training failed."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">    <span class="com"># Ensure output directory exists</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Ensuring model output directory exists: </span><span class="op">{</span><span class="nam">MODEL_OUTPUT_DIR</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">    <span class="nam">os</span><span class="op">.</span><span class="nam">makedirs</span><span class="op">(</span><span class="nam">MODEL_OUTPUT_DIR</span><span class="op">,</span> <span class="nam">exist_ok</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">    <span class="com"># Save the trained model</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Saving trained model to: </span><span class="op">{</span><span class="nam">MODEL_OUTPUT_PATH</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">        <span class="nam">joblib</span><span class="op">.</span><span class="nam">dump</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">MODEL_OUTPUT_PATH</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Model saved successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">save_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error saving model: </span><span class="op">{</span><span class="nam">save_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="str">"Failed to save model."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">    <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"--- HMM Model Training Finished ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">    <span class="com"># --- Optional: Analyze Trained States ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">        <span class="key">if</span> <span class="nam">model</span> <span class="key">and</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="str">'means_'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="str">'covars_'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"\n--- Analyzing Trained HMM States ---"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">            <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="str">'monitor_'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">                 <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Model converged: </span><span class="op">{</span><span class="nam">model</span><span class="op">.</span><span class="nam">monitor_</span><span class="op">.</span><span class="nam">converged</span><span class="op">}</span><span class="fst"> in </span><span class="op">{</span><span class="nam">model</span><span class="op">.</span><span class="nam">monitor_</span><span class="op">.</span><span class="nam">n_iter</span><span class="op">}</span><span class="fst"> iterations.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Number of states: </span><span class="op">{</span><span class="nam">model</span><span class="op">.</span><span class="nam">n_components</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">            <span class="nam">state_properties</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"\nState Properties:"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"--------------------------------------------"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">            <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">model</span><span class="op">.</span><span class="nam">n_components</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">                <span class="nam">mean</span> <span class="op">=</span> <span class="nam">model</span><span class="op">.</span><span class="nam">means_</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span><span class="op">[</span><span class="num">0</span><span class="op">]</span> <span class="com"># Assumes single feature</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">                <span class="nam">variance</span> <span class="op">=</span> <span class="nam">model</span><span class="op">.</span><span class="nam">covars_</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span><span class="op">[</span><span class="num">0</span><span class="op">,</span> <span class="num">0</span><span class="op">]</span> <span class="key">if</span> <span class="nam">model</span><span class="op">.</span><span class="nam">covars_</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span><span class="op">.</span><span class="nam">ndim</span> <span class="op">==</span> <span class="num">2</span> <span class="key">else</span> <span class="nam">model</span><span class="op">.</span><span class="nam">covars_</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span><span class="op">[</span><span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">                <span class="nam">std_dev</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">sqrt</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">abs</span><span class="op">(</span><span class="nam">variance</span><span class="op">)</span><span class="op">)</span> <span class="com"># Use abs to prevent sqrt of small negative</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">                <span class="nam">state_properties</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">'index'</span><span class="op">:</span> <span class="nam">i</span><span class="op">,</span> <span class="str">'mean'</span><span class="op">:</span> <span class="nam">mean</span><span class="op">,</span> <span class="str">'variance'</span><span class="op">:</span> <span class="nam">variance</span><span class="op">,</span> <span class="str">'std_dev'</span><span class="op">:</span> <span class="nam">std_dev</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">                <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  State </span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">: Mean=</span><span class="op">{</span><span class="nam">mean</span><span class="op">:</span><span class="fst">.6f</span><span class="op">}</span><span class="fst">, Variance=</span><span class="op">{</span><span class="nam">variance</span><span class="op">:</span><span class="fst">.8f</span><span class="op">}</span><span class="fst">, StdDev=</span><span class="op">{</span><span class="nam">std_dev</span><span class="op">:</span><span class="fst">.6f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"--------------------------------------------"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"\nTransition Matrix (P[State_t+1=j | State_t=i]):"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">(</span><span class="nam">model</span><span class="op">.</span><span class="nam">transmat_</span><span class="op">,</span> <span class="nam">index</span><span class="op">=</span><span class="op">[</span><span class="fst">f"</span><span class="fst">From S</span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">"</span> <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">N_STATES</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">columns</span><span class="op">=</span><span class="op">[</span><span class="fst">f"</span><span class="fst">To S</span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">"</span> <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">N_STATES</span><span class="op">)</span><span class="op">]</span><span class="op">)</span><span class="op">.</span><span class="nam">round</span><span class="op">(</span><span class="num">4</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"\nInitial State Log Probabilities:"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="nam">model</span><span class="op">.</span><span class="nam">startprob_</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">            <span class="com"># --- Assigning Labels based on Analysis (EXAMPLE - Adjust based on results!) ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">            <span class="nam">state_labels</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">            <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">state_properties</span><span class="op">)</span> <span class="op">==</span> <span class="nam">N_STATES</span><span class="op">:</span> <span class="com"># Check if analysis succeeded</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">                <span class="nam">state_properties</span><span class="op">.</span><span class="nam">sort</span><span class="op">(</span><span class="nam">key</span><span class="op">=</span><span class="key">lambda</span> <span class="nam">x</span><span class="op">:</span> <span class="nam">x</span><span class="op">[</span><span class="str">'variance'</span><span class="op">]</span><span class="op">)</span> <span class="com"># Sort by variance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">                <span class="com"># Simple Example Labeling (assumes N_STATES=3, using log returns)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">                <span class="key">if</span> <span class="nam">N_STATES</span> <span class="op">==</span> <span class="num">3</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">                    <span class="com"># Lowest variance = Range/Low Vol</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">                    <span class="nam">state_labels</span><span class="op">[</span><span class="nam">state_properties</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"Low Vol Range"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">                    <span class="com"># Higher variance states - differentiate by mean (drift)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">                    <span class="nam">mid_vol_state</span> <span class="op">=</span> <span class="nam">state_properties</span><span class="op">[</span><span class="num">1</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">                    <span class="nam">high_vol_state</span> <span class="op">=</span> <span class="nam">state_properties</span><span class="op">[</span><span class="num">2</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">                    <span class="com"># Assign labels based on mean sign for higher vol states</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">                    <span class="nam">mean_threshold</span> <span class="op">=</span> <span class="num">1e-5</span> <span class="com"># Define a small threshold to consider drift significant</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">                    <span class="key">if</span> <span class="nam">mid_vol_state</span><span class="op">[</span><span class="str">'mean'</span><span class="op">]</span> <span class="op">></span> <span class="nam">mean_threshold</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">mid_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"Med Vol Uptrend"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">                    <span class="key">elif</span> <span class="nam">mid_vol_state</span><span class="op">[</span><span class="str">'mean'</span><span class="op">]</span> <span class="op">&lt;</span> <span class="op">-</span><span class="nam">mean_threshold</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">mid_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"Med Vol Downtrend"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">                    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">mid_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"Med Vol Range"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">                    <span class="key">if</span> <span class="nam">high_vol_state</span><span class="op">[</span><span class="str">'mean'</span><span class="op">]</span> <span class="op">></span> <span class="nam">mean_threshold</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">high_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"High Vol Uptrend"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">                    <span class="key">elif</span> <span class="nam">high_vol_state</span><span class="op">[</span><span class="str">'mean'</span><span class="op">]</span> <span class="op">&lt;</span> <span class="op">-</span><span class="nam">mean_threshold</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">high_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"High Vol Downtrend"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">                    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">                         <span class="nam">state_labels</span><span class="op">[</span><span class="nam">high_vol_state</span><span class="op">[</span><span class="str">'index'</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="str">"High Vol Range"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">            <span class="com"># Default labeling if N_STATES is different or labeling logic fails</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">            <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">N_STATES</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">                <span class="key">if</span> <span class="nam">i</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">state_labels</span><span class="op">:</span> <span class="nam">state_labels</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">State </span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"\nExample State Label Assignment (Interpret Carefully!):"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"------------------------------------------------------"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">            <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">N_STATES</span><span class="op">)</span><span class="op">:</span> <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  State Index </span><span class="op">{</span><span class="nam">i</span><span class="op">}</span><span class="fst"> --> Label: '</span><span class="op">{</span><span class="nam">state_labels</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">i</span><span class="op">)</span><span class="op">}</span><span class="fst">'</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"------------------------------------------------------"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"(NOTE: Labeling requires manual interpretation of Mean/StdDev values printed above)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">            <span class="com"># --------------------------------------------------------------------------</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">            <span class="nam">logging</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Could not analyze states (model attributes missing)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">analysis_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">        <span class="nam">logging</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error during state analysis: </span><span class="op">{</span><span class="nam">analysis_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_6696b9cca54af58b_model_monitoring_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_21758df60ac2cdd3_mt5_client_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:43 -0500
        </p>
    </div>
</footer>
</body>
</html>
