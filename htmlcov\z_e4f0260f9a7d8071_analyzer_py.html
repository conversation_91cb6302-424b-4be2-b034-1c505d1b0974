<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\forex_bot\performance_analyzer\analyzer.py: 14%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\forex_bot\performance_analyzer\analyzer.py</b>:
            <span class="pc_cov">14%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">225 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">32<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">193<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_e4f0260f9a7d8071___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_e4f0260f9a7d8071_metrics_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:27 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># type: ignore</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">csv</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">dataclasses</span> <span class="key">import</span> <span class="nam">dataclass</span><span class="op">,</span> <span class="nam">field</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Union</span> <span class="com"># Keep Union for Dict value type</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Union</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="nam">NumericArray</span> <span class="op">=</span> <span class="nam">Union</span><span class="op">[</span><span class="nam">np</span><span class="op">.</span><span class="nam">ndarray</span><span class="op">,</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">timedelta</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="nam">forex_bot</span><span class="op">.</span><span class="nam">performance_analyzer</span><span class="op">.</span><span class="nam">metrics</span> <span class="key">import</span> <span class="nam">calculate_consecutive_wins_losses</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">_np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">if</span> <span class="key">not</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">_np</span><span class="op">,</span> <span class="str">'NaN'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">_np</span><span class="op">.</span><span class="nam">NaN</span> <span class="op">=</span> <span class="nam">_np</span><span class="op">.</span><span class="nam">nan</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="com"># --- Import ALL required metric functions from the updated metrics.py ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">from</span> <span class="nam">forex_bot</span><span class="op">.</span><span class="nam">performance_analyzer</span><span class="op">.</span><span class="nam">metrics</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="nam">net_profit</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">total_return_pct</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">sharpe_ratio</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">max_drawdown</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">sortino_ratio</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">annual_volatility</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="nam">calculate_batting_avg</span><span class="op">,</span> <span class="com"># Use this name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">profit_factor</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">calculate_skewness</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">calculate_kurtosis</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">calculate_var</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">calculate_cvar</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">calculate_alpha_beta</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="nam">calculate_capture_ratios</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="nam">calculate_avg_win_loss</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">calculate_consecutive_wins_losses</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="nam">calculate_avg_trade_duration</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="nam">calculate_drawdown_recovery_times</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="com"># --- Import utility functions ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="com"># Assuming utils.py contains these functions</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="key">from</span> <span class="nam">forex_bot</span><span class="op">.</span><span class="nam">performance_analyzer</span><span class="op">.</span><span class="nam">utils</span> <span class="key">import</span> <span class="nam">equity_curve</span><span class="op">,</span> <span class="nam">get_benchmark_data</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">logging</span><span class="op">.</span><span class="nam">getLogger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span> <span class="com"># Use module-specific logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t"><span class="op">@</span><span class="nam">dataclass</span><span class="op">(</span><span class="nam">frozen</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="key">class</span> <span class="nam">AnalyzerConfig</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="str">"""Configuration for the Performance Analyzer."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="nam">initial_capital</span><span class="op">:</span> <span class="nam">float</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="com"># &lt;&lt;&lt; ADDED: Benchmark symbol (e.g., 'DX-Y.NYB', 'UUP', 'DTWEXBGS') >>></span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="nam">benchmark_symbol</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Optional benchmark ticker</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">risk_free_rate</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.0</span> <span class="com"># Annual risk-free rate</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">    <span class="nam">trading_days</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">252</span> <span class="com"># Trading days per year for annualization</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">    <span class="nam">var_confidence_level</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.95</span> <span class="com"># e.g., 95%</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="key">def</span> <span class="nam">__post_init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="com"># Validation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">initial_capital</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Initial capital must be positive."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">trading_days</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Trading days must be positive."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="num">0</span> <span class="op">&lt;</span> <span class="nam">self</span><span class="op">.</span><span class="nam">var_confidence_level</span> <span class="op">&lt;</span> <span class="num">1</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"VaR confidence level must be between 0 and 1 (exclusive)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">benchmark_symbol</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="key">raise</span> <span class="nam">TypeError</span><span class="op">(</span><span class="str">"Benchmark symbol must be a string or None."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">        <span class="com"># Basic check for empty string</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">        <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Benchmark symbol cannot be an empty string."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t"><span class="key">class</span> <span class="nam">PerformanceAnalyzer</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="str">    Analyzes trading performance based on a DataFrame of closed deals.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="str">    Requires 'exit_time' (datetime, UTC recommended) and 'profit' (float) columns.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">deals</span><span class="op">:</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">,</span> <span class="nam">config</span><span class="op">:</span> <span class="nam">AnalyzerConfig</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t"><span class="str">            deals: DataFrame of closed trades. Must contain 'exit_time', 'profit'.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="str">                   'exit_time' should ideally be timezone-aware UTC datetime objects.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t"><span class="str">            config: An instance of AnalyzerConfig.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">deals</span><span class="op">,</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">            <span class="key">raise</span> <span class="nam">TypeError</span><span class="op">(</span><span class="str">"Input 'deals' must be a pandas DataFrame."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="key">if</span> <span class="str">'exit_time'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">deals</span><span class="op">.</span><span class="nam">columns</span> <span class="key">or</span> <span class="str">'profit'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">deals</span><span class="op">.</span><span class="nam">columns</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">             <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Input 'deals' DataFrame must contain 'exit_time' and 'profit' columns."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="com"># Attempt to convert exit_time if not already datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">api</span><span class="op">.</span><span class="nam">types</span><span class="op">.</span><span class="nam">is_datetime64_any_dtype</span><span class="op">(</span><span class="nam">deals</span><span class="op">[</span><span class="str">'exit_time'</span><span class="op">]</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="nam">deals</span><span class="op">[</span><span class="str">'exit_time'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_datetime</span><span class="op">(</span><span class="nam">deals</span><span class="op">[</span><span class="str">'exit_time'</span><span class="op">]</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Converted 'exit_time' column to datetime (UTC)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed to convert 'exit_time' to datetime: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">. Ensure it's in a parsable format.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Could not parse 'exit_time' column."</span><span class="op">)</span> <span class="key">from</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">config</span><span class="op">,</span> <span class="nam">AnalyzerConfig</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="key">raise</span> <span class="nam">TypeError</span><span class="op">(</span><span class="str">"Input 'config' must be an instance of AnalyzerConfig."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="com"># Sort deals early to ensure chronological order for equity calculation etc.</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">deals</span> <span class="op">=</span> <span class="nam">deals</span><span class="op">.</span><span class="nam">sort_values</span><span class="op">(</span><span class="nam">by</span><span class="op">=</span><span class="str">'exit_time'</span><span class="op">)</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">config</span> <span class="op">=</span> <span class="nam">config</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Cache for equity curve</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Cache for benchmark data</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="com"># Log config details safely</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="nam">log_config</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">            <span class="str">"Initial Capital"</span><span class="op">:</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">initial_capital</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">            <span class="str">"Benchmark"</span><span class="op">:</span> <span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span> <span class="key">if</span> <span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span> <span class="key">else</span> <span class="str">"N/A"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">            <span class="str">"Risk-Free Rate"</span><span class="op">:</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">risk_free_rate</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span> <span class="com"># Show more precision for RF rate</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">            <span class="str">"Trading Days"</span><span class="op">:</span> <span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="str">"VaR Confidence"</span><span class="op">:</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">config</span><span class="op">.</span><span class="nam">var_confidence_level</span><span class="op">:</span><span class="fst">.1%</span><span class="op">}</span><span class="fst">"</span> <span class="com"># Format confidence level</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">PerformanceAnalyzer initialized with </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span><span class="op">}</span><span class="fst"> deals.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Analyzer Config: </span><span class="op">{</span><span class="nam">log_config</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">    <span class="key">def</span> <span class="nam">compute_equity</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="str">"""Computes (or retrieves cached) equity curve. Deals are assumed pre-sorted."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">,</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Input 'deals' must be a pandas DataFrame."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">(</span><span class="op">[</span><span class="op">]</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">float</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"equity"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">                <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Computing equity curve..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">            <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">                 <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"No deals available to compute equity curve."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">                 <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">(</span><span class="op">[</span><span class="op">]</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">float</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"equity"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">                <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">                    <span class="com"># Deals are sorted in __init__</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span> <span class="op">=</span> <span class="nam">equity_curve</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">                        <span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">initial_capital</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">                    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">                    <span class="com"># Ensure index is datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">                    <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span><span class="op">.</span><span class="nam">index</span><span class="op">,</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DatetimeIndex</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">                         <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Equity curve index is not DatetimeIndex. Converting."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">                         <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span><span class="op">.</span><span class="nam">index</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_datetime</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span><span class="op">.</span><span class="nam">index</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">                <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">                    <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error computing equity curve."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">(</span><span class="op">[</span><span class="op">]</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">float</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">"equity"</span><span class="op">)</span> <span class="com"># Return empty on error</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_equity</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_benchmark</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">pd</span><span class="op">.</span><span class="nam">Series</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="str">"""Fetches (or retrieves cached) benchmark price data if configured."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span> <span class="com"># Check cache first (even if None)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">            <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"No benchmark symbol configured."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Cache None result</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="nam">ec</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">compute_equity</span><span class="op">(</span><span class="op">)</span> <span class="com"># Need equity curve for date range</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="key">if</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">empty</span> <span class="key">or</span> <span class="nam">len</span><span class="op">(</span><span class="nam">ec</span><span class="op">)</span> <span class="op">&lt;</span> <span class="num">2</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Equity curve is empty or too short; cannot determine date range for benchmark."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="nam">start_date</span> <span class="op">=</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="nam">end_date</span> <span class="op">=</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Fetching benchmark '</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">}</span><span class="fst">' data from </span><span class="op">{</span><span class="nam">start_date</span><span class="op">.</span><span class="nam">date</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst"> to </span><span class="op">{</span><span class="nam">end_date</span><span class="op">.</span><span class="nam">date</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">            <span class="com"># Assumes get_benchmark_data handles fetching &amp; returns Series[Price] indexed by Datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">            <span class="nam">benchmark_data</span> <span class="op">=</span> <span class="nam">get_benchmark_data</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">,</span> <span class="nam">start_date</span><span class="op">,</span> <span class="nam">end_date</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="key">if</span> <span class="nam">benchmark_data</span> <span class="key">is</span> <span class="key">None</span> <span class="key">or</span> <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">No data returned for benchmark '</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">}</span><span class="fst">'.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">                <span class="com"># Ensure index is datetime and timezone-aware (preferably UTC for consistency)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">                <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">,</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DatetimeIndex</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">                     <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Benchmark data index is not DatetimeIndex. Converting."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">                     <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">to_datetime</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">,</span> <span class="nam">utc</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">                <span class="key">elif</span> <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">                     <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Benchmark data index is timezone-naive. Localizing to UTC."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">                     <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span> <span class="op">=</span> <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz_localize</span><span class="op">(</span><span class="str">'UTC'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">                <span class="key">elif</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz</span><span class="op">,</span> <span class="str">'zone'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz</span><span class="op">.</span><span class="nam">zone</span> <span class="op">!=</span> <span class="str">'UTC'</span> <span class="key">or</span> <span class="op">(</span><span class="key">not</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz</span><span class="op">,</span> <span class="str">'zone'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">str</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tzinfo</span><span class="op">)</span> <span class="op">!=</span> <span class="str">'UTC'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">                     <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Benchmark data index has timezone </span><span class="op">{</span><span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz</span><span class="op">}</span><span class="fst">. Converting to UTC.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">                     <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span> <span class="op">=</span> <span class="nam">benchmark_data</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">tz_convert</span><span class="op">(</span><span class="str">'UTC'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Successfully fetched </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">benchmark_data</span><span class="op">)</span><span class="op">}</span><span class="fst"> data points for benchmark.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="op">=</span> <span class="nam">benchmark_data</span> <span class="com"># Cache successful result</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">                <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error fetching benchmark data for '</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">}</span><span class="fst">'.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span> <span class="op">=</span> <span class="key">None</span> <span class="com"># Cache failure</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_benchmark_prices</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">    <span class="com"># Refined type hint: Result values are expected to be floats or None/NaN represented as float</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="key">def</span> <span class="nam">run_all</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Union</span><span class="op">[</span><span class="nam">float</span><span class="op">,</span> <span class="nam">str</span><span class="op">,</span> <span class="key">None</span><span class="op">,</span> <span class="nam">timedelta</span><span class="op">]</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">        <span class="str">"""Computes and returns all core performance metrics including enhancements."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Running all performance calculations..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">        <span class="nam">var_key</span><span class="op">=</span><span class="fst">f"</span><span class="fst">var_</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">var_confidence_level</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">pct</span><span class="fst">"</span><span class="op">;</span> <span class="nam">cvar_key</span><span class="op">=</span><span class="fst">f"</span><span class="fst">cvar_</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">var_confidence_level</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">pct</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="com"># Initialize results dict with defaults for ALL metrics</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="nam">results</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">            <span class="str">"net_profit"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span> <span class="str">"total_return_pct"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span> <span class="str">"batting_avg_pct"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="str">"profit_factor"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span> <span class="str">"avg_win"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"avg_loss"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="str">"avg_win_loss_ratio"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"max_consecutive_wins"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span> <span class="str">"max_consecutive_losses"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="str">"avg_trade_duration"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span> <span class="str">"avg_peak_recovery_time"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span> <span class="str">"max_peak_recovery_time"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">             <span class="str">"avg_trough_recovery_time"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span> <span class="str">"max_trough_recovery_time"</span><span class="op">:</span> <span class="key">None</span><span class="op">,</span> <span class="com"># Added trough recovery</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">            <span class="str">"annual_volatility_pct"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"sharpe_ratio"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"sortino_ratio"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">            <span class="str">"max_drawdown_pct"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"skewness"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"kurtosis"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="nam">var_key</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="nam">cvar_key</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"alpha"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"beta"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">            <span class="str">"upside_capture_pct"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span><span class="op">,</span> <span class="str">"downside_capture_pct"</span><span class="op">:</span> <span class="nam">np</span><span class="op">.</span><span class="nam">nan</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"No deals provided."</span><span class="op">)</span><span class="op">;</span> <span class="key">return</span> <span class="nam">results</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">        <span class="com"># Calculate Deal-Based Metrics</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"net_profit"</span><span class="op">]</span><span class="op">=</span><span class="nam">net_profit</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"total_return_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">total_return_pct</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">initial_capital</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"batting_avg_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">calculate_batting_avg</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span> <span class="com"># Use the imported name</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">            <span class="com"># --- End Fix ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"profit_factor"</span><span class="op">]</span><span class="op">=</span><span class="nam">profit_factor</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">            <span class="nam">avg_w</span><span class="op">,</span> <span class="nam">avg_l</span><span class="op">,</span> <span class="nam">ratio_wl</span> <span class="op">=</span> <span class="nam">calculate_avg_win_loss</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"avg_win"</span><span class="op">]</span><span class="op">=</span><span class="nam">avg_w</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"avg_loss"</span><span class="op">]</span><span class="op">=</span><span class="nam">avg_l</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"avg_win_loss_ratio"</span><span class="op">]</span><span class="op">=</span><span class="nam">ratio_wl</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">            <span class="nam">max_w</span><span class="op">,</span> <span class="nam">max_l</span> <span class="op">=</span> <span class="nam">calculate_consecutive_wins_losses</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"max_consecutive_wins"</span><span class="op">]</span><span class="op">=</span><span class="nam">max_w</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"max_consecutive_losses"</span><span class="op">]</span><span class="op">=</span><span class="nam">max_l</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">            <span class="nam">results</span><span class="op">[</span><span class="str">"avg_trade_duration"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">calculate_avg_trade_duration</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">deals</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Deal metrics error: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">        <span class="com"># Calculate Equity Curve &amp; Return-Based Metrics</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">        <span class="nam">ec</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">compute_equity</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">        <span class="key">if</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">empty</span> <span class="key">or</span> <span class="nam">len</span><span class="op">(</span><span class="nam">ec</span><span class="op">)</span> <span class="op">&lt;</span> <span class="num">2</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"Equity curve empty/short."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Calculating return-based metrics...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">            <span class="com"># Volatility, Sharpe, Sortino, MDD, Skew, Kurtosis, VaR, CVaR</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">                <span class="nam">results</span><span class="op">[</span><span class="str">"annual_volatility_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">annual_volatility</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: annual_volatility"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="str">"sharpe_ratio"</span><span class="op">]</span><span class="op">=</span><span class="nam">sharpe_ratio</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">risk_free_rate</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: sharpe_ratio"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="str">"sortino_ratio"</span><span class="op">]</span><span class="op">=</span><span class="nam">sortino_ratio</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">risk_free_rate</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: sortino_ratio"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="str">"max_drawdown_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">max_drawdown</span><span class="op">(</span><span class="nam">ec</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: max_drawdown"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="str">"skewness"</span><span class="op">]</span><span class="op">=</span><span class="nam">calculate_skewness</span><span class="op">(</span><span class="nam">ec</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: skewness"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="str">"kurtosis"</span><span class="op">]</span><span class="op">=</span><span class="nam">calculate_kurtosis</span><span class="op">(</span><span class="nam">ec</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: kurtosis"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="nam">var_key</span><span class="op">]</span><span class="op">=</span><span class="nam">calculate_var</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">var_confidence_level</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error: </span><span class="op">{</span><span class="nam">var_key</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="nam">results</span><span class="op">[</span><span class="nam">cvar_key</span><span class="op">]</span><span class="op">=</span><span class="nam">calculate_cvar</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">var_confidence_level</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error: </span><span class="op">{</span><span class="nam">cvar_key</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">            <span class="com"># Drawdown Recovery Metrics</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">                 <span class="com"># --- FIX: Use correct function name ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">                <span class="nam">peak_rec_times</span><span class="op">,</span> <span class="nam">trough_rec_times</span> <span class="op">=</span> <span class="nam">calculate_drawdown_recovery_times</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">min_drawdown_threshold_pct</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">                 <span class="com"># --- End Fix ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">                <span class="key">if</span> <span class="nam">peak_rec_times</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">                    <span class="nam">results</span><span class="op">[</span><span class="str">"avg_peak_recovery_time"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="nam">peak_rec_times</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">                    <span class="nam">results</span><span class="op">[</span><span class="str">"max_peak_recovery_time"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="nam">peak_rec_times</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">                <span class="key">if</span> <span class="nam">trough_rec_times</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">                    <span class="nam">results</span><span class="op">[</span><span class="str">"avg_trough_recovery_time"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="nam">trough_rec_times</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">                    <span class="nam">results</span><span class="op">[</span><span class="str">"max_trough_recovery_time"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="nam">trough_rec_times</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error calculating recovery times"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">            <span class="com"># Benchmark Metrics</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">            <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">,</span> <span class="str">'benchmark_symbol'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">:</span> <span class="com"># Check if attribute exists</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Fetching benchmark '</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">}</span><span class="fst">' data...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">                <span class="nam">benchmark_prices</span> <span class="op">=</span> <span class="nam">get_benchmark_data</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">,</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">ec</span><span class="op">.</span><span class="nam">index</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">                <span class="key">if</span> <span class="nam">benchmark_prices</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">benchmark_prices</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">                    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Calculating Alpha, Beta, Capture Ratios..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">                    <span class="key">try</span><span class="op">:</span> <span class="nam">alpha</span><span class="op">,</span> <span class="nam">beta</span> <span class="op">=</span> <span class="nam">calculate_alpha_beta</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">benchmark_prices</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">risk_free_rate</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">)</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"alpha"</span><span class="op">]</span><span class="op">=</span><span class="nam">alpha</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"beta"</span><span class="op">]</span><span class="op">=</span><span class="nam">beta</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">                    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: alpha/beta"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">                    <span class="key">try</span><span class="op">:</span> <span class="nam">up</span><span class="op">,</span> <span class="nam">down</span> <span class="op">=</span> <span class="nam">calculate_capture_ratios</span><span class="op">(</span><span class="nam">ec</span><span class="op">,</span> <span class="nam">benchmark_prices</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">trading_days</span><span class="op">)</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"upside_capture_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">up</span><span class="op">;</span> <span class="nam">results</span><span class="op">[</span><span class="str">"downside_capture_pct"</span><span class="op">]</span><span class="op">=</span><span class="nam">down</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">                    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="str">"Error: capture ratios"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">                <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Benchmark data unavailable for '</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">config</span><span class="op">.</span><span class="nam">benchmark_symbol</span><span class="op">}</span><span class="fst">'.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">            <span class="key">else</span><span class="op">:</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"No benchmark symbol configured."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">        <span class="com"># --- Final Formatting &amp; Return ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">        <span class="nam">log_display_results</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">        <span class="key">for</span> <span class="nam">k</span><span class="op">,</span> <span class="nam">v</span> <span class="key">in</span> <span class="nam">results</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">             <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">v</span><span class="op">,</span> <span class="nam">timedelta</span><span class="op">)</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="com"># Format timedelta</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">             <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">v</span><span class="op">,</span> <span class="op">(</span><span class="nam">float</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">floating</span><span class="op">)</span><span class="op">)</span> <span class="key">and</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isfinite</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">v</span><span class="op">:</span><span class="fst">.4f</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">             <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">v</span><span class="op">,</span> <span class="op">(</span><span class="nam">int</span><span class="op">,</span> <span class="nam">np</span><span class="op">.</span><span class="nam">integer</span><span class="op">)</span><span class="op">)</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="com"># Format ints</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">             <span class="key">elif</span> <span class="nam">v</span> <span class="key">is</span> <span class="nam">np</span><span class="op">.</span><span class="nam">inf</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="str">"+inf"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">             <span class="key">elif</span> <span class="nam">v</span> <span class="op">==</span> <span class="op">-</span><span class="nam">np</span><span class="op">.</span><span class="nam">inf</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="str">"-inf"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">             <span class="key">elif</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">isna</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="key">or</span> <span class="nam">v</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="str">"N/A"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">             <span class="key">else</span><span class="op">:</span> <span class="nam">log_display_results</span><span class="op">[</span><span class="nam">k</span><span class="op">]</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="com"># Fallback</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Formatted Metrics: </span><span class="op">{</span><span class="nam">log_display_results</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">        <span class="key">return</span> <span class="nam">results</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t"><span class="com"># Function moved from metatrader.py</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t"><span class="key">def</span> <span class="nam">get_recent_performance_summary</span><span class="op">(</span><span class="nam">symbol</span><span class="op">,</span> <span class="nam">adapter</span><span class="op">,</span> <span class="nam">num_trades</span><span class="op">=</span><span class="num">5</span><span class="op">,</span> <span class="nam">perf_log_filename</span><span class="op">=</span><span class="str">'trading_performance.log'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t"><span class="str">    Reads the performance log and returns a summary string for the prompt. More robust file handling.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t"><span class="str">        symbol (str): Trading symbol to filter for</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t"><span class="str">        adapter (logging.LoggerAdapter): Logger adapter instance</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t"><span class="str">        num_trades (int): Number of recent trades to include in summary</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t"><span class="str">        perf_log_filename (str): Path to performance log file</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t"><span class="str">        str: Formatted summary of recent trades</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">    <span class="nam">summary</span> <span class="op">=</span> <span class="str">"No recent P/L data."</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Getting performance summary for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> (last </span><span class="op">{</span><span class="nam">num_trades</span><span class="op">}</span><span class="fst">)...</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">perf_log_filename</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Performance log file not found: </span><span class="op">{</span><span class="nam">perf_log_filename</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">        <span class="key">return</span> <span class="nam">summary</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">    <span class="nam">relevant_trades_data</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span> <span class="com"># Store tuples of (type, profit_float)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">        <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">perf_log_filename</span><span class="op">,</span> <span class="str">'r'</span><span class="op">,</span> <span class="nam">encoding</span><span class="op">=</span><span class="str">'utf-8'</span><span class="op">,</span> <span class="nam">newline</span><span class="op">=</span><span class="str">''</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span> <span class="com"># Add newline='' for csv reader</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">            <span class="nam">reader</span> <span class="op">=</span> <span class="nam">csv</span><span class="op">.</span><span class="nam">reader</span><span class="op">(</span><span class="nam">f</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">            <span class="nam">header</span> <span class="op">=</span> <span class="nam">next</span><span class="op">(</span><span class="nam">reader</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">header</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Performance log is empty or header missing: </span><span class="op">{</span><span class="nam">perf_log_filename</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">                <span class="key">return</span> <span class="nam">summary</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">            <span class="key">try</span><span class="op">:</span> <span class="com"># Find indices</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">                <span class="nam">symbol_idx</span> <span class="op">=</span> <span class="nam">header</span><span class="op">.</span><span class="nam">index</span><span class="op">(</span><span class="str">'Symbol'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">                <span class="nam">type_idx</span> <span class="op">=</span> <span class="nam">header</span><span class="op">.</span><span class="nam">index</span><span class="op">(</span><span class="str">'Type'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">                <span class="nam">profit_idx</span> <span class="op">=</span> <span class="nam">header</span><span class="op">.</span><span class="nam">index</span><span class="op">(</span><span class="str">'Profit'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">            <span class="key">except</span> <span class="nam">ValueError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">                <span class="nam">adapter</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Perf log header missing required columns in </span><span class="op">{</span><span class="nam">perf_log_filename</span><span class="op">}</span><span class="fst">.</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">                <span class="key">return</span> <span class="str">"P/L Log format error."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">            <span class="com"># Read line by line, keeping only the latest N matching the symbol</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">            <span class="key">for</span> <span class="nam">row</span> <span class="key">in</span> <span class="nam">reader</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">                <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">                    <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">row</span><span class="op">)</span> <span class="op">></span> <span class="nam">max</span><span class="op">(</span><span class="nam">symbol_idx</span><span class="op">,</span> <span class="nam">profit_idx</span><span class="op">)</span> <span class="key">and</span> <span class="nam">row</span><span class="op">[</span><span class="nam">symbol_idx</span><span class="op">]</span> <span class="op">==</span> <span class="nam">symbol</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">                        <span class="nam">trade_type</span> <span class="op">=</span> <span class="nam">row</span><span class="op">[</span><span class="nam">type_idx</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">                        <span class="nam">profit_str</span> <span class="op">=</span> <span class="nam">row</span><span class="op">[</span><span class="nam">profit_idx</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">                        <span class="nam">profit_float</span> <span class="op">=</span> <span class="nam">float</span><span class="op">(</span><span class="nam">profit_str</span><span class="op">)</span> <span class="com"># Convert profit early</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">                        <span class="nam">relevant_trades_data</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">(</span><span class="nam">trade_type</span><span class="op">,</span> <span class="nam">profit_float</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">                        <span class="com"># Keep only the last 'num_trades' items</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">                        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">relevant_trades_data</span><span class="op">)</span> <span class="op">></span> <span class="nam">num_trades</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t">                            <span class="nam">relevant_trades_data</span><span class="op">.</span><span class="nam">pop</span><span class="op">(</span><span class="num">0</span><span class="op">)</span> <span class="com"># Remove the oldest if list exceeds limit</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t">                <span class="key">except</span> <span class="op">(</span><span class="nam">IndexError</span><span class="op">,</span> <span class="nam">ValueError</span><span class="op">)</span> <span class="key">as</span> <span class="nam">row_err</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t">                     <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Skipping malformed row in perf log: </span><span class="op">{</span><span class="nam">row</span><span class="op">}</span><span class="fst">. Error: </span><span class="op">{</span><span class="nam">row_err</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">                     <span class="key">continue</span> <span class="com"># Skip this row</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">        <span class="com"># Format the results after the file is closed</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">        <span class="key">if</span> <span class="nam">relevant_trades_data</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">            <span class="nam">perf_details</span> <span class="op">=</span> <span class="op">[</span><span class="fst">f"</span><span class="op">{</span><span class="nam">trade_type</span><span class="op">}</span><span class="fst">:</span><span class="op">{</span><span class="nam">profit_float</span><span class="op">:</span><span class="fst">+.2f</span><span class="op">}</span><span class="fst">"</span> <span class="key">for</span> <span class="nam">trade_type</span><span class="op">,</span> <span class="nam">profit_float</span> <span class="key">in</span> <span class="nam">relevant_trades_data</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">            <span class="key">if</span> <span class="nam">perf_details</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">                <span class="nam">summary</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">Recent </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst"> P/L: </span><span class="op">{</span><span class="str">', '</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">perf_details</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">    <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">         <span class="nam">adapter</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Perf log FNF (race condition?): </span><span class="op">{</span><span class="nam">perf_log_filename</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">         <span class="key">return</span> <span class="nam">summary</span> <span class="com"># File disappeared between check and open</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">        <span class="nam">adapter</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Error reading/processing performance summary log: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">        <span class="key">return</span> <span class="str">"Error reading P/L log."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">    <span class="nam">adapter</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Performance summary for </span><span class="op">{</span><span class="nam">symbol</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">summary</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">    <span class="key">return</span> <span class="nam">summary</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_e4f0260f9a7d8071___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_e4f0260f9a7d8071_metrics_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.1">coverage.py v7.8.1</a>,
            created at 2025-05-27 22:27 -0500
        </p>
    </div>
</footer>
</body>
</html>
