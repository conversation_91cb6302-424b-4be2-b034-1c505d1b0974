"""
Simple test coverage for config_loader.py - Batch 11
Target: Push from 87% to 90%+ coverage
Focus: Testing actual Config class and functions
"""

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock


class TestConfigLoaderSimple:
    """Test class for config_loader.py simple coverage."""

    def test_config_initialization(self):
        """Test Config class initialization."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                # Test default values
                assert config.log_filename == 'trading_bot.log'
                assert config.perf_log_filename == 'trading_performance.log'
                assert config.volume == 0.01
                assert config.risk_reward_ratio == 3.0
                assert config.dry_run == True

    def test_config_get_env_int_valid(self):
        """Test Config._get_env_int with valid values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                with patch.dict(os.environ, {'TEST_INT': '42'}):
                    result = config._get_env_int('TEST_INT', 10)
                    assert result == 42

    def test_config_get_env_int_invalid(self):
        """Test Config._get_env_int with invalid values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                with patch.dict(os.environ, {'TEST_INT': 'invalid'}):
                    result = config._get_env_int('TEST_INT', 10)
                    assert result == 10  # Should return default

    def test_config_get_env_int_missing(self):
        """Test Config._get_env_int with missing values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                result = config._get_env_int('MISSING_INT', 20)
                assert result == 20  # Should return default

    def test_config_get_env_float_valid(self):
        """Test Config._get_env_float with valid values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                with patch.dict(os.environ, {'TEST_FLOAT': '3.14'}):
                    result = config._get_env_float('TEST_FLOAT', 1.0)
                    assert result == 3.14

    def test_config_get_env_float_invalid(self):
        """Test Config._get_env_float with invalid values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                with patch.dict(os.environ, {'TEST_FLOAT': 'invalid'}):
                    result = config._get_env_float('TEST_FLOAT', 2.5)
                    assert result == 2.5  # Should return default

    def test_config_get_env_float_missing(self):
        """Test Config._get_env_float with missing values."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                result = config._get_env_float('MISSING_FLOAT', 5.0)
                assert result == 5.0  # Should return default

    def test_config_calculate_paths(self):
        """Test Config._calculate_paths method."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch('os.path.exists', return_value=True):
                    config = Config()
                    
                    # Should have calculated paths
                    assert config.knowledge_base_strategy_file_path is not None
                    assert config.knowledge_base_metrics_file_path is not None

    def test_config_load_from_env_symbols(self):
        """Test Config._load_from_env with symbols parsing."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {'SYMBOLS': 'EURUSD,GBPUSD,USDJPY'}):
                    config = Config()
                    
                    expected_symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
                    assert config.symbols == expected_symbols

    def test_config_load_from_env_symbols_invalid(self):
        """Test Config._load_from_env with invalid symbols."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {'SYMBOLS': 'invalid,format,'}):
                    config = Config()
                    
                    # Should handle gracefully and use some symbols
                    assert isinstance(config.symbols, list)

    def test_config_dry_run_flag(self):
        """Test Config dry_run flag parsing."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                # Test True
                with patch.dict(os.environ, {'DRY_RUN': 'true'}):
                    config = Config()
                    assert config.dry_run == True
                
                # Test False
                with patch.dict(os.environ, {'DRY_RUN': 'false'}):
                    config = Config()
                    assert config.dry_run == False

    def test_get_config_singleton(self):
        """Test get_config singleton behavior."""
        from src.forex_bot.config_loader import get_config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config1 = get_config()
                config2 = get_config()
                
                # Should be the same instance
                assert config1 is config2

    def test_load_env_variables_function(self):
        """Test load_env_variables function."""
        from src.forex_bot.config_loader import load_env_variables
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch('src.forex_bot.config_loader.get_config') as mock_get_config:
                    mock_config = MagicMock()
                    mock_config.mt5_login = 12345
                    mock_config.mt5_password = "password"
                    mock_config.mt5_server = "server"
                    mock_config.mt5_path = "path"
                    mock_config.gemini_api_key = "api_key"
                    mock_config.gemini_model_name = "model"
                    mock_config.jb_news_api_key = "news_key"
                    mock_get_config.return_value = mock_config
                    
                    result = load_env_variables()
                    
                    assert len(result) == 7
                    assert result[0] == 12345  # mt5_login

    def test_load_env_variables_missing_credentials(self):
        """Test load_env_variables with missing credentials."""
        from src.forex_bot.config_loader import load_env_variables
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch('src.forex_bot.config_loader.get_config') as mock_get_config:
                    mock_config = MagicMock()
                    mock_config.mt5_login = None  # Missing
                    mock_config.mt5_password = "password"
                    mock_config.mt5_server = "server"
                    mock_config.gemini_api_key = "api_key"
                    mock_config.jb_news_api_key = "news_key"
                    mock_get_config.return_value = mock_config
                    
                    result = load_env_variables()
                    
                    # Should return tuple of None values
                    assert result == (None,) * 7

    def test_config_check_dependencies(self):
        """Test Config._check_dependencies method."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                config = Config()
                
                # Should have set dependency flags
                assert hasattr(config, 'rclone_exe_available')
                assert hasattr(config, 'qdrant_client_available')
                assert hasattr(config, 'sentence_transformers_available')
                assert hasattr(config, 'qdrant_available')
                assert hasattr(config, 'gemini_available')

    def test_config_feature_flags(self):
        """Test Config feature flags."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'ENABLE_TREND_ANALYSIS': 'false',
                    'ENABLE_CANDLESTICK_PATTERNS': 'true',
                    'ENABLE_SESSION_CONTEXT': 'false',
                    'ENABLE_ADAPTIVE_SLTP': 'true'
                }):
                    config = Config()
                    
                    assert config.enable_trend_analysis == False
                    assert config.enable_candlestick_patterns == True
                    assert config.enable_session_context == False
                    assert config.enable_adaptive_sltp == True

    def test_config_trading_parameters(self):
        """Test Config trading parameters from environment."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'VOLUME': '0.05',
                    'RISK_REWARD_RATIO': '2.5',
                    'ATR_PERIOD': '20',
                    'ATR_MULTIPLIER': '2.0'
                }):
                    config = Config()
                    
                    assert config.volume == 0.05
                    assert config.risk_reward_ratio == 2.5
                    assert config.atr_period == 20
                    assert config.atr_multiplier == 2.0

    def test_config_knowledge_base_settings(self):
        """Test Config knowledge base settings."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'QDRANT_URL': 'http://localhost:6333',
                    'QDRANT_API_KEY': 'test_key',
                    'QDRANT_STRATEGY_COLLECTION': 'TestStrategy',
                    'QDRANT_METRICS_COLLECTION': 'TestMetrics',
                    'EMBEDDING_MODEL_NAME': 'test-model'
                }):
                    config = Config()
                    
                    assert config.qdrant_url == 'http://localhost:6333'
                    assert config.qdrant_api_key == 'test_key'
                    assert config.qdrant_strategy_collection == 'TestStrategy'
                    assert config.qdrant_metrics_collection == 'TestMetrics'
                    assert config.embedding_model_name == 'test-model'

    def test_config_api_keys(self):
        """Test Config API keys loading."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'GEMINI_API_KEY': 'gemini_test_key',
                    'GEMINI_MODEL_NAME': 'gemini-test-model',
                    'JB_NEWS_API_KEY': 'news_test_key'
                }):
                    config = Config()
                    
                    assert config.gemini_api_key == 'gemini_test_key'
                    assert config.gemini_model_name == 'gemini-test-model'
                    assert config.jb_news_api_key == 'news_test_key'

    def test_config_mt5_credentials(self):
        """Test Config MT5 credentials loading."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'MT5_LOGIN': '12345',
                    'MT5_PASSWORD': 'test_password',
                    'MT5_SERVER': 'test_server',
                    'MT5_PATH': '/path/to/mt5'
                }):
                    config = Config()
                    
                    assert config.mt5_login == 12345
                    assert config.mt5_password == 'test_password'
                    assert config.mt5_server == 'test_server'
                    assert config.mt5_path == '/path/to/mt5'

    def test_config_rclone_settings(self):
        """Test Config rclone settings."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'RCLONE_REMOTE_NAME': 'test_remote',
                    'RCLONE_TARGET_FOLDER': 'test_folder',
                    'LOG_UPLOAD_INTERVAL_MINUTES': '10'
                }):
                    config = Config()
                    
                    assert config.rclone_remote_name == 'test_remote'
                    assert config.rclone_target_folder == 'test_folder'
                    assert config.log_upload_interval_minutes == 10

    def test_config_analysis_settings(self):
        """Test Config analysis settings."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch.dict(os.environ, {
                    'INITIAL_CAPITAL': '50000.0'
                }):
                    config = Config()
                    
                    assert config.initial_capital == 50000.0

    def test_config_path_calculation_fallback(self):
        """Test Config path calculation fallback behavior."""
        from src.forex_bot.config_loader import Config
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                with patch('os.path.exists', return_value=False):  # No .env or .git found
                    with patch('os.getcwd', return_value='/fallback/path'):
                        config = Config()
                        
                        # Should use fallback path
                        assert '/fallback/path' in config.knowledge_base_strategy_file_path
                        assert '/fallback/path' in config.knowledge_base_metrics_file_path
