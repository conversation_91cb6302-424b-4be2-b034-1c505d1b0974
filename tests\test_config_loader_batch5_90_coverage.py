"""
Comprehensive test coverage for config_loader.py - Batch 5
Target: Push from 87% to 90%+ coverage (QUICK WIN - only 3% needed!)
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import os
import tempfile
import json
import yaml
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path


class TestConfigLoaderBatch5Coverage:
    """Test class for config_loader.py comprehensive coverage."""

    @pytest.fixture
    def temp_config_file(self):
        """Create a temporary config file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "api_key": "test_key",
                "base_url": "https://api.test.com",
                "timeout": 30,
                "retries": 3
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

    @pytest.fixture
    def temp_yaml_config_file(self):
        """Create a temporary YAML config file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_data = {
                "database": {
                    "host": "localhost",
                    "port": 5432,
                    "name": "test_db"
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            }
            yaml.dump(config_data, f)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

    def test_load_config_json_file(self, temp_config_file):
        """Test loading JSON config file."""
        from src.forex_bot.config_loader import load_config
        
        config = load_config(temp_config_file)
        
        assert config["api_key"] == "test_key"
        assert config["base_url"] == "https://api.test.com"
        assert config["timeout"] == 30
        assert config["retries"] == 3

    def test_load_config_yaml_file(self, temp_yaml_config_file):
        """Test loading YAML config file."""
        from src.forex_bot.config_loader import load_config
        
        config = load_config(temp_yaml_config_file)
        
        assert config["database"]["host"] == "localhost"
        assert config["database"]["port"] == 5432
        assert config["logging"]["level"] == "INFO"

    def test_load_config_file_not_found(self):
        """Test loading config file that doesn't exist."""
        from src.forex_bot.config_loader import load_config
        
        with pytest.raises(FileNotFoundError):
            load_config("nonexistent_file.json")

    def test_load_config_invalid_json(self):
        """Test loading invalid JSON config file."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json content}')  # Invalid JSON
            temp_path = f.name
        
        try:
            with pytest.raises(json.JSONDecodeError):
                load_config(temp_path)
        finally:
            os.unlink(temp_path)

    def test_load_config_invalid_yaml(self):
        """Test loading invalid YAML config file."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write('invalid: yaml: content: [')  # Invalid YAML
            temp_path = f.name
        
        try:
            with pytest.raises(yaml.YAMLError):
                load_config(temp_path)
        finally:
            os.unlink(temp_path)

    def test_load_config_unsupported_extension(self):
        """Test loading config file with unsupported extension."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write('some text content')
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError, match="Unsupported file format"):
                load_config(temp_path)
        finally:
            os.unlink(temp_path)

    def test_load_config_empty_file(self):
        """Test loading empty config file."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('')  # Empty file
            temp_path = f.name
        
        try:
            with pytest.raises(json.JSONDecodeError):
                load_config(temp_path)
        finally:
            os.unlink(temp_path)

    def test_load_config_permission_denied(self):
        """Test loading config file with permission denied."""
        from src.forex_bot.config_loader import load_config
        
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            with pytest.raises(PermissionError):
                load_config("test_config.json")

    def test_load_config_with_environment_variables(self, temp_config_file):
        """Test loading config with environment variable substitution."""
        from src.forex_bot.config_loader import load_config
        
        # Create config with environment variable placeholder
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "api_key": "${API_KEY}",
                "timeout": 30
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            with patch.dict(os.environ, {'API_KEY': 'env_test_key'}):
                config = load_config(temp_path)
                # Note: This test assumes environment variable substitution is implemented
                # If not implemented, the test will verify current behavior
                assert "api_key" in config
        finally:
            os.unlink(temp_path)

    def test_load_config_nested_structure(self):
        """Test loading config with deeply nested structure."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "level1": {
                    "level2": {
                        "level3": {
                            "level4": {
                                "value": "deep_value"
                            }
                        }
                    }
                }
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["level1"]["level2"]["level3"]["level4"]["value"] == "deep_value"
        finally:
            os.unlink(temp_path)

    def test_load_config_large_file(self):
        """Test loading large config file."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            # Create a large config with many entries
            config_data = {f"key_{i}": f"value_{i}" for i in range(1000)}
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert len(config) == 1000
            assert config["key_0"] == "value_0"
            assert config["key_999"] == "value_999"
        finally:
            os.unlink(temp_path)

    def test_load_config_special_characters(self):
        """Test loading config with special characters."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "unicode_key": "测试数据",
                "special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
                "emoji": "🚀📊💹",
                "newlines": "line1\nline2\nline3"
            }
            json.dump(config_data, f, ensure_ascii=False)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["unicode_key"] == "测试数据"
            assert config["emoji"] == "🚀📊💹"
            assert "\n" in config["newlines"]
        finally:
            os.unlink(temp_path)

    def test_load_config_numeric_types(self):
        """Test loading config with various numeric types."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "integer": 42,
                "float": 3.14159,
                "negative": -123,
                "zero": 0,
                "scientific": 1.23e-4,
                "large_number": 9999999999999999
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["integer"] == 42
            assert config["float"] == 3.14159
            assert config["negative"] == -123
            assert config["zero"] == 0
            assert config["scientific"] == 1.23e-4
        finally:
            os.unlink(temp_path)

    def test_load_config_boolean_and_null(self):
        """Test loading config with boolean and null values."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "true_value": True,
                "false_value": False,
                "null_value": None,
                "empty_string": "",
                "empty_list": [],
                "empty_dict": {}
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["true_value"] is True
            assert config["false_value"] is False
            assert config["null_value"] is None
            assert config["empty_string"] == ""
            assert config["empty_list"] == []
            assert config["empty_dict"] == {}
        finally:
            os.unlink(temp_path)

    def test_load_config_case_sensitivity(self):
        """Test loading config with case-sensitive keys."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_data = {
                "Key": "value1",
                "key": "value2",
                "KEY": "value3",
                "kEy": "value4"
            }
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["Key"] == "value1"
            assert config["key"] == "value2"
            assert config["KEY"] == "value3"
            assert config["kEy"] == "value4"
            assert len(config) == 4  # All keys should be distinct
        finally:
            os.unlink(temp_path)

    def test_load_config_yaml_with_references(self):
        """Test loading YAML config with references and anchors."""
        from src.forex_bot.config_loader import load_config
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml_content = """
defaults: &defaults
  timeout: 30
  retries: 3

development:
  <<: *defaults
  host: localhost
  debug: true

production:
  <<: *defaults
  host: prod.example.com
  debug: false
"""
            f.write(yaml_content)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["development"]["timeout"] == 30
            assert config["development"]["host"] == "localhost"
            assert config["production"]["timeout"] == 30
            assert config["production"]["host"] == "prod.example.com"
        finally:
            os.unlink(temp_path)

    def test_load_config_file_encoding(self):
        """Test loading config file with different encodings."""
        from src.forex_bot.config_loader import load_config
        
        # Test UTF-8 encoding
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            config_data = {"message": "Hello 世界"}
            json.dump(config_data, f, ensure_ascii=False)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            assert config["message"] == "Hello 世界"
        finally:
            os.unlink(temp_path)

    def test_load_config_pathlib_path(self, temp_config_file):
        """Test loading config using pathlib.Path object."""
        from src.forex_bot.config_loader import load_config
        
        path_obj = Path(temp_config_file)
        config = load_config(path_obj)
        
        assert config["api_key"] == "test_key"
        assert config["timeout"] == 30

    def test_load_config_relative_path(self):
        """Test loading config using relative path."""
        from src.forex_bot.config_loader import load_config
        
        # Create config in current directory
        config_data = {"test": "relative_path"}
        with open("test_relative_config.json", "w") as f:
            json.dump(config_data, f)
        
        try:
            config = load_config("test_relative_config.json")
            assert config["test"] == "relative_path"
        finally:
            if os.path.exists("test_relative_config.json"):
                os.unlink("test_relative_config.json")

    def test_load_config_io_error(self):
        """Test loading config with I/O error during read."""
        from src.forex_bot.config_loader import load_config
        
        with patch('builtins.open', side_effect=IOError("I/O error")):
            with pytest.raises(IOError):
                load_config("test_config.json")

    def test_load_config_memory_error(self):
        """Test loading config with memory error."""
        from src.forex_bot.config_loader import load_config
        
        with patch('json.load', side_effect=MemoryError("Out of memory")):
            with pytest.raises(MemoryError):
                load_config("test_config.json")
