"""
Comprehensive test coverage for config_loader.py - Batch 6
Target: Push from 87% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import os
import tempfile
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path


class TestConfigLoaderBatch6Coverage:
    """Test class for config_loader.py comprehensive coverage."""

    def test_config_initialization(self):
        """Test Config initialization."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        # Test default values
        assert config.mt5_login is None
        assert config.mt5_password is None
        assert config.mt5_server is None
        assert config.dry_run is True
        assert config.initial_capital == 10000.0

    def test_config_post_init(self):
        """Test Config __post_init__ method."""
        from src.forex_bot.config_loader import Config
        
        with patch.object(Config, '_load_from_env') as mock_load_env, \
             patch.object(Config, '_calculate_paths') as mock_calc_paths, \
             patch.object(Config, '_check_dependencies') as mock_check_deps:
            
            config = Config()
            
            mock_load_env.assert_called_once()
            mock_calc_paths.assert_called_once()
            mock_check_deps.assert_called_once()

    def test_get_env_int_valid(self):
        """Test _get_env_int with valid integer."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch.dict(os.environ, {'TEST_INT': '123'}):
            result = config._get_env_int('TEST_INT')
            assert result == 123

    def test_get_env_int_invalid(self):
        """Test _get_env_int with invalid integer."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch.dict(os.environ, {'TEST_INT': 'invalid'}):
            result = config._get_env_int('TEST_INT')
            assert result is None

    def test_get_env_int_default(self):
        """Test _get_env_int with default value."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        result = config._get_env_int('NONEXISTENT_VAR', 456)
        assert result == 456

    def test_get_env_float_valid(self):
        """Test _get_env_float with valid float."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch.dict(os.environ, {'TEST_FLOAT': '123.45'}):
            result = config._get_env_float('TEST_FLOAT')
            assert result == 123.45

    def test_get_env_float_invalid(self):
        """Test _get_env_float with invalid float."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch.dict(os.environ, {'TEST_FLOAT': 'invalid'}):
            result = config._get_env_float('TEST_FLOAT')
            assert result is None

    def test_get_env_float_default(self):
        """Test _get_env_float with default value."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        result = config._get_env_float('NONEXISTENT_VAR', 789.12)
        assert result == 789.12

    def test_load_from_env_with_dotenv(self):
        """Test _load_from_env with .env file."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value='/path/to/.env'), \
             patch('src.forex_bot.config_loader.load_dotenv') as mock_load_dotenv, \
             patch.dict(os.environ, {
                 'MT5_LOGIN': '12345',
                 'MT5_PASSWORD': 'test_pass',
                 'GEMINI_API_KEY': 'test_key'
             }):
            
            config._load_from_env()
            
            mock_load_dotenv.assert_called_once_with(dotenv_path='/path/to/.env')
            assert config.mt5_login == 12345
            assert config.mt5_password == 'test_pass'
            assert config.gemini_api_key == 'test_key'

    def test_load_from_env_no_dotenv(self):
        """Test _load_from_env without .env file."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None), \
             patch('builtins.print') as mock_print:
            
            config._load_from_env()
            
            mock_print.assert_called_once_with("[WARNING] .env file not found. Relying on defaults or existing environment variables.")

    def test_calculate_paths(self):
        """Test _calculate_paths method."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        config.base_dir = "/test/base"
        
        config._calculate_paths()
        
        assert config.logs_dir == "/test/base/logs"
        assert config.data_dir == "/test/base/data"
        assert config.models_dir == "/test/base/models"

    def test_check_dependencies_all_available(self):
        """Test _check_dependencies with all dependencies available."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch('src.forex_bot.config_loader.mt5') as mock_mt5, \
             patch('importlib.util.find_spec') as mock_find_spec:
            
            mock_mt5.initialize.return_value = True
            mock_find_spec.return_value = MagicMock()  # Module found
            
            config._check_dependencies()
            
            assert config.mt5_available is True
            assert config.sentence_transformers_available is True
            assert config.qdrant_available is True
            assert config.gemini_available is True

    def test_check_dependencies_mt5_unavailable(self):
        """Test _check_dependencies with MT5 unavailable."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch('src.forex_bot.config_loader.mt5') as mock_mt5:
            mock_mt5.initialize.return_value = False
            
            config._check_dependencies()
            
            assert config.mt5_available is False

    def test_check_dependencies_import_errors(self):
        """Test _check_dependencies with import errors."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        with patch('importlib.util.find_spec', side_effect=ImportError("Module not found")):
            config._check_dependencies()
            
            assert config.sentence_transformers_available is False
            assert config.qdrant_available is False
            assert config.gemini_available is False

    def test_get_config_function(self):
        """Test get_config function."""
        from src.forex_bot.config_loader import get_config
        
        config = get_config()
        
        assert config is not None
        assert hasattr(config, 'mt5_login')
        assert hasattr(config, 'dry_run')

    def test_config_with_environment_overrides(self):
        """Test Config with environment variable overrides."""
        from src.forex_bot.config_loader import Config
        
        env_vars = {
            'MT5_LOGIN': '99999',
            'DRY_RUN': 'false',
            'INITIAL_CAPITAL': '50000.0',
            'GEMINI_MODEL_NAME': 'custom-model',
            'LOG_UPLOAD_INTERVAL_MINUTES': '30'
        }
        
        with patch.dict(os.environ, env_vars):
            config = Config()
            config._load_from_env()
            
            assert config.mt5_login == 99999
            assert config.dry_run is False
            assert config.initial_capital == 50000.0
            assert config.gemini_model_name == 'custom-model'
            assert config.log_upload_interval_minutes == 30

    def test_config_boolean_parsing(self):
        """Test Config boolean parsing from environment."""
        from src.forex_bot.config_loader import Config
        
        test_cases = [
            ('true', True),
            ('True', True),
            ('TRUE', True),
            ('false', False),
            ('False', False),
            ('FALSE', False),
            ('yes', False),  # Only 'true' should be True
            ('1', False),
            ('', False)
        ]
        
        for env_value, expected in test_cases:
            with patch.dict(os.environ, {'DRY_RUN': env_value}):
                config = Config()
                config._load_from_env()
                assert config.dry_run == expected

    def test_config_paths_creation(self):
        """Test Config paths creation."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        config.base_dir = "/custom/path"
        
        config._calculate_paths()
        
        expected_paths = [
            "/custom/path/logs",
            "/custom/path/data", 
            "/custom/path/models"
        ]
        
        assert config.logs_dir in expected_paths
        assert config.data_dir in expected_paths
        assert config.models_dir in expected_paths

    def test_config_edge_case_values(self):
        """Test Config with edge case environment values."""
        from src.forex_bot.config_loader import Config
        
        edge_cases = {
            'MT5_LOGIN': '0',  # Zero value
            'INITIAL_CAPITAL': '0.01',  # Very small float
            'LOG_UPLOAD_INTERVAL_MINUTES': '-1'  # Negative value
        }
        
        with patch.dict(os.environ, edge_cases):
            config = Config()
            config._load_from_env()
            
            assert config.mt5_login == 0
            assert config.initial_capital == 0.01
            assert config.log_upload_interval_minutes == -1

    def test_config_missing_required_fields(self):
        """Test Config behavior with missing required fields."""
        from src.forex_bot.config_loader import Config
        
        # Clear environment
        with patch.dict(os.environ, {}, clear=True):
            config = Config()
            config._load_from_env()
            
            # Should handle missing values gracefully
            assert config.mt5_login is None
            assert config.mt5_password is None
            assert config.gemini_api_key is None

    def test_config_dependency_check_edge_cases(self):
        """Test Config dependency checking edge cases."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        # Test with MT5 exception
        with patch('src.forex_bot.config_loader.mt5') as mock_mt5:
            mock_mt5.initialize.side_effect = Exception("MT5 error")
            
            config._check_dependencies()
            
            assert config.mt5_available is False

    def test_config_qdrant_initialization(self):
        """Test Config Qdrant initialization check."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        config.qdrant_url = "http://localhost:6333"
        config.qdrant_api_key = "test_key"
        
        # Test successful Qdrant check
        with patch('importlib.util.find_spec') as mock_find_spec:
            mock_find_spec.return_value = MagicMock()
            
            config._check_dependencies()
            
            assert config.qdrant_available is True

    def test_config_string_representation(self):
        """Test Config string representation."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        str_repr = str(config)
        assert isinstance(str_repr, str)
        assert "Config" in str_repr

    def test_config_attribute_access(self):
        """Test Config attribute access."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        # Test all major attributes exist
        attributes = [
            'mt5_login', 'mt5_password', 'mt5_server', 'mt5_path',
            'gemini_api_key', 'gemini_model_name', 'jb_news_api_key',
            'qdrant_url', 'qdrant_api_key', 'dry_run', 'initial_capital'
        ]
        
        for attr in attributes:
            assert hasattr(config, attr)

    def test_config_type_conversion_errors(self):
        """Test Config type conversion error handling."""
        from src.forex_bot.config_loader import Config
        
        config = Config()
        
        # Test with values that can't be converted
        with patch.dict(os.environ, {
            'MT5_LOGIN': 'not_a_number',
            'INITIAL_CAPITAL': 'not_a_float'
        }):
            config._load_from_env()
            
            # Should handle conversion errors gracefully
            assert config.mt5_login is None
            assert config.initial_capital == 10000.0  # Default value
