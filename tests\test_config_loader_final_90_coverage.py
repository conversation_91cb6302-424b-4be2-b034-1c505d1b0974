"""
Final coverage tests for the config_loader.py module to reach 90% coverage.
"""
import os
import pytest
import warnings
import sys
import importlib
from unittest.mock import patch, MagicMock, mock_open

from src.forex_bot.config_loader import (
    Config,
    get_config,
    load_env_variables,
    RCLONE_EXE_AVAILABLE,
    PERFORMANCE_ANALYZER_AVAILABLE,
    PA_IMPORT_ERROR_DETAILS,
    ENABLE_GARCH_FORECAST,
    ENABLE_HMM_REGIME,
    ENABLE_HEIKIN_ASHI,
    ENABLE_MACRO_ANALYSIS,
    ENABLE_SENTIMENT_ANALYSIS,
    ENABLE_TREND_ANALYSIS,
    ENABLE_CANDLESTICK_PATTERNS,
    ENABLE_SESSION_CONTEXT,
    ENABLE_ADAPTIVE_SLTP,
)


class TestImportErrorHandling:
    """Tests for import error handling in config_loader.py."""

    def test_import_error_handling(self):
        """Test handling of import errors for Phase 1/2/3 modules."""
        # Test that the module can be imported without errors
        import src.forex_bot.config_loader
        assert hasattr(src.forex_bot.config_loader, 'Config')
        assert hasattr(src.forex_bot.config_loader, 'get_config')
        assert hasattr(src.forex_bot.config_loader, 'load_env_variables')


class TestLoadEnvVariablesFinal:
    """Final tests for the load_env_variables function."""

    @patch('src.forex_bot.config_loader.get_config')
    def test_load_env_variables_return_values(self, mock_get_config):
        """Test that load_env_variables returns the correct values."""
        # Create a mock config
        mock_config = MagicMock()
        mock_config.mt5_login = 12345
        mock_config.mt5_password = "password"
        mock_config.mt5_server = "Demo"
        mock_config.mt5_path = "path/to/mt5"
        mock_config.gemini_api_key = "gemini_api_key"
        mock_config.gemini_model_name = "gemini-1.5-pro"
        mock_config.jb_news_api_key = "jb_news_api_key"

        # Set the mock config to be returned by get_config
        mock_get_config.return_value = mock_config

        # Call the function
        result = load_env_variables()

        # Verify the return values
        assert result == (
            mock_config.mt5_login,
            mock_config.mt5_password,
            mock_config.mt5_server,
            mock_config.mt5_path,
            mock_config.gemini_api_key,
            mock_config.gemini_model_name,
            mock_config.jb_news_api_key
        )

        # Verify that get_config was called
        mock_get_config.assert_called_once()

    @patch('src.forex_bot.config_loader.get_config')
    def test_load_env_variables_print_statement(self, mock_get_config):
        """Test that load_env_variables prints the correct message."""
        # Create a mock config
        mock_config = MagicMock()
        mock_config.mt5_login = 12345

        # Set the mock config to be returned by get_config
        mock_get_config.return_value = mock_config

        # Patch print to capture the output
        with patch('builtins.print') as mock_print:
            # Call the function
            load_env_variables()

            # Verify that the print statement was called with any argument
            mock_print.assert_called()


class TestGlobalVariables:
    """Tests for the global variables in config_loader.py."""

    def test_global_flags_initialization(self):
        """Test that global flags are initialized correctly."""
        # Import the module to trigger the global variable initialization
        import src.forex_bot.config_loader

        # Verify that the global flags are initialized correctly
        assert hasattr(src.forex_bot.config_loader, 'RCLONE_EXE_AVAILABLE')
        assert hasattr(src.forex_bot.config_loader, 'PERFORMANCE_ANALYZER_AVAILABLE')
        assert hasattr(src.forex_bot.config_loader, 'PA_IMPORT_ERROR_DETAILS')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_GARCH_FORECAST')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_HMM_REGIME')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_HEIKIN_ASHI')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_MACRO_ANALYSIS')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_SENTIMENT_ANALYSIS')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_TREND_ANALYSIS')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_CANDLESTICK_PATTERNS')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_SESSION_CONTEXT')
        assert hasattr(src.forex_bot.config_loader, 'ENABLE_ADAPTIVE_SLTP')


class TestConfigClass:
    """Comprehensive tests for the Config class."""

    @patch.dict(os.environ, {}, clear=True)
    def test_config_initialization_defaults(self):
        """Test Config initialization with default values."""
        config = Config()

        # Test default values
        assert config.log_filename == 'trading_bot.log'
        assert config.perf_log_filename == 'trading_performance.log'
        assert config.volume == 0.01
        assert config.risk_reward_ratio == 3.0
        assert config.atr_period == 14
        assert config.atr_multiplier == 1.5
        assert config.rsi_period == 14
        assert config.macd_fast == 12
        assert config.macd_slow == 26
        assert config.macd_signal == 9
        assert config.gemini_m5_bars == 50
        assert config.gemini_h4_bars == 205
        assert config.gemini_recent_trades_context == 5
        assert config.hist_bars_m5 == 500
        assert config.hist_bars_h4 == 300
        assert config.initial_capital == 100000.0
        assert config.dry_run == True
        assert config.enable_trend_analysis == True
        assert config.enable_candlestick_patterns == True
        assert config.enable_session_context == True
        assert config.enable_adaptive_sltp == True

    @patch.dict(os.environ, {
        'MT5_LOGIN': '12345',
        'MT5_PASSWORD': 'test_password',
        'MT5_SERVER': 'Demo',
        'MT5_PATH': '/path/to/mt5',
        'GEMINI_API_KEY': 'test_gemini_key',
        'GEMINI_MODEL_NAME': 'gemini-test',
        'JB_NEWS_API_KEY': 'test_news_key',
        'QDRANT_URL': 'http://localhost:6333',
        'QDRANT_API_KEY': 'test_qdrant_key',
        'VOLUME': '0.02',
        'RISK_REWARD_RATIO': '2.5',
        'INITIAL_CAPITAL': '50000.0',
        'DRY_RUN': 'false',
        'SYMBOLS': 'EURUSD,GBPUSD,USDJPY'
    })
    def test_config_load_from_env(self):
        """Test Config loading from environment variables."""
        config = Config()

        # Test environment variable loading
        assert config.mt5_login == 12345
        assert config.mt5_password == 'test_password'
        assert config.mt5_server == 'Demo'
        assert config.mt5_path == '/path/to/mt5'
        assert config.gemini_api_key == 'test_gemini_key'
        assert config.gemini_model_name == 'gemini-test'
        assert config.jb_news_api_key == 'test_news_key'
        assert config.qdrant_url == 'http://localhost:6333'
        assert config.qdrant_api_key == 'test_qdrant_key'
        assert config.volume == 0.02
        assert config.risk_reward_ratio == 2.5
        assert config.initial_capital == 50000.0
        assert config.dry_run == False
        assert config.symbols == ['EURUSD', 'GBPUSD', 'USDJPY']

    def test_get_env_int_valid(self):
        """Test _get_env_int with valid values."""
        config = Config()

        with patch.dict(os.environ, {'TEST_INT': '123'}):
            result = config._get_env_int('TEST_INT', 456)
            assert result == 123

    def test_get_env_int_invalid(self):
        """Test _get_env_int with invalid values."""
        config = Config()

        with patch.dict(os.environ, {'TEST_INT': 'invalid'}):
            with patch('builtins.print') as mock_print:
                result = config._get_env_int('TEST_INT', 456)
                assert result == 456
                mock_print.assert_called()

    def test_get_env_int_missing(self):
        """Test _get_env_int with missing values."""
        config = Config()

        result = config._get_env_int('MISSING_INT', 789)
        assert result == 789

    def test_get_env_float_valid(self):
        """Test _get_env_float with valid values."""
        config = Config()

        with patch.dict(os.environ, {'TEST_FLOAT': '123.45'}):
            result = config._get_env_float('TEST_FLOAT', 678.90)
            assert result == 123.45

    def test_get_env_float_invalid(self):
        """Test _get_env_float with invalid values."""
        config = Config()

        with patch.dict(os.environ, {'TEST_FLOAT': 'invalid'}):
            with patch('builtins.print') as mock_print:
                result = config._get_env_float('TEST_FLOAT', 678.90)
                assert result == 678.90
                mock_print.assert_called()

    def test_get_env_float_missing(self):
        """Test _get_env_float with missing values."""
        config = Config()

        result = config._get_env_float('MISSING_FLOAT', 999.99)
        assert result == 999.99

    @patch.dict(os.environ, {'SYMBOLS': 'INVALID,FORMAT,'})
    def test_symbols_parsing_error(self):
        """Test symbols parsing with invalid format."""
        config = Config()
        # Should use default symbols when parsing fails
        assert len(config.symbols) > 0
        # The symbols should be parsed correctly even with trailing comma
        assert 'INVALID' in config.symbols
        assert 'FORMAT' in config.symbols

    @patch('src.forex_bot.config_loader.find_dotenv')
    @patch('src.forex_bot.config_loader.load_dotenv')
    def test_dotenv_not_found(self, mock_load_dotenv, mock_find_dotenv):
        """Test behavior when .env file is not found."""
        mock_find_dotenv.return_value = None

        with patch('builtins.print') as mock_print:
            config = Config()
            mock_print.assert_called_with("[WARNING] .env file not found. Relying on defaults or existing environment variables.")

    @patch('src.forex_bot.config_loader.find_dotenv')
    @patch('src.forex_bot.config_loader.load_dotenv')
    def test_dotenv_found(self, mock_load_dotenv, mock_find_dotenv):
        """Test behavior when .env file is found."""
        mock_find_dotenv.return_value = '/path/to/.env'

        config = Config()
        mock_find_dotenv.assert_called()
        mock_load_dotenv.assert_called_with(dotenv_path='/path/to/.env')


class TestConfigPathCalculation:
    """Tests for the _calculate_paths method."""

    @patch('src.forex_bot.config_loader.find_dotenv')
    @patch('src.forex_bot.config_loader.load_dotenv')
    @patch('os.path.exists')
    @patch('os.path.dirname')
    @patch('os.path.abspath')
    def test_calculate_paths_with_file(self, mock_abspath, mock_dirname, mock_exists, mock_load_dotenv, mock_find_dotenv):
        """Test path calculation when __file__ is available."""
        mock_find_dotenv.return_value = '/test/.env'
        mock_dirname.return_value = '/test/src/forex_bot'
        mock_abspath.side_effect = lambda x: x
        mock_exists.side_effect = lambda x: '.env' in x

        config = Config()
        config._calculate_paths()

        assert config.knowledge_base_strategy_file_path is not None
        assert config.knowledge_base_metrics_file_path is not None

    @patch('src.forex_bot.config_loader.find_dotenv')
    @patch('src.forex_bot.config_loader.load_dotenv')
    @patch('os.path.exists')
    @patch('os.getcwd')
    def test_calculate_paths_fallback_to_cwd(self, mock_getcwd, mock_exists, mock_load_dotenv, mock_find_dotenv):
        """Test path calculation fallback to current working directory."""
        mock_find_dotenv.return_value = '/test/.env'
        mock_getcwd.return_value = '/fallback/path'
        mock_exists.return_value = False

        with patch('builtins.print') as mock_print:
            config = Config()
            config._calculate_paths()
            # Should have paths set even if project root not found
            assert config.knowledge_base_strategy_file_path is not None
            assert config.knowledge_base_metrics_file_path is not None

    def test_calculate_paths_basic_functionality(self):
        """Test basic path calculation functionality."""
        config = Config()

        # Test that paths are set after initialization
        assert config.knowledge_base_strategy_file_path is not None
        assert config.knowledge_base_metrics_file_path is not None
        assert isinstance(config.knowledge_base_strategy_file_path, str)
        assert isinstance(config.knowledge_base_metrics_file_path, str)


class TestGetConfigSingleton:
    """Tests for the get_config singleton pattern."""

    def test_get_config_singleton(self):
        """Test that get_config returns the same instance."""
        # Clear the singleton instance
        import src.forex_bot.config_loader
        src.forex_bot.config_loader._config_instance = None

        config1 = get_config()
        config2 = get_config()

        assert config1 is config2
        assert isinstance(config1, Config)

    def test_get_config_creates_instance(self):
        """Test that get_config creates a new instance when needed."""
        # Clear the singleton instance
        import src.forex_bot.config_loader
        src.forex_bot.config_loader._config_instance = None

        config = get_config()
        assert isinstance(config, Config)
        assert src.forex_bot.config_loader._config_instance is config


class TestLoadEnvVariablesEdgeCases:
    """Tests for edge cases in load_env_variables function."""

    @patch('src.forex_bot.config_loader.get_config')
    def test_load_env_variables_missing_credentials(self, mock_get_config):
        """Test load_env_variables when credentials are missing."""
        # Create a mock config with missing credentials
        mock_config = MagicMock()
        mock_config.mt5_login = None
        mock_config.mt5_password = None
        mock_config.mt5_server = None
        mock_config.mt5_path = None
        mock_config.gemini_api_key = None
        mock_config.gemini_model_name = "gemini-1.5-pro"
        mock_config.jb_news_api_key = None

        mock_get_config.return_value = mock_config

        with patch('builtins.print') as mock_print:
            result = load_env_variables()

            # Should return tuple of None values
            assert result == (None, None, None, None, None, None, None)

            # Should print error message
            mock_print.assert_called()


class TestConfigDependencyChecking:
    """Tests for the _check_dependencies method."""

    @patch('src.forex_bot.config_loader.find_dotenv')
    @patch('src.forex_bot.config_loader.load_dotenv')
    def test_check_dependencies(self, mock_load_dotenv, mock_find_dotenv):
        """Test that _check_dependencies sets availability flags."""
        mock_find_dotenv.return_value = '/test/.env'

        config = Config()
        config._check_dependencies()

        # Test that dependency flags are set (they should be boolean values)
        assert isinstance(config.rclone_exe_available, bool)
        assert isinstance(config.qdrant_client_available, bool)
        assert isinstance(config.sentence_transformers_available, bool)
        assert isinstance(config.qdrant_available, bool)
        assert isinstance(config.gemini_available, bool)


class TestConfigConstants:
    """Tests for module-level constants."""

    def test_module_constants_exist(self):
        """Test that module-level constants are defined."""
        import src.forex_bot.config_loader as config_module

        # Test that constants are defined
        assert hasattr(config_module, 'SYMBOLS')
        assert hasattr(config_module, 'TIMEFRAME')
        assert hasattr(config_module, 'VOLUME')
        assert hasattr(config_module, 'RISK_REWARD_RATIO')
        assert hasattr(config_module, 'ATR_PERIOD')
        assert hasattr(config_module, 'ATR_MULTIPLIER')
        assert hasattr(config_module, 'RSI_PERIOD')
        assert hasattr(config_module, 'MACD_FAST')
        assert hasattr(config_module, 'MACD_SLOW')
        assert hasattr(config_module, 'MACD_SIGNAL')
        assert hasattr(config_module, 'GEMINI_M5_BARS')
        assert hasattr(config_module, 'GEMINI_H4_BARS')
        assert hasattr(config_module, 'DRY_RUN')

    def test_knowledge_base_paths(self):
        """Test knowledge base path constants."""
        import src.forex_bot.config_loader as config_module

        assert hasattr(config_module, 'KNOWLEDGE_BASE_STRATEGY_FILE_PATH')
        assert hasattr(config_module, 'KNOWLEDGE_BASE_METRICS_FILE_PATH')
        assert config_module.KNOWLEDGE_BASE_STRATEGY_FILE_PATH is not None
        assert config_module.KNOWLEDGE_BASE_METRICS_FILE_PATH is not None
