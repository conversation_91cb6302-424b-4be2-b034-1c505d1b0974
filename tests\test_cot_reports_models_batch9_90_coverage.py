"""
Comprehensive test coverage for cot_reports/models.py - Batch 9
Target: Push from 65% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
from datetime import date, datetime
from unittest.mock import patch, MagicMock
import copy


class TestCOTReportsModelsBatch9Coverage:
    """Test class for cot_reports/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_date(self):
        """Sample date for testing."""
        return date(2024, 1, 15)

    @pytest.fixture
    def complete_cot_position_data(self, sample_date):
        """Complete COT position data for testing."""
        return {
            'report_date': sample_date,
            'commodity': 'EURUSD',
            'commercial_long': 150000,
            'commercial_short': 120000,
            'non_commercial_long': 80000,
            'non_commercial_short': 110000,
            'non_reportable_long': 30000,
            'non_reportable_short': 25000,
            'open_interest': 485000
        }

    def test_cot_position_complete_initialization(self, complete_cot_position_data):
        """Test COTPosition complete initialization."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        position = COTPosition(**complete_cot_position_data)
        
        assert position.report_date == complete_cot_position_data['report_date']
        assert position.commodity == 'EURUSD'
        assert position.commercial_long == 150000
        assert position.commercial_short == 120000
        assert position.non_commercial_long == 80000
        assert position.non_commercial_short == 110000
        assert position.non_reportable_long == 30000
        assert position.non_reportable_short == 25000
        assert position.open_interest == 485000

    def test_cot_position_minimal_initialization(self, sample_date):
        """Test COTPosition with minimal required fields."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        minimal_data = {
            'report_date': sample_date,
            'commodity': 'GBPUSD',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**minimal_data)
        
        assert position.commodity == 'GBPUSD'
        assert position.commercial_long == 100000
        assert position.open_interest == 358000

    def test_cot_position_different_commodities(self, sample_date):
        """Test COTPosition with different commodity types."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        commodities = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD',
            'NZDUSD', 'GOLD', 'SILVER', 'CRUDE_OIL', 'NATURAL_GAS'
        ]
        
        base_data = {
            'report_date': sample_date,
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        for commodity in commodities:
            data = base_data.copy()
            data['commodity'] = commodity
            
            position = COTPosition(**data)
            assert position.commodity == commodity

    def test_cot_position_zero_values(self, sample_date):
        """Test COTPosition with zero values."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        zero_data = {
            'report_date': sample_date,
            'commodity': 'ZERO_TEST',
            'commercial_long': 0,
            'commercial_short': 0,
            'non_commercial_long': 0,
            'non_commercial_short': 0,
            'non_reportable_long': 0,
            'non_reportable_short': 0,
            'open_interest': 0
        }
        
        position = COTPosition(**zero_data)
        
        assert position.commercial_long == 0
        assert position.commercial_short == 0
        assert position.open_interest == 0

    def test_cot_position_large_values(self, sample_date):
        """Test COTPosition with very large values."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        large_data = {
            'report_date': sample_date,
            'commodity': 'LARGE_TEST',
            'commercial_long': 999999999,
            'commercial_short': 888888888,
            'non_commercial_long': 777777777,
            'non_commercial_short': 666666666,
            'non_reportable_long': 555555555,
            'non_reportable_short': 444444444,
            'open_interest': 4331331331
        }
        
        position = COTPosition(**large_data)
        
        assert position.commercial_long == 999999999
        assert position.open_interest == 4331331331

    def test_cot_position_historical_dates(self):
        """Test COTPosition with various historical dates."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        dates = [
            date(2020, 1, 1),
            date(2021, 6, 15),
            date(2022, 12, 31),
            date(2023, 7, 4),
            date(2024, 2, 29)  # Leap year
        ]
        
        base_data = {
            'commodity': 'DATE_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        for test_date in dates:
            data = base_data.copy()
            data['report_date'] = test_date
            
            position = COTPosition(**data)
            assert position.report_date == test_date

    def test_cot_position_copy_operations(self, complete_cot_position_data):
        """Test COTPosition copy operations."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        position = COTPosition(**complete_cot_position_data)
        
        # Test shallow copy
        position_copy = copy.copy(position)
        assert position_copy.commodity == position.commodity
        assert position_copy.commercial_long == position.commercial_long
        
        # Test deep copy
        position_deepcopy = copy.deepcopy(position)
        assert position_deepcopy.commodity == position.commodity
        assert position_deepcopy.report_date == position.report_date

    def test_cot_position_string_representations(self, complete_cot_position_data):
        """Test COTPosition string representations."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        position = COTPosition(**complete_cot_position_data)
        
        # Test string representation
        str_repr = str(position)
        assert isinstance(str_repr, str)
        assert "COTPosition" in str_repr

    def test_cot_position_equality_comparison(self, sample_date):
        """Test COTPosition equality comparison."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        data1 = {
            'report_date': sample_date,
            'commodity': 'EQUAL_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        data2 = data1.copy()
        
        position1 = COTPosition(**data1)
        position2 = COTPosition(**data2)
        
        # Test individual attributes for equality
        assert position1.commodity == position2.commodity
        assert position1.commercial_long == position2.commercial_long
        assert position1.report_date == position2.report_date

    def test_cot_position_negative_values(self, sample_date):
        """Test COTPosition with negative values (edge case)."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        # Some systems might have negative values in edge cases
        negative_data = {
            'report_date': sample_date,
            'commodity': 'NEGATIVE_TEST',
            'commercial_long': -1000,
            'commercial_short': -500,
            'non_commercial_long': -200,
            'non_commercial_short': -300,
            'non_reportable_long': -100,
            'non_reportable_short': -50,
            'open_interest': -2150
        }
        
        position = COTPosition(**negative_data)
        
        assert position.commercial_long == -1000
        assert position.open_interest == -2150

    def test_cot_summary_initialization(self, sample_date):
        """Test COTSummary initialization."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        # Create sample positions
        positions = []
        for i in range(3):
            pos_data = {
                'report_date': sample_date,
                'commodity': f'TEST_{i}',
                'commercial_long': 100000 + i * 10000,
                'commercial_short': 95000 + i * 5000,
                'non_commercial_long': 60000 + i * 3000,
                'non_commercial_short': 65000 + i * 2000,
                'non_reportable_long': 20000 + i * 1000,
                'non_reportable_short': 18000 + i * 500,
                'open_interest': 358000 + i * 20000
            }
            positions.append(COTPosition(**pos_data))
        
        summary = COTSummary(
            report_date=sample_date,
            positions=positions,
            total_open_interest=sum(p.open_interest for p in positions)
        )
        
        assert summary.report_date == sample_date
        assert len(summary.positions) == 3
        assert summary.total_open_interest == sum(p.open_interest for p in positions)

    def test_cot_summary_empty_positions(self, sample_date):
        """Test COTSummary with empty positions list."""
        from src.forex_bot.cot_reports.models import COTSummary
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[],
            total_open_interest=0
        )
        
        assert summary.report_date == sample_date
        assert len(summary.positions) == 0
        assert summary.total_open_interest == 0

    def test_cot_summary_single_position(self, sample_date):
        """Test COTSummary with single position."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'SINGLE_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=position.open_interest
        )
        
        assert len(summary.positions) == 1
        assert summary.total_open_interest == position.open_interest

    def test_cot_summary_large_positions_list(self, sample_date):
        """Test COTSummary with large number of positions."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        positions = []
        total_oi = 0
        
        for i in range(100):
            pos_data = {
                'report_date': sample_date,
                'commodity': f'LARGE_TEST_{i}',
                'commercial_long': 1000 + i,
                'commercial_short': 950 + i,
                'non_commercial_long': 600 + i,
                'non_commercial_short': 650 + i,
                'non_reportable_long': 200 + i,
                'non_reportable_short': 180 + i,
                'open_interest': 3580 + i * 10
            }
            position = COTPosition(**pos_data)
            positions.append(position)
            total_oi += position.open_interest
        
        summary = COTSummary(
            report_date=sample_date,
            positions=positions,
            total_open_interest=total_oi
        )
        
        assert len(summary.positions) == 100
        assert summary.total_open_interest == total_oi

    def test_cot_summary_copy_operations(self, sample_date):
        """Test COTSummary copy operations."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'COPY_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=position.open_interest
        )
        
        # Test shallow copy
        summary_copy = copy.copy(summary)
        assert summary_copy.report_date == summary.report_date
        assert len(summary_copy.positions) == len(summary.positions)
        
        # Test deep copy
        summary_deepcopy = copy.deepcopy(summary)
        assert summary_deepcopy.report_date == summary.report_date
        assert summary_deepcopy.positions is not summary.positions

    def test_cot_summary_string_representations(self, sample_date):
        """Test COTSummary string representations."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'STR_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=position.open_interest
        )
        
        # Test string representation
        str_repr = str(summary)
        assert isinstance(str_repr, str)
        assert "COTSummary" in str_repr

    def test_cot_summary_mismatched_total(self, sample_date):
        """Test COTSummary with mismatched total open interest."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'MISMATCH_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        # Intentionally mismatch the total
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=999999  # Different from position.open_interest
        )
        
        assert summary.total_open_interest == 999999
        assert summary.positions[0].open_interest == 358000

    def test_cot_models_edge_case_values(self, sample_date):
        """Test COT models with edge case values."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        edge_cases = [
            # Very small values
            {
                'report_date': sample_date,
                'commodity': 'SMALL',
                'commercial_long': 1,
                'commercial_short': 1,
                'non_commercial_long': 1,
                'non_commercial_short': 1,
                'non_reportable_long': 1,
                'non_reportable_short': 1,
                'open_interest': 6
            },
            # Maximum integer values
            {
                'report_date': sample_date,
                'commodity': 'MAX',
                'commercial_long': 2147483647,
                'commercial_short': 2147483647,
                'non_commercial_long': 2147483647,
                'non_commercial_short': 2147483647,
                'non_reportable_long': 2147483647,
                'non_reportable_short': 2147483647,
                'open_interest': 12884901882
            }
        ]
        
        for edge_case in edge_cases:
            position = COTPosition(**edge_case)
            assert position.commodity == edge_case['commodity']
            assert position.open_interest == edge_case['open_interest']

    def test_cot_position_attribute_access(self, complete_cot_position_data):
        """Test COTPosition attribute access."""
        from src.forex_bot.cot_reports.models import COTPosition
        
        position = COTPosition(**complete_cot_position_data)
        
        # Test all attributes exist and are accessible
        attributes = [
            'report_date', 'commodity', 'commercial_long', 'commercial_short',
            'non_commercial_long', 'non_commercial_short', 'non_reportable_long',
            'non_reportable_short', 'open_interest'
        ]
        
        for attr in attributes:
            assert hasattr(position, attr)
            value = getattr(position, attr)
            assert value is not None

    def test_cot_summary_attribute_access(self, sample_date):
        """Test COTSummary attribute access."""
        from src.forex_bot.cot_reports.models import COTSummary, COTPosition
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'ATTR_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=position.open_interest
        )
        
        # Test all attributes exist and are accessible
        attributes = ['report_date', 'positions', 'total_open_interest']
        
        for attr in attributes:
            assert hasattr(summary, attr)
            value = getattr(summary, attr)
            assert value is not None

    def test_cot_models_type_consistency(self, sample_date):
        """Test COT models type consistency."""
        from src.forex_bot.cot_reports.models import COTPosition, COTSummary
        
        position_data = {
            'report_date': sample_date,
            'commodity': 'TYPE_TEST',
            'commercial_long': 100000,
            'commercial_short': 95000,
            'non_commercial_long': 60000,
            'non_commercial_short': 65000,
            'non_reportable_long': 20000,
            'non_reportable_short': 18000,
            'open_interest': 358000
        }
        
        position = COTPosition(**position_data)
        
        # Test types
        assert isinstance(position.report_date, date)
        assert isinstance(position.commodity, str)
        assert isinstance(position.commercial_long, int)
        assert isinstance(position.open_interest, int)
        
        summary = COTSummary(
            report_date=sample_date,
            positions=[position],
            total_open_interest=position.open_interest
        )
        
        assert isinstance(summary.report_date, date)
        assert isinstance(summary.positions, list)
        assert isinstance(summary.total_open_interest, int)
