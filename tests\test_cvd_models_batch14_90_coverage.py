"""
Comprehensive test coverage for cvd/models.py - Batch 14
Target: Push from 73% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime


class TestCVDModelsBatch14Coverage:
    """Test class for cvd/models.py comprehensive coverage."""

    def test_cvd_result_initialization(self):
        """Test CVDResult initialization."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3, 4, 5])
        cvd_values = np.array([100.0, 150.0, 120.0, 180.0, 200.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.timestamps, timestamps)
        assert np.array_equal(result.cvd_values, cvd_values)
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5

    def test_cvd_result_to_dataframe_basic(self):
        """Test CVDResult to_dataframe method basic functionality."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert len(df) == 3
        assert np.array_equal(df['timestamp'].values, timestamps)
        assert np.array_equal(df['cvd'].values, cvd_values)

    def test_cvd_result_with_volume_data(self):
        """Test CVDResult with buying and selling volume data."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        buying_volume = np.array([50.0, 75.0, 60.0])
        selling_volume = np.array([30.0, 45.0, 40.0])
        delta_volume = np.array([20.0, 30.0, 20.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5,
            buying_volume=buying_volume,
            selling_volume=selling_volume,
            delta_volume=delta_volume
        )
        
        df = result.to_dataframe()
        
        assert 'buying_volume' in df.columns
        assert 'selling_volume' in df.columns
        assert 'delta_volume' in df.columns
        assert np.array_equal(df['buying_volume'].values, buying_volume)
        assert np.array_equal(df['selling_volume'].values, selling_volume)
        assert np.array_equal(df['delta_volume'].values, delta_volume)

    def test_cvd_result_with_partial_volume_data(self):
        """Test CVDResult with only some volume data."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        buying_volume = np.array([50.0, 75.0, 60.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5,
            buying_volume=buying_volume,
            # Only buying_volume, others are None
        )
        
        df = result.to_dataframe()
        
        assert 'buying_volume' in df.columns
        assert 'selling_volume' not in df.columns
        assert 'delta_volume' not in df.columns

    def test_cvd_result_with_mismatched_volume_length(self):
        """Test CVDResult with volume data of different length."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        buying_volume = np.array([50.0, 75.0])  # Different length
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5,
            buying_volume=buying_volume
        )
        
        df = result.to_dataframe()
        
        # Volume should not be included due to length mismatch
        assert 'buying_volume' not in df.columns

    def test_cvd_result_with_optional_parameters(self):
        """Test CVDResult with optional parameters."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5,
            start_time=pd.Timestamp('2024-01-01'),
            end_time=pd.Timestamp('2024-01-02')
        )
        
        assert result.start_time == pd.Timestamp('2024-01-01')
        assert result.end_time == pd.Timestamp('2024-01-02')

    def test_cvd_result_default_values(self):
        """Test CVDResult default values."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert result.start_time is None
        assert result.end_time is None
        assert result.buying_volume is None
        assert result.selling_volume is None
        assert result.delta_volume is None

    def test_cvd_divergence_initialization(self):
        """Test CVDDivergence initialization."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2024-01-01 10:00:00'),
            end_time=pd.Timestamp('2024-01-01 11:00:00'),
            divergence_type="bullish",
            strength=0.8,
            price_start=1.2340,
            price_end=1.2350,
            cvd_start=100.0,
            cvd_end=80.0
        )
        
        assert divergence.start_time == pd.Timestamp('2024-01-01 10:00:00')
        assert divergence.end_time == pd.Timestamp('2024-01-01 11:00:00')
        assert divergence.divergence_type == "bullish"
        assert divergence.strength == 0.8
        assert divergence.price_start == 1.2340
        assert divergence.price_end == 1.2350
        assert divergence.cvd_start == 100.0
        assert divergence.cvd_end == 80.0

    def test_cvd_divergence_default_values(self):
        """Test CVDDivergence default values."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2024-01-01 10:00:00'),
            end_time=pd.Timestamp('2024-01-01 11:00:00'),
            divergence_type="bullish",
            price_start=1.2340,
            price_end=1.2350,
            cvd_start=100.0,
            cvd_end=80.0
        )
        
        assert divergence.strength is None

    def test_cvd_divergence_types(self):
        """Test CVDDivergence with different divergence types."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        divergence_types = ["bullish", "bearish", "hidden_bullish", "hidden_bearish"]
        
        for divergence_type in divergence_types:
            divergence = CVDDivergence(
                start_time=pd.Timestamp('2024-01-01 10:00:00'),
                end_time=pd.Timestamp('2024-01-01 11:00:00'),
                divergence_type=divergence_type,
                price_start=1.2340,
                price_end=1.2350,
                cvd_start=100.0,
                cvd_end=80.0
            )
            assert divergence.divergence_type == divergence_type

    def test_cvd_result_empty_arrays(self):
        """Test CVDResult with empty arrays."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=np.array([]),
            cvd_values=np.array([]),
            symbol="TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        assert len(df) == 0
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns

    def test_cvd_result_large_arrays(self):
        """Test CVDResult with large arrays."""
        from src.forex_bot.cvd.models import CVDResult
        
        size = 1000
        timestamps = np.arange(size)
        cvd_values = np.random.rand(size) * 1000
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="LARGE_TEST",
            timeframe=60
        )
        
        df = result.to_dataframe()
        assert len(df) == size
        assert len(result.timestamps) == size
        assert len(result.cvd_values) == size

    def test_cvd_result_dataframe_column_order(self):
        """Test CVDResult DataFrame column order."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="COLUMN_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        expected_columns = ['timestamp', 'cvd']
        assert list(df.columns) == expected_columns

    def test_cvd_result_dataframe_with_all_volumes(self):
        """Test CVDResult DataFrame with all volume data included."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        buying_volume = np.array([50.0, 75.0, 60.0])
        selling_volume = np.array([30.0, 45.0, 40.0])
        delta_volume = np.array([20.0, 30.0, 20.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="ALL_VOLUMES_TEST",
            timeframe=5,
            buying_volume=buying_volume,
            selling_volume=selling_volume,
            delta_volume=delta_volume
        )
        
        df = result.to_dataframe()
        expected_columns = [
            'timestamp', 'cvd', 'buying_volume', 'selling_volume', 'delta_volume'
        ]
        assert list(df.columns) == expected_columns

    def test_cvd_result_dataframe_data_types(self):
        """Test CVDResult DataFrame data types."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Check that all columns are numeric
        assert pd.api.types.is_numeric_dtype(df['timestamp'])
        assert pd.api.types.is_numeric_dtype(df['cvd'])

    def test_cvd_divergence_extreme_values(self):
        """Test CVDDivergence with extreme values."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        # Very large values
        divergence_large = CVDDivergence(
            start_time=pd.Timestamp('2024-01-01 10:00:00'),
            end_time=pd.Timestamp('2024-01-01 11:00:00'),
            divergence_type="bullish",
            strength=1.0,
            price_start=999999.999999,
            price_end=999999.999998,
            cvd_start=999999999.0,
            cvd_end=999999998.0
        )
        
        assert divergence_large.price_start == 999999.999999
        assert divergence_large.price_end == 999999.999998
        assert divergence_large.cvd_start == 999999999.0
        assert divergence_large.cvd_end == 999999998.0

    def test_cvd_result_mixed_data_types(self):
        """Test CVDResult with mixed data types."""
        from src.forex_bot.cvd.models import CVDResult
        
        # Mix of int and float
        timestamps = np.array([1, 2.5, 3])
        cvd_values = np.array([100, 150.5, 120])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="MIXED_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        assert len(df) == 3
        assert df['timestamp'].iloc[1] == 2.5
        assert df['cvd'].iloc[1] == 150.5

    def test_cvd_divergence_negative_values(self):
        """Test CVDDivergence with negative values."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2024-01-01 10:00:00'),
            end_time=pd.Timestamp('2024-01-01 11:00:00'),
            divergence_type="bearish",
            strength=-0.5,
            price_start=-1.2340,
            price_end=-1.2350,
            cvd_start=-100.0,
            cvd_end=-80.0
        )
        
        assert divergence.price_start == -1.2340
        assert divergence.price_end == -1.2350
        assert divergence.cvd_start == -100.0
        assert divergence.cvd_end == -80.0
        assert divergence.strength == -0.5

    def test_cvd_result_string_representation(self):
        """Test CVDResult string representation."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test that string representation works
        str_repr = str(result)
        assert isinstance(str_repr, str)

    def test_cvd_divergence_string_representation(self):
        """Test CVDDivergence string representation."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2024-01-01 10:00:00'),
            end_time=pd.Timestamp('2024-01-01 11:00:00'),
            divergence_type="bullish",
            price_start=1.2340,
            price_end=1.2350,
            cvd_start=100.0,
            cvd_end=80.0
        )
        
        # Test that string representation works
        str_repr = str(divergence)
        assert isinstance(str_repr, str)

    def test_cvd_result_type_consistency(self):
        """Test CVDResult type consistency."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1.0, 2.0, 3.0])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="TYPE_TEST",
            timeframe=5
        )
        
        assert isinstance(result.timestamps, np.ndarray)
        assert isinstance(result.cvd_values, np.ndarray)
        assert isinstance(result.symbol, str)
        assert isinstance(result.timeframe, int)

    def test_cvd_divergence_time_duration(self):
        """Test CVDDivergence time duration calculation."""
        from src.forex_bot.cvd.models import CVDDivergence
        
        start_time = pd.Timestamp('2024-01-01 10:00:00')
        end_time = pd.Timestamp('2024-01-01 11:30:00')
        
        divergence = CVDDivergence(
            start_time=start_time,
            end_time=end_time,
            divergence_type="bullish",
            price_start=1.2340,
            price_end=1.2350,
            cvd_start=100.0,
            cvd_end=80.0
        )
        
        # Calculate duration
        duration = divergence.end_time - divergence.start_time
        expected_duration = pd.Timedelta(hours=1, minutes=30)
        
        assert duration == expected_duration

    def test_cvd_result_zero_values(self):
        """Test CVDResult with zero values."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([0, 0, 0])
        cvd_values = np.array([0.0, 0.0, 0.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="ZERO_TEST",
            timeframe=0
        )
        
        df = result.to_dataframe()
        assert len(df) == 3
        assert all(df['timestamp'] == 0)
        assert all(df['cvd'] == 0.0)
        assert result.timeframe == 0

    def test_cvd_result_with_none_volume_arrays(self):
        """Test CVDResult with explicitly None volume arrays."""
        from src.forex_bot.cvd.models import CVDResult
        
        timestamps = np.array([1, 2, 3])
        cvd_values = np.array([100.0, 150.0, 120.0])
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="NONE_VOLUMES_TEST",
            timeframe=5,
            buying_volume=None,
            selling_volume=None,
            delta_volume=None
        )
        
        df = result.to_dataframe()
        
        # Should only have basic columns
        expected_columns = ['timestamp', 'cvd']
        assert list(df.columns) == expected_columns