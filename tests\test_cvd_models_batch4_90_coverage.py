"""
Comprehensive test coverage for cvd/models.py - Batch 4
Target: Push from 73% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock
import copy


class TestCVDModelsBatch4Coverage:
    """Test class for cvd/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_timestamps(self):
        """Sample timestamps for testing."""
        return np.array([
            pd.Timestamp('2024-01-01 10:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 11:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 12:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 13:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 14:00:00', tz='UTC')
        ])

    @pytest.fixture
    def sample_cvd_values(self):
        """Sample CVD values for testing."""
        return np.array([100.0, 150.0, 120.0, 180.0, 200.0])

    @pytest.fixture
    def sample_volumes(self):
        """Sample volume arrays for testing."""
        buying = np.array([50.0, 75.0, 60.0, 90.0, 100.0])
        selling = np.array([30.0, 45.0, 40.0, 60.0, 70.0])
        delta = buying - selling
        return buying, selling, delta

    def test_cvd_result_initialization(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult initialization."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.timestamps, sample_timestamps)
        assert np.array_equal(result.cvd_values, sample_cvd_values)
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5
        assert result.start_time is None
        assert result.end_time is None

    def test_cvd_result_with_optional_parameters(self, sample_timestamps, sample_cvd_values, sample_volumes):
        """Test CVDResult with optional parameters."""
        from src.forex_bot.cvd.models import CVDResult
        
        buying, selling, delta = sample_volumes
        start_time = pd.Timestamp('2024-01-01 09:00:00', tz='UTC')
        end_time = pd.Timestamp('2024-01-01 15:00:00', tz='UTC')
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EURUSD",
            timeframe=5,
            start_time=start_time,
            end_time=end_time,
            buying_volume=buying,
            selling_volume=selling,
            delta_volume=delta
        )
        
        assert result.start_time == start_time
        assert result.end_time == end_time
        assert np.array_equal(result.buying_volume, buying)
        assert np.array_equal(result.selling_volume, selling)
        assert np.array_equal(result.delta_volume, delta)

    def test_cvd_result_to_dataframe_basic(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult to_dataframe method with basic data."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert len(df) == len(sample_timestamps)
        assert np.array_equal(df['cvd'].values, sample_cvd_values)

    def test_cvd_result_to_dataframe_with_volumes(self, sample_timestamps, sample_cvd_values, sample_volumes):
        """Test CVDResult to_dataframe method with volume data."""
        from src.forex_bot.cvd.models import CVDResult
        
        buying, selling, delta = sample_volumes
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EURUSD",
            timeframe=5,
            buying_volume=buying,
            selling_volume=selling,
            delta_volume=delta
        )
        
        df = result.to_dataframe()
        
        assert 'buying_volume' in df.columns
        assert 'selling_volume' in df.columns
        assert 'delta_volume' in df.columns
        assert np.array_equal(df['buying_volume'].values, buying)
        assert np.array_equal(df['selling_volume'].values, selling)
        assert np.array_equal(df['delta_volume'].values, delta)

    def test_cvd_result_to_dataframe_mismatched_lengths(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult to_dataframe with mismatched array lengths."""
        from src.forex_bot.cvd.models import CVDResult
        
        # Create volume arrays with different lengths
        short_buying = np.array([50.0, 75.0])  # Only 2 elements instead of 5
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EURUSD",
            timeframe=5,
            buying_volume=short_buying
        )
        
        df = result.to_dataframe()
        
        # Should not include buying_volume due to length mismatch
        assert 'buying_volume' not in df.columns
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns

    def test_cvd_result_empty_arrays(self):
        """Test CVDResult with empty arrays."""
        from src.forex_bot.cvd.models import CVDResult
        
        empty_timestamps = np.array([])
        empty_values = np.array([])
        
        result = CVDResult(
            timestamps=empty_timestamps,
            cvd_values=empty_values,
            symbol="EMPTY",
            timeframe=1
        )
        
        assert len(result.timestamps) == 0
        assert len(result.cvd_values) == 0
        
        df = result.to_dataframe()
        assert len(df) == 0

    def test_cvd_result_single_value(self):
        """Test CVDResult with single value."""
        from src.forex_bot.cvd.models import CVDResult
        
        single_timestamp = np.array([pd.Timestamp('2024-01-01 10:00:00', tz='UTC')])
        single_value = np.array([100.0])
        
        result = CVDResult(
            timestamps=single_timestamp,
            cvd_values=single_value,
            symbol="SINGLE",
            timeframe=5
        )
        
        assert len(result.timestamps) == 1
        assert len(result.cvd_values) == 1
        
        df = result.to_dataframe()
        assert len(df) == 1
        assert df['cvd'].iloc[0] == 100.0

    def test_cvd_result_large_dataset(self):
        """Test CVDResult with large dataset."""
        from src.forex_bot.cvd.models import CVDResult
        
        large_size = 10000
        large_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC') + pd.Timedelta(minutes=i)
            for i in range(large_size)
        ])
        large_values = np.random.rand(large_size) * 1000
        
        result = CVDResult(
            timestamps=large_timestamps,
            cvd_values=large_values,
            symbol="LARGE",
            timeframe=1
        )
        
        assert len(result.timestamps) == large_size
        assert len(result.cvd_values) == large_size
        
        df = result.to_dataframe()
        assert len(df) == large_size

    def test_cvd_result_extreme_values(self):
        """Test CVDResult with extreme values."""
        from src.forex_bot.cvd.models import CVDResult
        
        extreme_timestamps = np.array([
            pd.Timestamp('1900-01-01', tz='UTC'),
            pd.Timestamp('2100-12-31', tz='UTC')
        ])
        extreme_values = np.array([-999999999.99, 999999999.99])
        
        result = CVDResult(
            timestamps=extreme_timestamps,
            cvd_values=extreme_values,
            symbol="EXTREME",
            timeframe=1440
        )
        
        assert result.cvd_values[0] == -999999999.99
        assert result.cvd_values[1] == 999999999.99
        
        df = result.to_dataframe()
        assert len(df) == 2

    def test_cvd_result_nan_values(self):
        """Test CVDResult with NaN values."""
        from src.forex_bot.cvd.models import CVDResult
        
        nan_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC'),
            pd.Timestamp('2024-01-02', tz='UTC')
        ])
        nan_values = np.array([100.0, np.nan])
        
        result = CVDResult(
            timestamps=nan_timestamps,
            cvd_values=nan_values,
            symbol="NAN_TEST",
            timeframe=1440
        )
        
        assert not np.isnan(result.cvd_values[0])
        assert np.isnan(result.cvd_values[1])
        
        df = result.to_dataframe()
        assert len(df) == 2
        assert np.isnan(df['cvd'].iloc[1])

    def test_cvd_result_inf_values(self):
        """Test CVDResult with infinite values."""
        from src.forex_bot.cvd.models import CVDResult
        
        inf_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC'),
            pd.Timestamp('2024-01-02', tz='UTC')
        ])
        inf_values = np.array([np.inf, -np.inf])
        
        result = CVDResult(
            timestamps=inf_timestamps,
            cvd_values=inf_values,
            symbol="INF_TEST",
            timeframe=1440
        )
        
        assert np.isinf(result.cvd_values[0])
        assert np.isinf(result.cvd_values[1])
        
        df = result.to_dataframe()
        assert len(df) == 2

    def test_cvd_result_copy_operations(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult copy operations."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="COPY_TEST",
            timeframe=5
        )
        
        # Test shallow copy
        result_copy = copy.copy(result)
        assert result_copy.symbol == result.symbol
        assert np.array_equal(result_copy.timestamps, result.timestamps)
        
        # Test deep copy
        result_deepcopy = copy.deepcopy(result)
        assert result_deepcopy.symbol == result.symbol
        assert np.array_equal(result_deepcopy.timestamps, result.timestamps)

    def test_cvd_result_string_representations(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult string representations."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test string representation
        str_repr = str(result)
        assert isinstance(str_repr, str)
        assert "CVDResult" in str_repr

    def test_cvd_result_equality_comparison(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult equality comparison."""
        from src.forex_bot.cvd.models import CVDResult
        
        result1 = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        result2 = CVDResult(
            timestamps=sample_timestamps.copy(),
            cvd_values=sample_cvd_values.copy(),
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        # Test individual attributes for equality
        assert result1.symbol == result2.symbol
        assert result1.timeframe == result2.timeframe
        assert np.array_equal(result1.timestamps, result2.timestamps)
        assert np.array_equal(result1.cvd_values, result2.cvd_values)

    def test_cvd_result_dataframe_index_handling(self, sample_timestamps, sample_cvd_values):
        """Test CVDResult DataFrame index handling."""
        from src.forex_bot.cvd.models import CVDResult
        
        result = CVDResult(
            timestamps=sample_timestamps,
            cvd_values=sample_cvd_values,
            symbol="INDEX_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Test DataFrame properties
        assert df.index.name is None  # Default index
        assert len(df.columns) == 2  # timestamp and cvd
        assert df.dtypes['cvd'] == np.float64

    def test_cvd_result_memory_efficiency(self):
        """Test CVDResult memory efficiency with large arrays."""
        from src.forex_bot.cvd.models import CVDResult
        
        # Test with moderately large arrays
        size = 1000
        timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC') + pd.Timedelta(seconds=i)
            for i in range(size)
        ])
        values = np.arange(size, dtype=np.float64)
        
        result = CVDResult(
            timestamps=timestamps,
            cvd_values=values,
            symbol="MEMORY_TEST",
            timeframe=1
        )
        
        # Should handle large arrays without issues
        df = result.to_dataframe()
        assert len(df) == size
        assert df.memory_usage(deep=True).sum() > 0
