"""
Additional comprehensive test coverage for global_pmi/models.py - Batch 17
Target: Push from 78% to 90%+ coverage
Focus: PMITrend and PMIDivergence classes
"""

import pytest
from datetime import datetime, date
import pandas as pd


class TestGlobalPMIModelsAdditionalBatch17:
    """Additional test class for global_pmi/models.py comprehensive coverage."""

    def test_pmi_trend_initialization(self):
        """Test PMITrend initialization."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="United States",
            pmi_type="Manufacturing",
            latest_date=date(2024, 1, 15),
            latest_value=52.5,
            trend_direction="up",
            trend_strength=0.8,
            values_3m=[50.1, 51.2, 52.5],
            values_6m=[48.5, 49.2, 50.1, 51.2, 52.5, 52.0],
            values_12m=[45.0, 46.0, 47.0, 48.0, 48.5, 49.2, 50.1, 51.2, 52.5, 52.0, 51.8, 52.2],
            avg_3m=51.27,
            avg_6m=50.58,
            avg_12m=49.46,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.country == "United States"
        assert pmi_trend.pmi_type == "Manufacturing"
        assert pmi_trend.latest_value == 52.5
        assert pmi_trend.trend_direction == "up"
        assert pmi_trend.trend_strength == 0.8

    def test_pmi_trend_economic_state_strongly_expanding(self):
        """Test PMITrend economic_state property for strongly expanding."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Manufacturing",
            latest_date=date(2024, 1, 15),
            latest_value=55.0,  # > 50
            trend_direction="up",  # up trend
            trend_strength=0.8,
            values_3m=[52.0, 53.5, 55.0],
            values_6m=[50.0, 51.0, 52.0, 53.5, 55.0, 54.5],
            values_12m=[48.0, 49.0, 50.0, 51.0, 52.0, 53.5, 55.0, 54.5, 54.8, 55.2, 55.1, 55.0],
            avg_3m=53.5,
            avg_6m=52.67,
            avg_12m=52.26,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.economic_state == "Strongly Expanding"

    def test_pmi_trend_economic_state_moderately_expanding(self):
        """Test PMITrend economic_state property for moderately expanding."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Services",
            latest_date=date(2024, 1, 15),
            latest_value=52.0,  # > 50
            trend_direction="down",  # down trend
            trend_strength=0.3,
            values_3m=[54.0, 53.0, 52.0],
            values_6m=[55.0, 54.5, 54.0, 53.0, 52.0, 52.5],
            values_12m=[56.0, 55.5, 55.0, 54.5, 54.0, 53.0, 52.0, 52.5, 52.2, 52.1, 52.0, 52.0],
            avg_3m=53.0,
            avg_6m=53.5,
            avg_12m=53.4,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        assert pmi_trend.economic_state == "Moderately Expanding"

    def test_pmi_trend_economic_state_stable_expansion(self):
        """Test PMITrend economic_state property for stable expansion."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Composite",
            latest_date=date(2024, 1, 15),
            latest_value=51.5,  # > 50
            trend_direction="flat",  # flat trend
            trend_strength=0.1,
            values_3m=[51.4, 51.5, 51.5],
            values_6m=[51.3, 51.4, 51.4, 51.5, 51.5, 51.5],
            values_12m=[51.0, 51.1, 51.2, 51.3, 51.4, 51.4, 51.5, 51.5, 51.5, 51.4, 51.5, 51.5],
            avg_3m=51.47,
            avg_6m=51.43,
            avg_12m=51.36,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.economic_state == "Stable Expansion"

    def test_pmi_trend_economic_state_improving_contraction(self):
        """Test PMITrend economic_state property for improving contraction."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Manufacturing",
            latest_date=date(2024, 1, 15),
            latest_value=48.5,  # < 50
            trend_direction="up",  # up trend
            trend_strength=0.6,
            values_3m=[46.0, 47.2, 48.5],
            values_6m=[44.0, 45.0, 46.0, 47.2, 48.5, 48.0],
            values_12m=[40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.2, 48.5, 48.0, 48.2, 48.5],
            avg_3m=47.23,
            avg_6m=46.45,
            avg_12m=45.12,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.economic_state == "Improving Contraction"

    def test_pmi_trend_economic_state_deepening_contraction(self):
        """Test PMITrend economic_state property for deepening contraction."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Services",
            latest_date=date(2024, 1, 15),
            latest_value=45.0,  # < 50
            trend_direction="down",  # down trend
            trend_strength=0.7,
            values_3m=[48.0, 46.5, 45.0],
            values_6m=[50.0, 49.0, 48.0, 46.5, 45.0, 44.5],
            values_12m=[52.0, 51.0, 50.0, 49.0, 48.0, 46.5, 45.0, 44.5, 44.0, 44.2, 44.8, 45.0],
            avg_3m=46.5,
            avg_6m=47.17,
            avg_12m=47.33,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        assert pmi_trend.economic_state == "Deepening Contraction"

    def test_pmi_trend_economic_state_stable_contraction(self):
        """Test PMITrend economic_state property for stable contraction."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Composite",
            latest_date=date(2024, 1, 15),
            latest_value=47.5,  # < 50
            trend_direction="flat",  # flat trend
            trend_strength=0.1,
            values_3m=[47.4, 47.5, 47.5],
            values_6m=[47.3, 47.4, 47.4, 47.5, 47.5, 47.5],
            values_12m=[47.0, 47.1, 47.2, 47.3, 47.4, 47.4, 47.5, 47.5, 47.5, 47.4, 47.5, 47.5],
            avg_3m=47.47,
            avg_6m=47.43,
            avg_12m=47.36,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.economic_state == "Stable Contraction"

    def test_pmi_divergence_initialization(self):
        """Test PMIDivergence initialization."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="United States",
            currency="USD",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=50.0,
            end_pmi=52.5,
            pmi_change=2.5,
            start_currency_value=1.0800,
            end_currency_value=1.0750,
            currency_change=-0.0050,
            divergence_type="positive",
            strength=0.7
        )
        
        assert divergence.country == "United States"
        assert divergence.currency == "USD"
        assert divergence.start_pmi == 50.0
        assert divergence.end_pmi == 52.5
        assert divergence.pmi_change == 2.5
        assert divergence.divergence_type == "positive"
        assert divergence.strength == 0.7

    def test_pmi_divergence_positive_divergence_property(self):
        """Test PMIDivergence is_positive_divergence property."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        # Positive divergence
        divergence_pos = PMIDivergence(
            country="Test",
            currency="EUR",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=48.0,
            end_pmi=52.0,
            pmi_change=4.0,
            start_currency_value=1.1000,
            end_currency_value=1.0950,
            currency_change=-0.0050,
            divergence_type="positive",
            strength=0.8
        )
        
        assert divergence_pos.is_positive_divergence is True
        assert divergence_pos.is_negative_divergence is False

    def test_pmi_divergence_negative_divergence_property(self):
        """Test PMIDivergence is_negative_divergence property."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        # Negative divergence
        divergence_neg = PMIDivergence(
            country="Test",
            currency="GBP",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=52.0,
            end_pmi=48.0,
            pmi_change=-4.0,
            start_currency_value=1.2500,
            end_currency_value=1.2600,
            currency_change=0.0100,
            divergence_type="negative",
            strength=0.6
        )
        
        assert divergence_neg.is_positive_divergence is False
        assert divergence_neg.is_negative_divergence is True

    def test_pmi_divergence_trading_signal_buy(self):
        """Test PMIDivergence trading_signal property for BUY signal."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="Test",
            currency="JPY",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=47.0,
            end_pmi=53.0,
            pmi_change=6.0,
            start_currency_value=110.0,
            end_currency_value=108.0,
            currency_change=-2.0,
            divergence_type="positive",
            strength=0.8  # > 0.5
        )
        
        assert divergence.trading_signal == "BUY"

    def test_pmi_divergence_trading_signal_sell(self):
        """Test PMIDivergence trading_signal property for SELL signal."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="Test",
            currency="CAD",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=53.0,
            end_pmi=47.0,
            pmi_change=-6.0,
            start_currency_value=1.3500,
            end_currency_value=1.3300,
            currency_change=-0.0200,
            divergence_type="negative",
            strength=0.7  # > 0.5
        )
        
        assert divergence.trading_signal == "SELL"

    def test_pmi_divergence_trading_signal_none_weak_strength(self):
        """Test PMIDivergence trading_signal property for None (weak strength)."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="Test",
            currency="AUD",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=50.0,
            end_pmi=52.0,
            pmi_change=2.0,
            start_currency_value=0.7500,
            end_currency_value=0.7480,
            currency_change=-0.0020,
            divergence_type="positive",
            strength=0.3  # <= 0.5
        )
        
        assert divergence.trading_signal is None

    def test_pmi_divergence_trading_signal_none_negative_weak(self):
        """Test PMIDivergence trading_signal property for None (negative weak)."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="Test",
            currency="CHF",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=52.0,
            end_pmi=50.0,
            pmi_change=-2.0,
            start_currency_value=0.9200,
            end_currency_value=0.9250,
            currency_change=0.0050,
            divergence_type="negative",
            strength=0.4  # <= 0.5
        )
        
        assert divergence.trading_signal is None

    def test_pmi_trend_crossed_thresholds_default(self):
        """Test PMITrend crossed threshold default values."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Manufacturing",
            latest_date=date(2024, 1, 15),
            latest_value=52.0,
            trend_direction="up",
            trend_strength=0.5,
            values_3m=[50.0, 51.0, 52.0],
            values_6m=[48.0, 49.0, 50.0, 51.0, 52.0, 51.5],
            values_12m=[45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 51.5, 51.8, 52.0, 52.0],
            avg_3m=51.0,
            avg_6m=50.25,
            avg_12m=49.36,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert pmi_trend.crossed_above_50 is False
        assert pmi_trend.crossed_below_50 is False

    def test_pmi_trend_crossed_thresholds_explicit(self):
        """Test PMITrend crossed threshold explicit values."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test",
            pmi_type="Services",
            latest_date=date(2024, 1, 15),
            latest_value=51.0,
            trend_direction="up",
            trend_strength=0.6,
            values_3m=[49.0, 50.2, 51.0],
            values_6m=[47.0, 48.0, 49.0, 50.2, 51.0, 50.8],
            values_12m=[44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.2, 51.0, 50.8, 50.9, 51.0, 51.0],
            avg_3m=50.07,
            avg_6m=49.33,
            avg_12m=48.66,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True,
            crossed_above_50=True,
            crossed_below_50=False
        )
        
        assert pmi_trend.crossed_above_50 is True
        assert pmi_trend.crossed_below_50 is False

    def test_pmi_divergence_string_representation(self):
        """Test PMIDivergence string representation."""
        from src.forex_bot.global_pmi.models import PMIDivergence
        
        divergence = PMIDivergence(
            country="Test String",
            currency="TEST",
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            start_pmi=50.0,
            end_pmi=52.0,
            pmi_change=2.0,
            start_currency_value=1.0000,
            end_currency_value=0.9950,
            currency_change=-0.0050,
            divergence_type="positive",
            strength=0.5
        )
        
        # Test that string representation works
        str_repr = str(divergence)
        assert isinstance(str_repr, str)

    def test_pmi_trend_string_representation(self):
        """Test PMITrend string representation."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_trend = PMITrend(
            country="Test String",
            pmi_type="Test Type",
            latest_date=date(2024, 1, 15),
            latest_value=50.0,
            trend_direction="flat",
            trend_strength=0.0,
            values_3m=[50.0, 50.0, 50.0],
            values_6m=[50.0, 50.0, 50.0, 50.0, 50.0, 50.0],
            values_12m=[50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0],
            avg_3m=50.0,
            avg_6m=50.0,
            avg_12m=50.0,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        # Test that string representation works
        str_repr = str(pmi_trend)
        assert isinstance(str_repr, str)