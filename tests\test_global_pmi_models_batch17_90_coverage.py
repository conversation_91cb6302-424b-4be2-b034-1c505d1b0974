"""
Comprehensive test coverage for global_pmi/models.py - Batch 17
Target: Push from 73% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
from datetime import datetime, date
import pandas as pd


class TestGlobalPMIModelsBatch17Coverage:
    """Test class for global_pmi/models.py comprehensive coverage."""

    def test_pmi_data_initialization(self):
        """Test PMIData initialization."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="United States",
            date=date(2024, 1, 15),
            pmi_value=52.5,
            pmi_type="Manufacturing"
        )
        
        assert pmi_data.country == "United States"
        assert pmi_data.date == date(2024, 1, 15)
        assert pmi_data.pmi_value == 52.5
        assert pmi_data.pmi_type == "Manufacturing"

    def test_pmi_data_default_values(self):
        """Test PMIData default values."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Germany",
            date=date(2024, 1, 15),
            pmi_value=48.5,
            pmi_type="Services"
        )
        
        assert pmi_data.previous_value is None
        assert pmi_data.change is None
        assert pmi_data.forecast_value is None
        assert pmi_data.is_expansion is False
        assert pmi_data.is_contraction is True  # 48.5 < 50, so it's contraction

    def test_pmi_data_post_init_expansion(self):
        """Test PMIData __post_init__ with expansion (PMI > 50)."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Japan",
            date=date(2024, 1, 15),
            pmi_value=55.2,
            pmi_type="Manufacturing"
        )
        
        assert pmi_data.is_expansion is True
        assert pmi_data.is_contraction is False

    def test_pmi_data_post_init_contraction(self):
        """Test PMIData __post_init__ with contraction (PMI < 50)."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="United Kingdom",
            date=date(2024, 1, 15),
            pmi_value=45.8,
            pmi_type="Services"
        )
        
        assert pmi_data.is_expansion is False
        assert pmi_data.is_contraction is True

    def test_pmi_data_post_init_neutral(self):
        """Test PMIData __post_init__ with neutral PMI (exactly 50)."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="France",
            date=date(2024, 1, 15),
            pmi_value=50.0,
            pmi_type="Composite"
        )
        
        assert pmi_data.is_expansion is False
        assert pmi_data.is_contraction is False

    def test_pmi_data_change_calculation(self):
        """Test PMIData change calculation in __post_init__."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Italy",
            date=date(2024, 1, 15),
            pmi_value=52.3,
            pmi_type="Manufacturing",
            previous_value=49.8
        )
        
        expected_change = 52.3 - 49.8
        assert pmi_data.change == expected_change
        assert abs(pmi_data.change - 2.5) < 0.001

    def test_pmi_data_change_not_calculated_when_provided(self):
        """Test PMIData change not recalculated when already provided."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Spain",
            date=date(2024, 1, 15),
            pmi_value=51.5,
            pmi_type="Services",
            previous_value=48.0,
            change=3.0  # Explicitly provided
        )
        
        # Should keep the provided change value
        assert pmi_data.change == 3.0

    def test_pmi_data_change_not_calculated_without_previous(self):
        """Test PMIData change not calculated without previous value."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Netherlands",
            date=date(2024, 1, 15),
            pmi_value=53.2,
            pmi_type="Manufacturing"
        )
        
        assert pmi_data.change is None

    def test_pmi_data_with_all_optional_parameters(self):
        """Test PMIData with all optional parameters."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Canada",
            date=date(2024, 1, 15),
            pmi_value=54.1,
            pmi_type="Composite",
            previous_value=52.0,
            change=2.1,
            forecast_value=53.5
        )
        
        assert pmi_data.previous_value == 52.0
        assert pmi_data.change == 2.1
        assert pmi_data.forecast_value == 53.5
        assert pmi_data.is_expansion is True
        assert pmi_data.is_contraction is False

    def test_pmi_data_different_pmi_types(self):
        """Test PMIData with different PMI types."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_types = ["Manufacturing", "Services", "Composite"]
        
        for pmi_type in pmi_types:
            pmi_data = PMIData(
                country="Australia",
                date=date(2024, 1, 15),
                pmi_value=51.0,
                pmi_type=pmi_type
            )
            assert pmi_data.pmi_type == pmi_type

    def test_pmi_data_extreme_values(self):
        """Test PMIData with extreme PMI values."""
        from src.forex_bot.global_pmi.models import PMIData
        
        # Very high PMI
        pmi_high = PMIData(
            country="Test High",
            date=date(2024, 1, 15),
            pmi_value=99.9,
            pmi_type="Manufacturing"
        )
        
        assert pmi_high.is_expansion is True
        assert pmi_high.is_contraction is False
        
        # Very low PMI
        pmi_low = PMIData(
            country="Test Low",
            date=date(2024, 1, 15),
            pmi_value=0.1,
            pmi_type="Services"
        )
        
        assert pmi_low.is_expansion is False
        assert pmi_low.is_contraction is True

    def test_pmi_data_negative_values(self):
        """Test PMIData with negative values."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test Negative",
            date=date(2024, 1, 15),
            pmi_value=-5.0,
            pmi_type="Manufacturing",
            previous_value=-10.0,
            change=5.0,
            forecast_value=-3.0
        )
        
        assert pmi_data.pmi_value == -5.0
        assert pmi_data.previous_value == -10.0
        assert pmi_data.change == 5.0
        assert pmi_data.forecast_value == -3.0
        assert pmi_data.is_expansion is False
        assert pmi_data.is_contraction is True

    def test_pmi_data_zero_values(self):
        """Test PMIData with zero values."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test Zero",
            date=date(2024, 1, 15),
            pmi_value=0.0,
            pmi_type="Services",
            previous_value=0.0,
            change=0.0,
            forecast_value=0.0
        )
        
        assert pmi_data.pmi_value == 0.0
        assert pmi_data.previous_value == 0.0
        assert pmi_data.change == 0.0
        assert pmi_data.forecast_value == 0.0
        assert pmi_data.is_expansion is False
        assert pmi_data.is_contraction is True

    def test_pmi_data_decimal_precision(self):
        """Test PMIData with high decimal precision."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test Precision",
            date=date(2024, 1, 15),
            pmi_value=50.123456789,
            pmi_type="Manufacturing",
            previous_value=49.987654321
        )
        
        expected_change = 50.123456789 - 49.987654321
        assert abs(pmi_data.change - expected_change) < 1e-10
        assert pmi_data.is_expansion is True

    def test_pmi_data_string_representation(self):
        """Test PMIData string representation."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test String",
            date=date(2024, 1, 15),
            pmi_value=52.5,
            pmi_type="Manufacturing"
        )
        
        # Test that string representation works
        str_repr = str(pmi_data)
        assert isinstance(str_repr, str)

    def test_pmi_data_edge_case_boundary_values(self):
        """Test PMIData with boundary values around 50."""
        from src.forex_bot.global_pmi.models import PMIData
        
        # Just above 50
        pmi_above = PMIData(
            country="Test Above",
            date=date(2024, 1, 15),
            pmi_value=50.000001,
            pmi_type="Services"
        )
        
        assert pmi_above.is_expansion is True
        assert pmi_above.is_contraction is False
        
        # Just below 50
        pmi_below = PMIData(
            country="Test Below",
            date=date(2024, 1, 15),
            pmi_value=49.999999,
            pmi_type="Manufacturing"
        )
        
        assert pmi_below.is_expansion is False
        assert pmi_below.is_contraction is True

    def test_pmi_data_different_date_formats(self):
        """Test PMIData with different date objects."""
        from src.forex_bot.global_pmi.models import PMIData
        
        # Test with different date objects
        dates = [
            date(2024, 1, 1),
            date(2024, 12, 31),
            date(2000, 1, 1),
            date(2099, 12, 31)
        ]
        
        for test_date in dates:
            pmi_data = PMIData(
                country="Test Date",
                date=test_date,
                pmi_value=51.0,
                pmi_type="Composite"
            )
            assert pmi_data.date == test_date

    def test_pmi_data_type_consistency(self):
        """Test PMIData type consistency."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test Types",
            date=date(2024, 1, 15),
            pmi_value=52.5,
            pmi_type="Manufacturing",
            previous_value=50.0,
            change=2.5,
            forecast_value=53.0
        )
        
        assert isinstance(pmi_data.country, str)
        assert isinstance(pmi_data.date, date)
        assert isinstance(pmi_data.pmi_value, float)
        assert isinstance(pmi_data.pmi_type, str)
        assert isinstance(pmi_data.previous_value, float)
        assert isinstance(pmi_data.change, float)
        assert isinstance(pmi_data.forecast_value, float)
        assert isinstance(pmi_data.is_expansion, bool)
        assert isinstance(pmi_data.is_contraction, bool)

    def test_pmi_data_change_calculation_edge_cases(self):
        """Test PMIData change calculation edge cases."""
        from src.forex_bot.global_pmi.models import PMIData
        
        # Test with very small differences
        pmi_data_small = PMIData(
            country="Test Small Change",
            date=date(2024, 1, 15),
            pmi_value=50.000001,
            pmi_type="Services",
            previous_value=50.000000
        )
        
        expected_change = 50.000001 - 50.000000
        assert abs(pmi_data_small.change - expected_change) < 1e-15
        
        # Test with large differences
        pmi_data_large = PMIData(
            country="Test Large Change",
            date=date(2024, 1, 15),
            pmi_value=80.0,
            pmi_type="Manufacturing",
            previous_value=20.0
        )
        
        assert pmi_data_large.change == 60.0

    def test_pmi_data_country_variations(self):
        """Test PMIData with various country names."""
        from src.forex_bot.global_pmi.models import PMIData
        
        countries = [
            "United States",
            "United Kingdom", 
            "European Union",
            "China",
            "Japan",
            "Germany",
            "France",
            "Italy",
            "Spain",
            "Canada",
            "Australia",
            "Brazil",
            "India",
            "Russia",
            "South Korea"
        ]
        
        for country in countries:
            pmi_data = PMIData(
                country=country,
                date=date(2024, 1, 15),
                pmi_value=51.0,
                pmi_type="Manufacturing"
            )
            assert pmi_data.country == country

    def test_pmi_data_immutability_after_init(self):
        """Test PMIData behavior after initialization."""
        from src.forex_bot.global_pmi.models import PMIData
        
        pmi_data = PMIData(
            country="Test Immutable",
            date=date(2024, 1, 15),
            pmi_value=52.0,
            pmi_type="Services",
            previous_value=50.0
        )
        
        # Verify initial state
        assert pmi_data.change == 2.0
        assert pmi_data.is_expansion is True
        
        # Test that we can modify attributes (dataclass is mutable by default)
        pmi_data.pmi_value = 48.0
        assert pmi_data.pmi_value == 48.0
        
        # Note: is_expansion and is_contraction are set in __post_init__
        # and won't automatically update when pmi_value changes

    def test_pmi_data_comprehensive_scenario(self):
        """Test PMIData with comprehensive real-world scenario."""
        from src.forex_bot.global_pmi.models import PMIData
        
        # Simulate a real PMI release
        pmi_data = PMIData(
            country="United States",
            date=date(2024, 3, 1),
            pmi_value=52.2,
            pmi_type="Manufacturing",
            previous_value=49.1,
            forecast_value=50.8
        )
        
        # Verify all calculations and flags
        assert abs(pmi_data.change - 3.1) < 1e-10  # 52.2 - 49.1
        assert pmi_data.is_expansion is True  # 52.2 > 50
        assert pmi_data.is_contraction is False
        assert pmi_data.pmi_value > pmi_data.forecast_value  # Beat forecast
        assert pmi_data.pmi_value > pmi_data.previous_value  # Improved from previous