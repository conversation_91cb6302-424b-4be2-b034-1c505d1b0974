"""
Comprehensive test coverage for global_pmi/models.py - Batch 4
Target: Push from 73% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import pandas as pd
from datetime import datetime, date, timezone
from unittest.mock import patch, MagicMock
import copy


class TestGlobalPMIModelsBatch4Coverage:
    """Test class for global_pmi/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_date(self):
        """Sample date for testing."""
        return date(2024, 1, 15)

    @pytest.fixture
    def sample_pmi_data(self, sample_date):
        """Sample PMI data for testing."""
        return {
            'country': 'United States',
            'date': sample_date,
            'pmi_value': 52.5,
            'pmi_type': 'Manufacturing',
            'previous_value': 51.2,
            'forecast_value': 52.0
        }

    def test_pmi_data_initialization(self, sample_pmi_data):
        """Test PMIData initialization."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(**sample_pmi_data)

        assert pmi.country == 'United States'
        assert pmi.date == sample_pmi_data['date']
        assert pmi.pmi_value == 52.5
        assert pmi.pmi_type == 'Manufacturing'
        assert pmi.previous_value == 51.2
        assert pmi.forecast_value == 52.0

    def test_pmi_data_post_init_expansion(self, sample_date):
        """Test PMIData __post_init__ with expansion (PMI > 50)."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Germany',
            date=sample_date,
            pmi_value=55.0,
            pmi_type='Services'
        )

        assert pmi.is_expansion is True
        assert pmi.is_contraction is False

    def test_pmi_data_post_init_contraction(self, sample_date):
        """Test PMIData __post_init__ with contraction (PMI < 50)."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='France',
            date=sample_date,
            pmi_value=45.0,
            pmi_type='Manufacturing'
        )

        assert pmi.is_expansion is False
        assert pmi.is_contraction is True

    def test_pmi_data_post_init_neutral(self, sample_date):
        """Test PMIData __post_init__ with neutral (PMI = 50)."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Japan',
            date=sample_date,
            pmi_value=50.0,
            pmi_type='Composite'
        )

        assert pmi.is_expansion is False
        assert pmi.is_contraction is False

    def test_pmi_data_change_calculation(self, sample_date):
        """Test PMIData change calculation in __post_init__."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='UK',
            date=sample_date,
            pmi_value=53.5,
            pmi_type='Services',
            previous_value=51.0
        )

        assert pmi.change == 2.5

    def test_pmi_data_change_provided(self, sample_date):
        """Test PMIData when change is already provided."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Canada',
            date=sample_date,
            pmi_value=54.0,
            pmi_type='Manufacturing',
            previous_value=52.0,
            change=1.5  # Provided change (different from calculated)
        )

        # Should keep the provided change, not calculate
        assert pmi.change == 1.5

    def test_pmi_data_no_previous_value(self, sample_date):
        """Test PMIData without previous value."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Australia',
            date=sample_date,
            pmi_value=56.0,
            pmi_type='Services'
        )

        assert pmi.previous_value is None
        assert pmi.change is None

    def test_pmi_data_extreme_values(self, sample_date):
        """Test PMIData with extreme values."""
        from src.forex_bot.global_pmi.models import PMIData

        # Test very low PMI
        low_pmi = PMIData(
            country='Test Low',
            date=sample_date,
            pmi_value=0.1,
            pmi_type='Manufacturing'
        )
        assert low_pmi.is_contraction is True

        # Test very high PMI
        high_pmi = PMIData(
            country='Test High',
            date=sample_date,
            pmi_value=99.9,
            pmi_type='Services'
        )
        assert high_pmi.is_expansion is True

    def test_pmi_data_edge_case_values(self, sample_date):
        """Test PMIData with edge case values."""
        from src.forex_bot.global_pmi.models import PMIData

        # Test PMI exactly at 50
        neutral_pmi = PMIData(
            country='Neutral',
            date=sample_date,
            pmi_value=50.0,
            pmi_type='Composite'
        )
        assert neutral_pmi.is_expansion is False
        assert neutral_pmi.is_contraction is False

        # Test PMI just above 50
        just_expansion = PMIData(
            country='Just Expansion',
            date=sample_date,
            pmi_value=50.1,
            pmi_type='Manufacturing'
        )
        assert just_expansion.is_expansion is True

        # Test PMI just below 50
        just_contraction = PMIData(
            country='Just Contraction',
            date=sample_date,
            pmi_value=49.9,
            pmi_type='Services'
        )
        assert just_contraction.is_contraction is True

    def test_pmi_data_different_types(self, sample_date):
        """Test PMIData with different PMI types."""
        from src.forex_bot.global_pmi.models import PMIData

        types = ['Manufacturing', 'Services', 'Composite']

        for pmi_type in types:
            pmi = PMIData(
                country='Multi Type',
                date=sample_date,
                pmi_value=52.0,
                pmi_type=pmi_type
            )
            assert pmi.pmi_type == pmi_type
            assert pmi.is_expansion is True

    def test_pmi_data_negative_change(self, sample_date):
        """Test PMIData with negative change."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Declining',
            date=sample_date,
            pmi_value=48.0,
            pmi_type='Manufacturing',
            previous_value=52.0
        )

        assert pmi.change == -4.0
        assert pmi.is_contraction is True

    def test_pmi_data_zero_change(self, sample_date):
        """Test PMIData with zero change."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(
            country='Stable',
            date=sample_date,
            pmi_value=51.5,
            pmi_type='Services',
            previous_value=51.5
        )

        assert pmi.change == 0.0
        assert pmi.is_expansion is True

    def test_pmi_data_string_representations(self, sample_pmi_data):
        """Test PMIData string representations."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(**sample_pmi_data)

        # Test string representation
        str_repr = str(pmi)
        assert isinstance(str_repr, str)
        assert "PMIData" in str_repr

    def test_pmi_data_copy_operations(self, sample_pmi_data):
        """Test PMIData copy operations."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi = PMIData(**sample_pmi_data)

        # Test shallow copy
        pmi_copy = copy.copy(pmi)
        assert pmi_copy.country == pmi.country
        assert pmi_copy.pmi_value == pmi.pmi_value

        # Test deep copy
        pmi_deepcopy = copy.deepcopy(pmi)
        assert pmi_deepcopy.country == pmi.country
        assert pmi_deepcopy.pmi_value == pmi.pmi_value

    def test_pmi_data_equality_comparison(self, sample_date):
        """Test PMIData equality comparison."""
        from src.forex_bot.global_pmi.models import PMIData

        pmi1 = PMIData(
            country='Equal Test',
            date=sample_date,
            pmi_value=52.0,
            pmi_type='Manufacturing'
        )

        pmi2 = PMIData(
            country='Equal Test',
            date=sample_date,
            pmi_value=52.0,
            pmi_type='Manufacturing'
        )

        # Test individual attributes for equality
        assert pmi1.country == pmi2.country
        assert pmi1.date == pmi2.date
        assert pmi1.pmi_value == pmi2.pmi_value
        assert pmi1.pmi_type == pmi2.pmi_type

    def test_pmi_trend_initialization(self, sample_date):
        """Test PMITrend initialization."""
        from src.forex_bot.global_pmi.models import PMITrend

        trend = PMITrend(
            country='Trend Test',
            pmi_type='Manufacturing',
            latest_date=sample_date,
            latest_value=52.5,
            trend_direction='up',
            trend_strength=0.7,
            values_3m=[51.0, 51.5, 52.5],
            values_6m=[50.0, 50.5, 51.0, 51.5, 52.0, 52.5],
            values_12m=[49.0, 49.5, 50.0, 50.2, 50.5, 50.8, 51.0, 51.2, 51.5, 51.8, 52.0, 52.5],
            avg_3m=51.67,
            avg_6m=51.25,
            avg_12m=50.92,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )

        assert trend.country == 'Trend Test'
        assert trend.pmi_type == 'Manufacturing'
        assert trend.latest_date == sample_date
        assert trend.latest_value == 52.5
        assert trend.trend_direction == 'up'
        assert trend.is_improving is True

    def test_pmi_data_with_forecast_accuracy(self, sample_date):
        """Test PMIData with forecast accuracy scenarios."""
        from src.forex_bot.global_pmi.models import PMIData

        # Actual matches forecast
        accurate_pmi = PMIData(
            country='Accurate',
            date=sample_date,
            pmi_value=52.0,
            pmi_type='Services',
            forecast_value=52.0
        )
        assert accurate_pmi.pmi_value == accurate_pmi.forecast_value

        # Actual exceeds forecast
        beat_forecast = PMIData(
            country='Beat Forecast',
            date=sample_date,
            pmi_value=53.5,
            pmi_type='Manufacturing',
            forecast_value=52.0
        )
        assert beat_forecast.pmi_value > beat_forecast.forecast_value

        # Actual misses forecast
        miss_forecast = PMIData(
            country='Miss Forecast',
            date=sample_date,
            pmi_value=50.5,
            pmi_type='Composite',
            forecast_value=52.0
        )
        assert miss_forecast.pmi_value < miss_forecast.forecast_value

    def test_pmi_data_historical_dates(self):
        """Test PMIData with various historical dates."""
        from src.forex_bot.global_pmi.models import PMIData

        # Test with very old date
        old_date = date(1990, 1, 1)
        old_pmi = PMIData(
            country='Historical',
            date=old_date,
            pmi_value=45.0,
            pmi_type='Manufacturing'
        )
        assert old_pmi.date == old_date
        assert old_pmi.is_contraction is True

        # Test with future date
        future_date = date(2030, 12, 31)
        future_pmi = PMIData(
            country='Future',
            date=future_date,
            pmi_value=55.0,
            pmi_type='Services'
        )
        assert future_pmi.date == future_date
        assert future_pmi.is_expansion is True

    def test_pmi_data_country_variations(self, sample_date):
        """Test PMIData with various country names."""
        from src.forex_bot.global_pmi.models import PMIData

        countries = [
            'United States',
            'United Kingdom',
            'Euro Area',
            'China',
            'Japan',
            'Germany',
            'France',
            'Italy',
            'Spain',
            'Canada',
            'Australia'
        ]

        for country in countries:
            pmi = PMIData(
                country=country,
                date=sample_date,
                pmi_value=52.0,
                pmi_type='Manufacturing'
            )
            assert pmi.country == country
            assert pmi.is_expansion is True

    def test_pmi_data_decimal_precision(self, sample_date):
        """Test PMIData with various decimal precisions."""
        from src.forex_bot.global_pmi.models import PMIData

        # Test with high precision
        precise_pmi = PMIData(
            country='Precise',
            date=sample_date,
            pmi_value=52.*********,
            pmi_type='Services',
            previous_value=51.*********
        )

        assert precise_pmi.pmi_value == 52.*********
        assert precise_pmi.previous_value == 51.*********
        assert abs(precise_pmi.change - 0.*********) < 1e-9

    def test_pmi_data_type_validation(self, sample_date):
        """Test PMIData type validation scenarios."""
        from src.forex_bot.global_pmi.models import PMIData

        # Test with empty string country
        empty_country = PMIData(
            country='',
            date=sample_date,
            pmi_value=50.0,
            pmi_type='Manufacturing'
        )
        assert empty_country.country == ''

        # Test with special characters in country name
        special_country = PMIData(
            country='São Paulo (Brazil)',
            date=sample_date,
            pmi_value=48.5,
            pmi_type='Services'
        )
        assert special_country.country == 'São Paulo (Brazil)'
        assert special_country.is_contraction is True
