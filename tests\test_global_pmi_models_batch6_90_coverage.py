"""
Comprehensive test coverage for global_pmi/models.py - Batch 6
Target: Push from 78% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import pandas as pd
from datetime import datetime, date, timezone
from unittest.mock import patch, MagicMock
import copy


class TestGlobalPMIModelsBatch6Coverage:
    """Test class for global_pmi/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_date(self):
        """Sample date for testing."""
        return date(2024, 1, 15)

    @pytest.fixture
    def complete_pmi_trend_data(self, sample_date):
        """Complete PMI trend data for testing."""
        return {
            'country': 'United States',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 52.5,
            'trend_direction': 'up',
            'trend_strength': 0.7,
            'values_3m': [51.0, 51.5, 52.5],
            'values_6m': [50.0, 50.5, 51.0, 51.5, 52.0, 52.5],
            'values_12m': [49.0, 49.5, 50.0, 50.2, 50.5, 50.8, 51.0, 51.2, 51.5, 51.8, 52.0, 52.5],
            'avg_3m': 51.67,
            'avg_6m': 51.25,
            'avg_12m': 50.92,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }

    def test_pmi_trend_complete_initialization(self, complete_pmi_trend_data):
        """Test PMITrend complete initialization."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        trend = PMITrend(**complete_pmi_trend_data)
        
        assert trend.country == 'United States'
        assert trend.pmi_type == 'Manufacturing'
        assert trend.latest_value == 52.5
        assert trend.trend_direction == 'up'
        assert trend.trend_strength == 0.7
        assert len(trend.values_3m) == 3
        assert len(trend.values_6m) == 6
        assert len(trend.values_12m) == 12
        assert trend.is_improving is True
        assert trend.is_deteriorating is False

    def test_pmi_trend_minimal_initialization(self, sample_date):
        """Test PMITrend with minimal required fields."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        minimal_data = {
            'country': 'Germany',
            'pmi_type': 'Services',
            'latest_date': sample_date,
            'latest_value': 48.5,
            'trend_direction': 'down',
            'trend_strength': 0.3,
            'values_3m': [49.5, 49.0, 48.5],
            'values_6m': [51.0, 50.5, 50.0, 49.5, 49.0, 48.5],
            'values_12m': [52.0, 51.8, 51.5, 51.2, 51.0, 50.8, 50.5, 50.2, 50.0, 49.5, 49.0, 48.5],
            'avg_3m': 49.0,
            'avg_6m': 49.75,
            'avg_12m': 50.42,
            'is_improving': False,
            'is_deteriorating': True,
            'is_above_3m_avg': False,
            'is_above_6m_avg': False,
            'is_above_12m_avg': False
        }
        
        trend = PMITrend(**minimal_data)
        
        assert trend.country == 'Germany'
        assert trend.latest_value == 48.5
        assert trend.is_improving is False
        assert trend.is_deteriorating is True

    def test_pmi_trend_different_directions(self, sample_date):
        """Test PMITrend with different trend directions."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        base_data = {
            'country': 'Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 50.0,
            'trend_strength': 0.5,
            'values_3m': [50.0, 50.0, 50.0],
            'values_6m': [50.0, 50.0, 50.0, 50.0, 50.0, 50.0],
            'values_12m': [50.0] * 12,
            'avg_3m': 50.0,
            'avg_6m': 50.0,
            'avg_12m': 50.0,
            'is_improving': False,
            'is_deteriorating': False,
            'is_above_3m_avg': False,
            'is_above_6m_avg': False,
            'is_above_12m_avg': False
        }
        
        directions = ['up', 'down', 'stable', 'volatile']
        
        for direction in directions:
            data = base_data.copy()
            data['trend_direction'] = direction
            
            trend = PMITrend(**data)
            assert trend.trend_direction == direction

    def test_pmi_trend_different_strengths(self, sample_date):
        """Test PMITrend with different trend strengths."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        base_data = {
            'country': 'Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 50.0,
            'trend_direction': 'up',
            'values_3m': [50.0, 50.0, 50.0],
            'values_6m': [50.0, 50.0, 50.0, 50.0, 50.0, 50.0],
            'values_12m': [50.0] * 12,
            'avg_3m': 50.0,
            'avg_6m': 50.0,
            'avg_12m': 50.0,
            'is_improving': False,
            'is_deteriorating': False,
            'is_above_3m_avg': False,
            'is_above_6m_avg': False,
            'is_above_12m_avg': False
        }
        
        strengths = [0.0, 0.1, 0.5, 0.9, 1.0]
        
        for strength in strengths:
            data = base_data.copy()
            data['trend_strength'] = strength
            
            trend = PMITrend(**data)
            assert trend.trend_strength == strength

    def test_pmi_trend_extreme_values(self, sample_date):
        """Test PMITrend with extreme PMI values."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        extreme_data = {
            'country': 'Extreme Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 99.9,
            'trend_direction': 'up',
            'trend_strength': 1.0,
            'values_3m': [99.7, 99.8, 99.9],
            'values_6m': [99.4, 99.5, 99.6, 99.7, 99.8, 99.9],
            'values_12m': [99.0, 99.1, 99.2, 99.3, 99.4, 99.5, 99.6, 99.7, 99.8, 99.9, 99.9, 99.9],
            'avg_3m': 99.8,
            'avg_6m': 99.65,
            'avg_12m': 99.45,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        trend = PMITrend(**extreme_data)
        assert trend.latest_value == 99.9
        assert trend.avg_3m == 99.8

    def test_pmi_trend_very_low_values(self, sample_date):
        """Test PMITrend with very low PMI values."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        low_data = {
            'country': 'Low Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 0.1,
            'trend_direction': 'down',
            'trend_strength': 0.9,
            'values_3m': [0.3, 0.2, 0.1],
            'values_6m': [0.6, 0.5, 0.4, 0.3, 0.2, 0.1],
            'values_12m': [1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],
            'avg_3m': 0.2,
            'avg_6m': 0.35,
            'avg_12m': 0.65,
            'is_improving': False,
            'is_deteriorating': True,
            'is_above_3m_avg': False,
            'is_above_6m_avg': False,
            'is_above_12m_avg': False
        }
        
        trend = PMITrend(**low_data)
        assert trend.latest_value == 0.1
        assert trend.is_deteriorating is True

    def test_pmi_trend_different_list_lengths(self, sample_date):
        """Test PMITrend with different value list lengths."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        # Test with shorter lists
        short_data = {
            'country': 'Short Test',
            'pmi_type': 'Services',
            'latest_date': sample_date,
            'latest_value': 52.0,
            'trend_direction': 'up',
            'trend_strength': 0.6,
            'values_3m': [51.0, 52.0],  # Only 2 values instead of 3
            'values_6m': [50.0, 50.5, 51.0, 51.5, 52.0],  # Only 5 values instead of 6
            'values_12m': [49.0, 49.5, 50.0, 50.5, 51.0, 51.5, 52.0],  # Only 7 values instead of 12
            'avg_3m': 51.5,
            'avg_6m': 51.0,
            'avg_12m': 50.5,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        trend = PMITrend(**short_data)
        assert len(trend.values_3m) == 2
        assert len(trend.values_6m) == 5
        assert len(trend.values_12m) == 7

    def test_pmi_trend_boolean_combinations(self, sample_date):
        """Test PMITrend with different boolean combinations."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        base_data = {
            'country': 'Boolean Test',
            'pmi_type': 'Composite',
            'latest_date': sample_date,
            'latest_value': 50.0,
            'trend_direction': 'stable',
            'trend_strength': 0.0,
            'values_3m': [50.0, 50.0, 50.0],
            'values_6m': [50.0] * 6,
            'values_12m': [50.0] * 12,
            'avg_3m': 50.0,
            'avg_6m': 50.0,
            'avg_12m': 50.0
        }
        
        boolean_combinations = [
            (True, False, True, True, True),
            (False, True, False, False, False),
            (True, True, True, False, False),
            (False, False, False, True, True)
        ]
        
        for improving, deteriorating, above_3m, above_6m, above_12m in boolean_combinations:
            data = base_data.copy()
            data.update({
                'is_improving': improving,
                'is_deteriorating': deteriorating,
                'is_above_3m_avg': above_3m,
                'is_above_6m_avg': above_6m,
                'is_above_12m_avg': above_12m
            })
            
            trend = PMITrend(**data)
            assert trend.is_improving == improving
            assert trend.is_deteriorating == deteriorating
            assert trend.is_above_3m_avg == above_3m
            assert trend.is_above_6m_avg == above_6m
            assert trend.is_above_12m_avg == above_12m

    def test_pmi_trend_copy_operations(self, complete_pmi_trend_data):
        """Test PMITrend copy operations."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        trend = PMITrend(**complete_pmi_trend_data)
        
        # Test shallow copy
        trend_copy = copy.copy(trend)
        assert trend_copy.country == trend.country
        assert trend_copy.latest_value == trend.latest_value
        
        # Test deep copy
        trend_deepcopy = copy.deepcopy(trend)
        assert trend_deepcopy.country == trend.country
        assert trend_deepcopy.values_3m == trend.values_3m
        assert trend_deepcopy.values_3m is not trend.values_3m

    def test_pmi_trend_string_representations(self, complete_pmi_trend_data):
        """Test PMITrend string representations."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        trend = PMITrend(**complete_pmi_trend_data)
        
        # Test string representation
        str_repr = str(trend)
        assert isinstance(str_repr, str)
        assert "PMITrend" in str_repr

    def test_pmi_trend_equality_comparison(self, sample_date):
        """Test PMITrend equality comparison."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        data1 = {
            'country': 'Equal Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 52.0,
            'trend_direction': 'up',
            'trend_strength': 0.7,
            'values_3m': [51.0, 51.5, 52.0],
            'values_6m': [50.0, 50.5, 51.0, 51.5, 51.8, 52.0],
            'values_12m': [49.0] + [49.5 + i*0.25 for i in range(11)],
            'avg_3m': 51.5,
            'avg_6m': 51.0,
            'avg_12m': 50.5,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        data2 = data1.copy()
        
        trend1 = PMITrend(**data1)
        trend2 = PMITrend(**data2)
        
        # Test individual attributes for equality
        assert trend1.country == trend2.country
        assert trend1.latest_value == trend2.latest_value
        assert trend1.trend_direction == trend2.trend_direction
        assert trend1.values_3m == trend2.values_3m

    def test_pmi_trend_different_countries(self, sample_date):
        """Test PMITrend with different country names."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        countries = [
            'United States', 'United Kingdom', 'Euro Area', 'China', 'Japan',
            'Germany', 'France', 'Italy', 'Spain', 'Canada', 'Australia',
            'Brazil', 'India', 'Russia', 'South Korea'
        ]
        
        base_data = {
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 52.0,
            'trend_direction': 'up',
            'trend_strength': 0.6,
            'values_3m': [51.0, 51.5, 52.0],
            'values_6m': [50.0, 50.5, 51.0, 51.5, 51.8, 52.0],
            'values_12m': [49.0] + [49.5 + i*0.25 for i in range(11)],
            'avg_3m': 51.5,
            'avg_6m': 51.0,
            'avg_12m': 50.5,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        for country in countries:
            data = base_data.copy()
            data['country'] = country
            
            trend = PMITrend(**data)
            assert trend.country == country

    def test_pmi_trend_different_types(self, sample_date):
        """Test PMITrend with different PMI types."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        pmi_types = ['Manufacturing', 'Services', 'Composite', 'Construction', 'Retail']
        
        base_data = {
            'country': 'Type Test',
            'latest_date': sample_date,
            'latest_value': 52.0,
            'trend_direction': 'up',
            'trend_strength': 0.6,
            'values_3m': [51.0, 51.5, 52.0],
            'values_6m': [50.0, 50.5, 51.0, 51.5, 51.8, 52.0],
            'values_12m': [49.0] + [49.5 + i*0.25 for i in range(11)],
            'avg_3m': 51.5,
            'avg_6m': 51.0,
            'avg_12m': 50.5,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        for pmi_type in pmi_types:
            data = base_data.copy()
            data['pmi_type'] = pmi_type
            
            trend = PMITrend(**data)
            assert trend.pmi_type == pmi_type

    def test_pmi_trend_historical_dates(self):
        """Test PMITrend with various historical dates."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        dates = [
            date(2020, 1, 1),
            date(2021, 6, 15),
            date(2022, 12, 31),
            date(2023, 7, 4),
            date(2024, 2, 29)  # Leap year
        ]
        
        base_data = {
            'country': 'Date Test',
            'pmi_type': 'Manufacturing',
            'latest_value': 52.0,
            'trend_direction': 'up',
            'trend_strength': 0.6,
            'values_3m': [51.0, 51.5, 52.0],
            'values_6m': [50.0, 50.5, 51.0, 51.5, 51.8, 52.0],
            'values_12m': [49.0] + [49.5 + i*0.25 for i in range(11)],
            'avg_3m': 51.5,
            'avg_6m': 51.0,
            'avg_12m': 50.5,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        for test_date in dates:
            data = base_data.copy()
            data['latest_date'] = test_date
            
            trend = PMITrend(**data)
            assert trend.latest_date == test_date

    def test_pmi_trend_precision_values(self, sample_date):
        """Test PMITrend with high precision values."""
        from src.forex_bot.global_pmi.models import PMITrend
        
        precision_data = {
            'country': 'Precision Test',
            'pmi_type': 'Manufacturing',
            'latest_date': sample_date,
            'latest_value': 52.123456789,
            'trend_direction': 'up',
            'trend_strength': 0.123456789,
            'values_3m': [51.111111111, 51.555555555, 52.123456789],
            'values_6m': [50.1, 50.5, 51.0, 51.5, 51.8, 52.123456789],
            'values_12m': [49.0] + [49.5 + i*0.123456789 for i in range(11)],
            'avg_3m': 51.596296296,
            'avg_6m': 51.066666667,
            'avg_12m': 50.679012346,
            'is_improving': True,
            'is_deteriorating': False,
            'is_above_3m_avg': True,
            'is_above_6m_avg': True,
            'is_above_12m_avg': True
        }
        
        trend = PMITrend(**precision_data)
        assert trend.latest_value == 52.123456789
        assert trend.trend_strength == 0.123456789
