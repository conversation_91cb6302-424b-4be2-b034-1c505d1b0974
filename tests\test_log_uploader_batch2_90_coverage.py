"""
Comprehensive tests for log_uploader.py to achieve 90%+ coverage.
This file targets the remaining uncovered lines and edge cases.
"""

import pytest
import os
import logging
import shutil
import subprocess
from unittest.mock import patch, MagicMock, mock_open, call
from pathlib import Path


class TestLogUploaderBatch2Coverage:
    """Comprehensive tests to push log_uploader.py to 90%+ coverage."""

    @pytest.fixture
    def mock_logger_adapter(self):
        """Create a mock logger adapter for testing."""
        adapter = MagicMock()
        adapter.extra = {"instance_id": "test_instance"}
        return adapter

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for testing."""
        config = MagicMock()
        config.rclone_exe_available = True
        config.log_filename = "trading_bot.log"
        config.perf_log_filename = "trading_performance.log"
        config.general_log_format = "%(asctime)s - %(levelname)s - %(message)s"
        config.perf_log_format = "%(asctime)s - %(levelname)s - %(message)s"
        config.rclone_remote_name = "remote"
        config.rclone_remote_folder = "logs"
        return config

    def test_upload_log_file_success(self, mock_logger_adapter, mock_config):
        """Test successful log file upload."""
        from src.forex_bot.log_uploader import upload_log_file

        local_log_path = "/path/to/log.txt"
        remote_name = "test_remote"
        remote_folder = "test_folder"

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 0
                mock_run.return_value.stdout = "Upload successful"
                mock_run.return_value.stderr = ""

                with patch('os.path.exists', return_value=True):
                    result = upload_log_file(local_log_path, remote_name, remote_folder, mock_logger_adapter)

                    assert result is True
                    mock_run.assert_called_once()

    def test_upload_log_file_rclone_not_available(self, mock_logger_adapter, mock_config):
        """Test upload when rclone is not available."""
        from src.forex_bot.log_uploader import upload_log_file

        mock_config.rclone_exe_available = False

        with patch('src.forex_bot.log_uploader.config', mock_config):
            result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

            assert result is False
            # The actual message is "Rclone executable not found, skipping upload."
            mock_logger_adapter.debug.assert_called_with("Rclone executable not found, skipping upload.")

    def test_upload_log_file_file_not_exists(self, mock_logger_adapter, mock_config):
        """Test upload when file doesn't exist."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('os.path.exists', return_value=False):
                result = upload_log_file("/nonexistent/log.txt", "remote", "folder", mock_logger_adapter)

                assert result is False
                mock_logger_adapter.warning.assert_called()

    def test_upload_log_file_subprocess_error(self, mock_logger_adapter, mock_config):
        """Test upload with subprocess error."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 1
                mock_run.return_value.stdout = ""
                mock_run.return_value.stderr = "Upload failed"

                with patch('os.path.exists', return_value=True):
                    result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                    assert result is False
                    mock_logger_adapter.error.assert_called()

    def test_upload_log_file_exception_handling(self, mock_logger_adapter, mock_config):
        """Test upload with general exception."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('subprocess.run', side_effect=Exception("Test exception")):
                with patch('os.path.exists', return_value=True):
                    result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                    assert result is False
                    mock_logger_adapter.error.assert_called()

    def test_upload_logs_to_cloud_success(self, mock_logger_adapter, mock_config):
        """Test successful cloud upload."""
        from src.forex_bot.log_uploader import upload_logs_to_cloud

        log_files = ["log1.txt", "log2.txt"]

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('src.forex_bot.log_uploader.upload_log_file') as mock_upload:
                mock_upload.return_value = True

                # upload_logs_to_cloud doesn't return a value, it just logs
                upload_logs_to_cloud(mock_logger_adapter, log_files)

                assert mock_upload.call_count == 2  # Both log files
                mock_logger_adapter.info.assert_called()

    def test_upload_logs_to_cloud_partial_failure(self, mock_logger_adapter, mock_config):
        """Test cloud upload with partial failure."""
        from src.forex_bot.log_uploader import upload_logs_to_cloud

        log_files = ["log1.txt", "log2.txt"]

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('src.forex_bot.log_uploader.upload_log_file') as mock_upload:
                # First upload succeeds, second fails
                mock_upload.side_effect = [True, False]

                upload_logs_to_cloud(mock_logger_adapter, log_files)

                assert mock_upload.call_count == 2
                mock_logger_adapter.warning.assert_called()

    def test_upload_logs_to_cloud_exception(self, mock_logger_adapter, mock_config):
        """Test cloud upload with exception."""
        from src.forex_bot.log_uploader import upload_logs_to_cloud

        log_files = ["log1.txt"]

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('src.forex_bot.log_uploader.upload_log_file', side_effect=Exception("Test exception")):
                # This should raise the exception since upload_logs_to_cloud doesn't catch it
                try:
                    upload_logs_to_cloud(mock_logger_adapter, log_files)
                except Exception:
                    pass  # Expected

    def test_set_handlers_success(self):
        """Test successful handler setting."""
        from src.forex_bot.log_uploader import set_handlers

        mock_file_handler = MagicMock()
        mock_perf_handler = MagicMock()

        with patch('src.forex_bot.log_uploader.file_handler', mock_file_handler):
            with patch('src.forex_bot.log_uploader.perf_file_handler', mock_perf_handler):
                set_handlers(mock_file_handler, mock_perf_handler)

                # Should set the global handlers
                assert True  # Function doesn't return anything

    def test_set_loggers_success(self):
        """Test successful logger setting."""
        from src.forex_bot.log_uploader import set_loggers

        mock_logger = MagicMock()
        mock_perf_logger = MagicMock()

        with patch('src.forex_bot.log_uploader.logger', mock_logger):
            with patch('src.forex_bot.log_uploader.perf_logger', mock_perf_logger):
                set_loggers(mock_logger, mock_perf_logger)

                # Should set the global loggers
                assert True  # Function doesn't return anything

    def test_handler_closing_error_handling(self, mock_logger_adapter, mock_config):
        """Test error handling when closing handlers."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('src.forex_bot.log_uploader.file_handler') as mock_handler:
                mock_handler.close.side_effect = Exception("Close error")

                with patch('subprocess.run') as mock_run:
                    mock_run.return_value.returncode = 0
                    mock_run.return_value.stdout = "Success"
                    mock_run.return_value.stderr = ""

                    with patch('os.path.exists', return_value=True):
                        result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                        # Should still succeed despite handler close error
                        assert result is True

    def test_handler_reopening_error_handling(self, mock_logger_adapter, mock_config):
        """Test error handling when reopening handlers."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('logging.FileHandler', side_effect=Exception("Handler creation error")):
                with patch('subprocess.run') as mock_run:
                    mock_run.return_value.returncode = 0
                    mock_run.return_value.stdout = "Success"
                    mock_run.return_value.stderr = ""

                    with patch('os.path.exists', return_value=True):
                        result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                        # Should still succeed despite handler reopen error
                        assert result is True

    def test_global_variables_access(self):
        """Test access to global variables."""
        from src.forex_bot import log_uploader

        # Test that global variables exist and can be accessed
        assert hasattr(log_uploader, 'file_handler')
        assert hasattr(log_uploader, 'perf_file_handler')
        assert hasattr(log_uploader, 'logger')
        assert hasattr(log_uploader, 'perf_logger')
        assert hasattr(log_uploader, 'config')

    def test_config_loading(self):
        """Test configuration loading."""
        from src.forex_bot import log_uploader

        # Test that config is loaded
        assert log_uploader.config is not None

    def test_edge_case_parameters(self, mock_logger_adapter, mock_config):
        """Test edge case parameters."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            # Test with empty strings
            result = upload_log_file("", "", "", mock_logger_adapter)
            assert result is False

            # Test with None values (should handle gracefully)
            try:
                result = upload_log_file(None, None, None, mock_logger_adapter)
                assert result is False
            except (TypeError, AttributeError):
                # Expected for None values
                pass

    def test_subprocess_timeout_handling(self, mock_logger_adapter, mock_config):
        """Test subprocess timeout handling."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('subprocess.run', side_effect=subprocess.TimeoutExpired("rclone", 30)):
                with patch('os.path.exists', return_value=True):
                    result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                    assert result is False
                    mock_logger_adapter.error.assert_called()

    def test_file_permission_error(self, mock_logger_adapter, mock_config):
        """Test file permission error handling."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('subprocess.run', side_effect=PermissionError("Permission denied")):
                with patch('os.path.exists', return_value=True):
                    result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                    assert result is False
                    mock_logger_adapter.error.assert_called()

    def test_different_return_codes(self, mock_logger_adapter, mock_config):
        """Test different subprocess return codes."""
        from src.forex_bot.log_uploader import upload_log_file

        with patch('src.forex_bot.log_uploader.config', mock_config):
            with patch('os.path.exists', return_value=True):
                # Test various return codes
                for return_code in [0, 1, 2, 255]:
                    with patch('subprocess.run') as mock_run:
                        mock_run.return_value.returncode = return_code
                        mock_run.return_value.stdout = f"Output for code {return_code}"
                        mock_run.return_value.stderr = f"Error for code {return_code}"

                        result = upload_log_file("/path/to/log.txt", "remote", "folder", mock_logger_adapter)

                        if return_code == 0:
                            assert result is True
                        else:
                            assert result is False
