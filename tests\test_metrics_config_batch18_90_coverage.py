"""
Comprehensive test coverage for metrics/metrics_config.py - Batch 18
Target: Push from 73% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from pydantic import ValidationError


class TestMetricsConfigBatch18Coverage:
    """Test class for metrics/metrics_config.py comprehensive coverage."""

    def test_prometheus_config_initialization(self):
        """Test PrometheusConfig initialization."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        config = PrometheusConfig()
        
        assert config.enabled is True
        assert config.port == 8000
        assert config.path == "/metrics"
        assert config.namespace == "forex_bot"
        assert config.labels == {}

    def test_prometheus_config_custom_values(self):
        """Test PrometheusConfig with custom values."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        config = PrometheusConfig(
            enabled=False,
            port=9090,
            path="/custom-metrics",
            namespace="custom_bot",
            labels={"env": "test", "version": "1.0"}
        )
        
        assert config.enabled is False
        assert config.port == 9090
        assert config.path == "/custom-metrics"
        assert config.namespace == "custom_bot"
        assert config.labels == {"env": "test", "version": "1.0"}

    def test_prometheus_config_port_validation_valid(self):
        """Test PrometheusConfig port validation with valid values."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        # Test valid port values
        valid_ports = [0, 1, 80, 443, 8000, 9090, 65535]
        
        for port in valid_ports:
            config = PrometheusConfig(port=port)
            assert config.port == port

    def test_prometheus_config_port_validation_invalid(self):
        """Test PrometheusConfig port validation with invalid values."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        # Test invalid port values
        invalid_ports = [-1, -100, 65536, 100000]
        
        for port in invalid_ports:
            with pytest.raises(ValidationError) as exc_info:
                PrometheusConfig(port=port)
            assert "Port must be between 0 and 65535" in str(exc_info.value)

    def test_opentelemetry_config_initialization(self):
        """Test OpenTelemetryConfig initialization."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        config = OpenTelemetryConfig()
        
        assert config.enabled is True
        assert config.service_name == "forex_bot"
        assert config.exporter_type == "console"
        assert config.exporter_endpoint is None
        assert config.sample_rate == 1.0

    def test_opentelemetry_config_custom_values(self):
        """Test OpenTelemetryConfig with custom values."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        config = OpenTelemetryConfig(
            enabled=False,
            service_name="custom_service",
            exporter_type="jaeger",
            exporter_endpoint="http://localhost:14268/api/traces",
            sample_rate=0.5
        )
        
        assert config.enabled is False
        assert config.service_name == "custom_service"
        assert config.exporter_type == "jaeger"
        assert config.exporter_endpoint == "http://localhost:14268/api/traces"
        assert config.sample_rate == 0.5

    def test_opentelemetry_config_exporter_type_validation_valid(self):
        """Test OpenTelemetryConfig exporter type validation with valid values."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        valid_types = ["console", "jaeger", "otlp"]
        
        for exporter_type in valid_types:
            config = OpenTelemetryConfig(exporter_type=exporter_type)
            assert config.exporter_type == exporter_type

    def test_opentelemetry_config_exporter_type_validation_invalid(self):
        """Test OpenTelemetryConfig exporter type validation with invalid values."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        invalid_types = ["invalid", "zipkin", "prometheus", ""]
        
        for exporter_type in invalid_types:
            with pytest.raises(ValidationError) as exc_info:
                OpenTelemetryConfig(exporter_type=exporter_type)
            assert "Exporter type must be one of" in str(exc_info.value)

    def test_opentelemetry_config_sample_rate_validation_valid(self):
        """Test OpenTelemetryConfig sample rate validation with valid values."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        valid_rates = [0.0, 0.1, 0.5, 0.9, 1.0]
        
        for rate in valid_rates:
            config = OpenTelemetryConfig(sample_rate=rate)
            assert config.sample_rate == rate

    def test_opentelemetry_config_sample_rate_validation_invalid(self):
        """Test OpenTelemetryConfig sample rate validation with invalid values."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        invalid_rates = [-0.1, -1.0, 1.1, 2.0, 10.0]
        
        for rate in invalid_rates:
            with pytest.raises(ValidationError) as exc_info:
                OpenTelemetryConfig(sample_rate=rate)
            assert "Sample rate must be between 0.0 and 1.0" in str(exc_info.value)

    def test_alerting_config_initialization(self):
        """Test AlertingConfig initialization."""
        from src.forex_bot.metrics.metrics_config import AlertingConfig
        
        config = AlertingConfig()
        
        assert config.enabled is True

    def test_alerting_config_custom_values(self):
        """Test AlertingConfig with custom values."""
        from src.forex_bot.metrics.metrics_config import AlertingConfig
        
        config = AlertingConfig(enabled=False)
        
        assert config.enabled is False

    def test_metrics_config_initialization(self):
        """Test MetricsConfig initialization."""
        from src.forex_bot.metrics.metrics_config import MetricsConfig
        
        config = MetricsConfig()
        
        assert hasattr(config, 'prometheus')
        assert hasattr(config, 'opentelemetry')
        assert hasattr(config, 'alerting')

    def test_metrics_config_custom_components(self):
        """Test MetricsConfig with custom component configurations."""
        from src.forex_bot.metrics.metrics_config import (
            MetricsConfig, PrometheusConfig, OpenTelemetryConfig, AlertingConfig
        )
        
        prometheus_config = PrometheusConfig(port=9090, namespace="custom")
        otel_config = OpenTelemetryConfig(service_name="custom_service")
        alerting_config = AlertingConfig(enabled=False)
        
        config = MetricsConfig(
            prometheus=prometheus_config,
            opentelemetry=otel_config,
            alerting=alerting_config
        )
        
        assert config.prometheus.port == 9090
        assert config.prometheus.namespace == "custom"
        assert config.opentelemetry.service_name == "custom_service"
        assert config.alerting.enabled is False

    def test_get_metrics_config_function(self):
        """Test get_metrics_config function."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        config = get_metrics_config()
        
        assert hasattr(config, 'prometheus')
        assert hasattr(config, 'opentelemetry')
        assert hasattr(config, 'alerting')

    @patch.dict(os.environ, {
        'PROMETHEUS_ENABLED': 'false',
        'PROMETHEUS_PORT': '9090',
        'OTEL_SERVICE_NAME': 'test_service'
    })
    def test_get_metrics_config_with_env_vars(self):
        """Test get_metrics_config with environment variables."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear any cached config
        import src.forex_bot.metrics.metrics_config as config_module
        if hasattr(config_module, '_cached_config'):
            delattr(config_module, '_cached_config')
        
        config = get_metrics_config()
        
        # Environment variables should influence the configuration
        assert hasattr(config, 'prometheus')
        assert hasattr(config, 'opentelemetry')

    def test_prometheus_config_field_descriptions(self):
        """Test PrometheusConfig field descriptions."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        # Test that fields have descriptions
        schema = PrometheusConfig.schema()
        properties = schema['properties']
        
        assert 'description' in properties['enabled']
        assert 'description' in properties['port']
        assert 'description' in properties['path']
        assert 'description' in properties['namespace']
        assert 'description' in properties['labels']

    def test_opentelemetry_config_field_descriptions(self):
        """Test OpenTelemetryConfig field descriptions."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        # Test that fields have descriptions
        schema = OpenTelemetryConfig.schema()
        properties = schema['properties']
        
        assert 'description' in properties['enabled']
        assert 'description' in properties['service_name']
        assert 'description' in properties['exporter_type']
        assert 'description' in properties['sample_rate']

    def test_prometheus_config_json_serialization(self):
        """Test PrometheusConfig JSON serialization."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        config = PrometheusConfig(
            enabled=True,
            port=8080,
            path="/test-metrics",
            namespace="test_bot",
            labels={"env": "test"}
        )
        
        json_data = config.json()
        assert isinstance(json_data, str)
        
        # Test deserialization
        config_from_json = PrometheusConfig.parse_raw(json_data)
        assert config_from_json.enabled == config.enabled
        assert config_from_json.port == config.port
        assert config_from_json.path == config.path
        assert config_from_json.namespace == config.namespace
        assert config_from_json.labels == config.labels

    def test_opentelemetry_config_json_serialization(self):
        """Test OpenTelemetryConfig JSON serialization."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        config = OpenTelemetryConfig(
            enabled=True,
            service_name="test_service",
            exporter_type="jaeger",
            exporter_endpoint="http://localhost:14268",
            sample_rate=0.8
        )
        
        json_data = config.json()
        assert isinstance(json_data, str)
        
        # Test deserialization
        config_from_json = OpenTelemetryConfig.parse_raw(json_data)
        assert config_from_json.enabled == config.enabled
        assert config_from_json.service_name == config.service_name
        assert config_from_json.exporter_type == config.exporter_type
        assert config_from_json.exporter_endpoint == config.exporter_endpoint
        assert config_from_json.sample_rate == config.sample_rate

    def test_prometheus_config_dict_conversion(self):
        """Test PrometheusConfig dictionary conversion."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        config = PrometheusConfig(
            enabled=False,
            port=9000,
            path="/custom",
            namespace="custom_ns",
            labels={"key": "value"}
        )
        
        config_dict = config.dict()
        assert isinstance(config_dict, dict)
        assert config_dict['enabled'] is False
        assert config_dict['port'] == 9000
        assert config_dict['path'] == "/custom"
        assert config_dict['namespace'] == "custom_ns"
        assert config_dict['labels'] == {"key": "value"}

    def test_opentelemetry_config_dict_conversion(self):
        """Test OpenTelemetryConfig dictionary conversion."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        config = OpenTelemetryConfig(
            enabled=False,
            service_name="dict_test",
            exporter_type="otlp",
            exporter_endpoint="http://test:4317",
            sample_rate=0.3
        )
        
        config_dict = config.dict()
        assert isinstance(config_dict, dict)
        assert config_dict['enabled'] is False
        assert config_dict['service_name'] == "dict_test"
        assert config_dict['exporter_type'] == "otlp"
        assert config_dict['exporter_endpoint'] == "http://test:4317"
        assert config_dict['sample_rate'] == 0.3

    def test_prometheus_config_copy(self):
        """Test PrometheusConfig copy functionality."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        original = PrometheusConfig(
            enabled=True,
            port=8080,
            labels={"original": "value"}
        )
        
        # Test copy with updates
        copied = original.copy(update={'port': 9090, 'labels': {"copied": "value"}})
        
        assert copied.enabled == original.enabled
        assert copied.port == 9090
        assert copied.labels == {"copied": "value"}
        assert original.port == 8080
        assert original.labels == {"original": "value"}

    def test_opentelemetry_config_copy(self):
        """Test OpenTelemetryConfig copy functionality."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        original = OpenTelemetryConfig(
            service_name="original_service",
            sample_rate=1.0
        )
        
        # Test copy with updates
        copied = original.copy(update={'service_name': "copied_service", 'sample_rate': 0.5})
        
        assert copied.enabled == original.enabled
        assert copied.service_name == "copied_service"
        assert copied.sample_rate == 0.5
        assert original.service_name == "original_service"
        assert original.sample_rate == 1.0

    def test_config_validation_edge_cases(self):
        """Test configuration validation edge cases."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig, OpenTelemetryConfig
        
        # Test boundary values for port
        PrometheusConfig(port=0)  # Should work
        PrometheusConfig(port=65535)  # Should work
        
        # Test boundary values for sample rate
        OpenTelemetryConfig(sample_rate=0.0)  # Should work
        OpenTelemetryConfig(sample_rate=1.0)  # Should work
        
        # Test empty strings
        PrometheusConfig(path="", namespace="")  # Should work
        OpenTelemetryConfig(service_name="")  # Should work

    def test_config_immutability_after_creation(self):
        """Test configuration behavior after creation."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        config = PrometheusConfig()
        
        # Test that we can modify fields (Pydantic models are mutable by default)
        original_port = config.port
        config.port = 9090
        assert config.port == 9090
        assert config.port != original_port

    def test_config_with_none_values(self):
        """Test configuration with None values where allowed."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        config = OpenTelemetryConfig(exporter_endpoint=None)
        assert config.exporter_endpoint is None

    def test_config_string_representation(self):
        """Test configuration string representation."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig, OpenTelemetryConfig
        
        prometheus_config = PrometheusConfig()
        otel_config = OpenTelemetryConfig()
        
        # Test that string representation works
        prometheus_str = str(prometheus_config)
        otel_str = str(otel_config)
        
        assert isinstance(prometheus_str, str)
        assert isinstance(otel_str, str)
        assert len(prometheus_str) > 0
        assert len(otel_str) > 0