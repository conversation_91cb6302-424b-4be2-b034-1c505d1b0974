"""
Comprehensive tests for signal_generator.py to achieve 90%+ coverage.
This file targets the remaining uncovered lines and edge cases.
"""

import pytest
import pandas as pd
import logging
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any


class TestSignalGeneratorBatch2Coverage:
    """Comprehensive tests to push signal_generator.py to 90%+ coverage."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger adapter."""
        return Mock(spec=logging.LoggerAdapter)

    @pytest.fixture
    def sample_dataframes(self):
        """Create sample DataFrames for testing."""
        data = {
            'time': [datetime.now(timezone.utc)],
            'open': [1.1000],
            'high': [1.1010],
            'low': [1.0990],
            'close': [1.1005],
            'volume': [1000]
        }
        df = pd.DataFrame(data)
        return df.copy(), df.copy(), df.copy()

    def test_import_error_handling_blocks(self):
        """Test import error handling by checking availability flags."""
        from src.forex_bot import signal_generator
        
        # Test that availability flags exist and can be accessed
        assert hasattr(signal_generator, 'ORDER_FLOW_ANALYZER_AVAILABLE')
        assert hasattr(signal_generator, 'MARKET_DEPTH_VISUALIZER_AVAILABLE')
        assert hasattr(signal_generator, 'CORRELATION_MATRIX_AVAILABLE')
        assert hasattr(signal_generator, 'METRICS_DASHBOARD_AVAILABLE')
        
        # These should be boolean values
        assert isinstance(signal_generator.ORDER_FLOW_ANALYZER_AVAILABLE, bool)
        assert isinstance(signal_generator.MARKET_DEPTH_VISUALIZER_AVAILABLE, bool)
        assert isinstance(signal_generator.CORRELATION_MATRIX_AVAILABLE, bool)
        assert isinstance(signal_generator.METRICS_DASHBOARD_AVAILABLE, bool)

    def test_signal_generator_class_initialization(self, mock_logger):
        """Test SignalGenerator class initialization."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        assert generator.adapter == mock_logger

    def test_run_analysis_modules_error_handling(self, mock_logger, sample_dataframes):
        """Test error handling in run_analysis_modules."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        macro_info = {}
        
        # Test with mocked modules that raise exceptions
        with patch('src.forex_bot.signal_generator.trend_analyzer') as mock_trend:
            mock_trend.analyze_trend.side_effect = Exception("Trend analysis error")
            
            result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, macro_info)
            
            # Should handle error gracefully and return dict
            assert isinstance(result, dict)
            mock_logger.error.assert_called()

    def test_prepare_analysis_context_error_handling(self, mock_logger, sample_dataframes):
        """Test error handling in prepare_analysis_context."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        digits = 5
        
        # Test with invalid analysis results
        invalid_analysis = "not a dict"
        
        result = generator.prepare_analysis_context(symbol, df_h4, df_m5, digits, [], invalid_analysis)
        
        # Should handle error gracefully
        assert isinstance(result, str)

    def test_generate_signal_error_handling(self, mock_logger):
        """Test error handling in generate_signal."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        symbol = "EURUSD"
        context = "test context"
        
        # Test with mocked gemini client that raises exception
        with patch('src.forex_bot.signal_generator.gemini_client') as mock_gemini:
            mock_gemini.get_gemini_response.side_effect = Exception("Gemini error")
            
            result = generator.generate_signal(symbol, context)
            
            # Should handle error gracefully and return HOLD
            assert result == "HOLD"
            mock_logger.error.assert_called()

    def test_get_knowledge_base_context_error_handling(self, mock_logger):
        """Test error handling in get_knowledge_base_context."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        symbol = "EURUSD"
        query = "test query"
        
        # Test with mocked qdrant service that raises exception
        with patch('src.forex_bot.signal_generator.qdrant_service') as mock_qdrant:
            mock_qdrant.QDRANT_FULLY_INITIALIZED = True
            mock_qdrant.get_qdrant_context.side_effect = Exception("Qdrant error")
            
            result = generator.get_knowledge_base_context(symbol, query)
            
            # Should handle error gracefully
            assert isinstance(result, str)
            mock_logger.error.assert_called()

    def test_backward_compatibility_functions(self, mock_logger, sample_dataframes):
        """Test backward compatibility functions."""
        from src.forex_bot.signal_generator import (
            run_analysis_modules,
            prepare_analysis_context,
            generate_signal,
            get_knowledge_base_context
        )
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        digits = 5
        
        # Test run_analysis_modules
        result = run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {}, mock_logger)
        assert isinstance(result, dict)
        
        # Test prepare_analysis_context
        result = prepare_analysis_context(symbol, df_h4, df_m5, digits, [], {}, mock_logger)
        assert isinstance(result, str)
        
        # Test generate_signal
        result = generate_signal(symbol, "test context", mock_logger)
        assert result in ["BUY", "SELL", "HOLD"]
        
        # Test get_knowledge_base_context
        result = get_knowledge_base_context(symbol, "test query", mock_logger)
        assert isinstance(result, str)

    def test_feature_flag_conditionals(self, mock_logger, sample_dataframes):
        """Test feature flag conditional paths."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with different feature flag combinations
        with patch('src.forex_bot.signal_generator.ORDER_FLOW_ANALYZER_AVAILABLE', False):
            with patch('src.forex_bot.signal_generator.MARKET_DEPTH_VISUALIZER_AVAILABLE', False):
                with patch('src.forex_bot.signal_generator.CORRELATION_MATRIX_AVAILABLE', False):
                    with patch('src.forex_bot.signal_generator.METRICS_DASHBOARD_AVAILABLE', False):
                        result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {})
                        assert isinstance(result, dict)

    def test_specific_error_paths(self, mock_logger, sample_dataframes):
        """Test specific error paths in analysis modules."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test volume profile error path
        with patch('src.forex_bot.signal_generator.volume_profile_calculator') as mock_vp:
            mock_vp.get_volume_profile_context.side_effect = Exception("VP error")
            
            result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {})
            assert isinstance(result, dict)
            
        # Test CVD error path
        with patch('src.forex_bot.signal_generator.cvd_calculator') as mock_cvd:
            mock_cvd.get_cvd_context.side_effect = Exception("CVD error")
            
            result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {})
            assert isinstance(result, dict)
            
        # Test COT error path
        with patch('src.forex_bot.signal_generator.cot_analyzer') as mock_cot:
            mock_cot.get_cot_context.side_effect = Exception("COT error")
            
            result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {})
            assert isinstance(result, dict)

    def test_multilingual_news_error_path(self, mock_logger, sample_dataframes):
        """Test multilingual news error handling."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test multilingual news error
        with patch('src.forex_bot.signal_generator.news_service') as mock_news:
            mock_news.get_multilingual_news_context.side_effect = Exception("News error")
            
            result = generator.run_analysis_modules(symbol, df_m5, df_h1, df_h4, now_utc, {})
            assert isinstance(result, dict)

    def test_edge_case_parameters(self, mock_logger):
        """Test edge case parameters."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        
        # Test with empty DataFrames
        empty_df = pd.DataFrame()
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        result = generator.run_analysis_modules(symbol, empty_df, empty_df, empty_df, now_utc, {})
        assert isinstance(result, dict)
        
        # Test with None macro_info
        result = generator.run_analysis_modules(symbol, empty_df, empty_df, empty_df, now_utc, None)
        assert isinstance(result, dict)

    def test_knowledge_base_edge_cases(self, mock_logger):
        """Test knowledge base edge cases."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger)
        symbol = "EURUSD"
        
        # Test with qdrant not initialized
        with patch('src.forex_bot.signal_generator.qdrant_service') as mock_qdrant:
            mock_qdrant.QDRANT_FULLY_INITIALIZED = False
            
            result = generator.get_knowledge_base_context(symbol, "test query")
            assert result == ""
            
        # Test with different top_k values
        with patch('src.forex_bot.signal_generator.qdrant_service') as mock_qdrant:
            mock_qdrant.QDRANT_FULLY_INITIALIZED = True
            mock_qdrant.get_qdrant_context.return_value = "KB result"
            
            result = generator.get_knowledge_base_context(symbol, "test query", top_k=0)
            assert isinstance(result, str)
            
            result = generator.get_knowledge_base_context(symbol, "test query", top_k=10)
            assert isinstance(result, str)
