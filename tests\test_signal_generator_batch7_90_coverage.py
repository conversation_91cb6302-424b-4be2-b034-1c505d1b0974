"""
Comprehensive test coverage for signal_generator.py - Batch 7
Target: Push from 76% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock, Mock
from datetime import datetime, timezone


class TestSignalGeneratorBatch7Coverage:
    """Test class for signal_generator.py comprehensive coverage."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return Mock()

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for testing."""
        config = Mock()
        config.symbols = ['EURUSD', 'GBPUSD']
        config.timeframe = 5
        config.risk_reward_ratio = 2.0
        config.atr_period = 14
        config.atr_multiplier = 1.5
        config.rsi_period = 14
        config.macd_fast = 12
        config.macd_slow = 26
        config.macd_signal = 9
        return config

    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing."""
        dates = pd.date_range('2024-01-01', periods=100, freq='5min')
        data = pd.DataFrame({
            'time': dates,
            'open': np.random.uniform(1.1000, 1.1100, 100),
            'high': np.random.uniform(1.1050, 1.1150, 100),
            'low': np.random.uniform(1.0950, 1.1050, 100),
            'close': np.random.uniform(1.1000, 1.1100, 100),
            'tick_volume': np.random.randint(100, 1000, 100)
        })
        return data

    def test_signal_generator_initialization(self, mock_logger, mock_config):
        """Test SignalGenerator initialization."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        assert generator.logger == mock_logger
        assert generator.config == mock_config

    def test_calculate_rsi_normal_data(self, mock_logger, mock_config, sample_data):
        """Test RSI calculation with normal data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        rsi = generator.calculate_rsi(sample_data, period=14)
        
        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(sample_data)
        assert not rsi.isna().all()

    def test_calculate_rsi_insufficient_data(self, mock_logger, mock_config):
        """Test RSI calculation with insufficient data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Create data with only 5 rows (less than period)
        short_data = pd.DataFrame({
            'close': [1.1000, 1.1010, 1.1005, 1.1015, 1.1020]
        })
        
        rsi = generator.calculate_rsi(short_data, period=14)
        
        assert isinstance(rsi, pd.Series)
        assert rsi.isna().all()

    def test_calculate_macd_normal_data(self, mock_logger, mock_config, sample_data):
        """Test MACD calculation with normal data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        macd_line, signal_line, histogram = generator.calculate_macd(sample_data)
        
        assert isinstance(macd_line, pd.Series)
        assert isinstance(signal_line, pd.Series)
        assert isinstance(histogram, pd.Series)
        assert len(macd_line) == len(sample_data)

    def test_calculate_macd_insufficient_data(self, mock_logger, mock_config):
        """Test MACD calculation with insufficient data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Create data with only 10 rows (less than slow period)
        short_data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 10)
        })
        
        macd_line, signal_line, histogram = generator.calculate_macd(short_data)
        
        assert isinstance(macd_line, pd.Series)
        assert macd_line.isna().all()

    def test_calculate_atr_normal_data(self, mock_logger, mock_config, sample_data):
        """Test ATR calculation with normal data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        atr = generator.calculate_atr(sample_data, period=14)
        
        assert isinstance(atr, pd.Series)
        assert len(atr) == len(sample_data)
        assert (atr >= 0).all()  # ATR should be non-negative

    def test_calculate_atr_insufficient_data(self, mock_logger, mock_config):
        """Test ATR calculation with insufficient data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Create data with only 5 rows
        short_data = pd.DataFrame({
            'high': [1.1020, 1.1030, 1.1025, 1.1035, 1.1040],
            'low': [1.1000, 1.1010, 1.1005, 1.1015, 1.1020],
            'close': [1.1010, 1.1020, 1.1015, 1.1025, 1.1030]
        })
        
        atr = generator.calculate_atr(short_data, period=14)
        
        assert isinstance(atr, pd.Series)
        assert atr.isna().all()

    def test_generate_signal_buy_conditions(self, mock_logger, mock_config, sample_data):
        """Test signal generation with buy conditions."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Mock indicators to create buy signal
        with patch.object(generator, 'calculate_rsi', return_value=pd.Series([30.0] * len(sample_data))), \
             patch.object(generator, 'calculate_macd', return_value=(
                 pd.Series([0.001] * len(sample_data)),  # MACD line
                 pd.Series([-0.001] * len(sample_data)),  # Signal line
                 pd.Series([0.002] * len(sample_data))   # Histogram
             )), \
             patch.object(generator, 'calculate_atr', return_value=pd.Series([0.001] * len(sample_data))):
            
            signal = generator.generate_signal(sample_data, 'EURUSD')
            
            assert signal is not None
            assert signal['action'] in ['BUY', 'SELL', 'HOLD']

    def test_generate_signal_sell_conditions(self, mock_logger, mock_config, sample_data):
        """Test signal generation with sell conditions."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Mock indicators to create sell signal
        with patch.object(generator, 'calculate_rsi', return_value=pd.Series([70.0] * len(sample_data))), \
             patch.object(generator, 'calculate_macd', return_value=(
                 pd.Series([-0.001] * len(sample_data)),  # MACD line
                 pd.Series([0.001] * len(sample_data)),   # Signal line
                 pd.Series([-0.002] * len(sample_data))   # Histogram
             )), \
             patch.object(generator, 'calculate_atr', return_value=pd.Series([0.001] * len(sample_data))):
            
            signal = generator.generate_signal(sample_data, 'EURUSD')
            
            assert signal is not None
            assert signal['action'] in ['BUY', 'SELL', 'HOLD']

    def test_generate_signal_hold_conditions(self, mock_logger, mock_config, sample_data):
        """Test signal generation with hold conditions."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Mock indicators to create hold signal
        with patch.object(generator, 'calculate_rsi', return_value=pd.Series([50.0] * len(sample_data))), \
             patch.object(generator, 'calculate_macd', return_value=(
                 pd.Series([0.0] * len(sample_data)),    # MACD line
                 pd.Series([0.0] * len(sample_data)),    # Signal line
                 pd.Series([0.0] * len(sample_data))     # Histogram
             )), \
             patch.object(generator, 'calculate_atr', return_value=pd.Series([0.001] * len(sample_data))):
            
            signal = generator.generate_signal(sample_data, 'EURUSD')
            
            assert signal is not None
            assert signal['action'] == 'HOLD'

    def test_generate_signal_empty_data(self, mock_logger, mock_config):
        """Test signal generation with empty data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        empty_data = pd.DataFrame()
        
        signal = generator.generate_signal(empty_data, 'EURUSD')
        
        assert signal is None

    def test_generate_signal_invalid_data(self, mock_logger, mock_config):
        """Test signal generation with invalid data."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Data with NaN values
        invalid_data = pd.DataFrame({
            'close': [np.nan, np.nan, np.nan],
            'high': [np.nan, np.nan, np.nan],
            'low': [np.nan, np.nan, np.nan]
        })
        
        signal = generator.generate_signal(invalid_data, 'EURUSD')
        
        assert signal is None

    def test_calculate_stop_loss_take_profit(self, mock_logger, mock_config):
        """Test stop loss and take profit calculation."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        entry_price = 1.1000
        atr_value = 0.001
        action = 'BUY'
        
        sl, tp = generator.calculate_stop_loss_take_profit(entry_price, atr_value, action)
        
        assert isinstance(sl, float)
        assert isinstance(tp, float)
        assert sl < entry_price  # For BUY, SL should be below entry
        assert tp > entry_price  # For BUY, TP should be above entry

    def test_calculate_stop_loss_take_profit_sell(self, mock_logger, mock_config):
        """Test stop loss and take profit calculation for SELL."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        entry_price = 1.1000
        atr_value = 0.001
        action = 'SELL'
        
        sl, tp = generator.calculate_stop_loss_take_profit(entry_price, atr_value, action)
        
        assert isinstance(sl, float)
        assert isinstance(tp, float)
        assert sl > entry_price  # For SELL, SL should be above entry
        assert tp < entry_price  # For SELL, TP should be below entry

    def test_calculate_position_size(self, mock_logger, mock_config):
        """Test position size calculation."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        account_balance = 10000.0
        risk_percent = 2.0
        entry_price = 1.1000
        stop_loss = 1.0950
        
        position_size = generator.calculate_position_size(
            account_balance, risk_percent, entry_price, stop_loss
        )
        
        assert isinstance(position_size, float)
        assert position_size > 0

    def test_calculate_position_size_zero_risk(self, mock_logger, mock_config):
        """Test position size calculation with zero risk."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        account_balance = 10000.0
        risk_percent = 0.0
        entry_price = 1.1000
        stop_loss = 1.0950
        
        position_size = generator.calculate_position_size(
            account_balance, risk_percent, entry_price, stop_loss
        )
        
        assert position_size == 0.0

    def test_calculate_position_size_same_prices(self, mock_logger, mock_config):
        """Test position size calculation with same entry and stop loss prices."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        account_balance = 10000.0
        risk_percent = 2.0
        entry_price = 1.1000
        stop_loss = 1.1000  # Same as entry
        
        position_size = generator.calculate_position_size(
            account_balance, risk_percent, entry_price, stop_loss
        )
        
        assert position_size == 0.0

    def test_validate_signal_valid(self, mock_logger, mock_config):
        """Test signal validation with valid signal."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        valid_signal = {
            'action': 'BUY',
            'entry_price': 1.1000,
            'stop_loss': 1.0950,
            'take_profit': 1.1100,
            'position_size': 0.1,
            'confidence': 0.8
        }
        
        is_valid = generator.validate_signal(valid_signal)
        
        assert is_valid is True

    def test_validate_signal_invalid_action(self, mock_logger, mock_config):
        """Test signal validation with invalid action."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        invalid_signal = {
            'action': 'INVALID',
            'entry_price': 1.1000,
            'stop_loss': 1.0950,
            'take_profit': 1.1100,
            'position_size': 0.1,
            'confidence': 0.8
        }
        
        is_valid = generator.validate_signal(invalid_signal)
        
        assert is_valid is False

    def test_validate_signal_missing_fields(self, mock_logger, mock_config):
        """Test signal validation with missing fields."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        incomplete_signal = {
            'action': 'BUY',
            'entry_price': 1.1000
            # Missing other required fields
        }
        
        is_valid = generator.validate_signal(incomplete_signal)
        
        assert is_valid is False

    def test_validate_signal_negative_position_size(self, mock_logger, mock_config):
        """Test signal validation with negative position size."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        invalid_signal = {
            'action': 'BUY',
            'entry_price': 1.1000,
            'stop_loss': 1.0950,
            'take_profit': 1.1100,
            'position_size': -0.1,  # Negative
            'confidence': 0.8
        }
        
        is_valid = generator.validate_signal(invalid_signal)
        
        assert is_valid is False

    def test_format_signal_output(self, mock_logger, mock_config):
        """Test signal output formatting."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        signal_data = {
            'action': 'BUY',
            'entry_price': 1.1000,
            'stop_loss': 1.0950,
            'take_profit': 1.1100,
            'position_size': 0.1,
            'confidence': 0.8
        }
        
        formatted_signal = generator.format_signal_output(signal_data, 'EURUSD')
        
        assert isinstance(formatted_signal, dict)
        assert 'symbol' in formatted_signal
        assert formatted_signal['symbol'] == 'EURUSD'
        assert 'timestamp' in formatted_signal

    def test_calculate_signal_strength(self, mock_logger, mock_config):
        """Test signal strength calculation."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        indicators = {
            'rsi': 30.0,
            'macd_histogram': 0.001,
            'trend_strength': 0.7
        }
        
        strength = generator.calculate_signal_strength(indicators)
        
        assert isinstance(strength, float)
        assert 0.0 <= strength <= 1.0

    def test_calculate_signal_strength_neutral(self, mock_logger, mock_config):
        """Test signal strength calculation with neutral indicators."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        indicators = {
            'rsi': 50.0,
            'macd_histogram': 0.0,
            'trend_strength': 0.0
        }
        
        strength = generator.calculate_signal_strength(indicators)
        
        assert isinstance(strength, float)
        assert strength == 0.0

    def test_apply_filters_basic(self, mock_logger, mock_config):
        """Test basic signal filtering."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        signal = {
            'action': 'BUY',
            'confidence': 0.8,
            'strength': 0.7
        }
        
        filtered_signal = generator.apply_filters(signal)
        
        assert filtered_signal is not None
        assert filtered_signal['action'] == 'BUY'

    def test_apply_filters_low_confidence(self, mock_logger, mock_config):
        """Test signal filtering with low confidence."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        signal = {
            'action': 'BUY',
            'confidence': 0.3,  # Low confidence
            'strength': 0.7
        }
        
        filtered_signal = generator.apply_filters(signal)
        
        # Should be filtered out or modified
        assert filtered_signal is None or filtered_signal['action'] == 'HOLD'

    def test_get_market_context(self, mock_logger, mock_config, sample_data):
        """Test market context analysis."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        context = generator.get_market_context(sample_data)
        
        assert isinstance(context, dict)
        assert 'volatility' in context
        assert 'trend' in context

    def test_exception_handling(self, mock_logger, mock_config):
        """Test exception handling in signal generation."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Test with None data
        signal = generator.generate_signal(None, 'EURUSD')
        
        assert signal is None

    def test_edge_case_extreme_values(self, mock_logger, mock_config):
        """Test handling of extreme market values."""
        from src.forex_bot.signal_generator import SignalGenerator
        
        generator = SignalGenerator(mock_logger, mock_config)
        
        # Create data with extreme values
        extreme_data = pd.DataFrame({
            'close': [0.0001, 999999.9999, 0.0001],
            'high': [0.0002, 999999.9999, 0.0002],
            'low': [0.0001, 999999.9998, 0.0001]
        })
        
        signal = generator.generate_signal(extreme_data, 'EXTREME')
        
        # Should handle gracefully
        assert signal is None or isinstance(signal, dict)
