"""
Comprehensive tests for system_monitor.py to achieve 90%+ coverage.
This file targets the remaining uncovered lines and edge cases.
"""

import pytest
import os
import json
import threading
import time
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock, mock_open
from typing import Dict, Any


class TestSystemMonitorBatch2Coverage:
    """Comprehensive tests to push system_monitor.py to 90%+ coverage."""

    @pytest.fixture
    def mock_logger_adapter(self):
        """Create a mock logger adapter for testing."""
        adapter = MagicMock()
        adapter.extra = {"instance_id": "test_instance"}
        return adapter

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for testing."""
        config = MagicMock()
        config.symbol = "EURUSD"
        config.kill_switch_enabled = True
        config.max_connection_failures = 3
        return config

    def test_system_monitor_class_initialization(self, mock_logger_adapter):
        """Test SystemMonitor class initialization."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        assert monitor.adapter == mock_logger_adapter
        assert monitor.mt5_connection_failures == 0
        assert monitor.last_mt5_check is None

    def test_check_mt5_connection_success(self, mock_logger_adapter, mock_config):
        """Test successful MT5 connection check."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch('src.forex_bot.system_monitor.mt5_client') as mock_mt5:
                mock_mt5.check_connection.return_value = True
                
                result = monitor.check_mt5_connection()
                
                assert result is True
                assert monitor.mt5_connection_failures == 0

    def test_check_mt5_connection_failure(self, mock_logger_adapter, mock_config):
        """Test MT5 connection failure."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch('src.forex_bot.system_monitor.mt5_client') as mock_mt5:
                mock_mt5.check_connection.return_value = False
                
                result = monitor.check_mt5_connection()
                
                assert result is False
                assert monitor.mt5_connection_failures == 1

    def test_check_mt5_connection_exception(self, mock_logger_adapter, mock_config):
        """Test MT5 connection check with exception."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch('src.forex_bot.system_monitor.mt5_client') as mock_mt5:
                mock_mt5.check_connection.side_effect = Exception("Connection error")
                
                result = monitor.check_mt5_connection()
                
                assert result is False
                mock_logger_adapter.error.assert_called()

    def test_check_mt5_connection_cached(self, mock_logger_adapter, mock_config):
        """Test cached MT5 connection check."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        monitor.last_mt5_check = datetime.now(timezone.utc)
        monitor.last_mt5_status = True
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            result = monitor.check_mt5_connection()
            
            # Should return cached result
            assert result is True

    def test_check_market_hours_open(self, mock_logger_adapter):
        """Test market hours check when market is open."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.market_hours') as mock_market:
            mock_market.is_market_open.return_value = True
            
            result = monitor.check_market_hours()
            
            assert result is True

    def test_check_market_hours_closed(self, mock_logger_adapter):
        """Test market hours check when market is closed."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.market_hours') as mock_market:
            mock_market.is_market_open.return_value = False
            
            result = monitor.check_market_hours()
            
            assert result is False

    def test_check_market_hours_exception(self, mock_logger_adapter):
        """Test market hours check with exception."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.market_hours') as mock_market:
            mock_market.is_market_open.side_effect = Exception("Market hours error")
            
            result = monitor.check_market_hours()
            
            assert result is False
            mock_logger_adapter.error.assert_called()

    def test_check_system_status_all_ok(self, mock_logger_adapter, mock_config):
        """Test system status check when everything is OK."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch.object(monitor, 'check_mt5_connection', return_value=True):
                with patch.object(monitor, 'check_market_hours', return_value=True):
                    result = monitor.check_system_status()
                    
                    assert result['status'] == 'OK'
                    assert result['mt5_connected'] is True
                    assert result['market_open'] is True

    def test_check_system_status_mt5_down(self, mock_logger_adapter, mock_config):
        """Test system status check when MT5 is down."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch.object(monitor, 'check_mt5_connection', return_value=False):
                with patch.object(monitor, 'check_market_hours', return_value=True):
                    result = monitor.check_system_status()
                    
                    assert result['status'] == 'ERROR'
                    assert result['mt5_connected'] is False

    def test_check_system_status_market_closed(self, mock_logger_adapter, mock_config):
        """Test system status check when market is closed."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch.object(monitor, 'check_mt5_connection', return_value=True):
                with patch.object(monitor, 'check_market_hours', return_value=False):
                    result = monitor.check_system_status()
                    
                    assert result['status'] == 'WARNING'
                    assert result['market_open'] is False

    def test_handle_connection_errors_mt5_disconnect(self, mock_logger_adapter, mock_config):
        """Test handling MT5 disconnect errors."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        monitor.mt5_connection_failures = 2  # Below threshold
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            error_details = {"error_type": "mt5_disconnect", "message": "Connection lost"}
            
            monitor.handle_connection_errors(error_details)
            
            # Should log warning about attempting reconnection
            mock_logger_adapter.warning.assert_called()

    def test_handle_connection_errors_kill_switch_activation(self, mock_logger_adapter, mock_config):
        """Test kill switch activation on repeated failures."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        monitor.mt5_connection_failures = 3  # At threshold
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            with patch.object(monitor, 'activate_kill_switch') as mock_kill_switch:
                error_details = {"error_type": "mt5_disconnect", "message": "Connection lost"}
                
                monitor.handle_connection_errors(error_details)
                
                # Should activate kill switch
                mock_kill_switch.assert_called_once()

    def test_handle_connection_errors_gemini_api_failure(self, mock_logger_adapter, mock_config):
        """Test handling Gemini API failures."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            error_details = {"error_type": "gemini_api_failure", "message": "API error"}
            
            monitor.handle_connection_errors(error_details)
            
            mock_logger_adapter.warning.assert_called()

    def test_handle_connection_errors_unknown_error(self, mock_logger_adapter, mock_config):
        """Test handling unknown errors."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            error_details = {"error_type": "unknown_error", "message": "Unknown error"}
            
            monitor.handle_connection_errors(error_details)
            
            mock_logger_adapter.error.assert_called()

    def test_activate_kill_switch(self, mock_logger_adapter):
        """Test kill switch activation."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('builtins.open', mock_open()) as mock_file:
            with patch('json.dump') as mock_json_dump:
                monitor.activate_kill_switch("Test reason")
                
                mock_file.assert_called_once()
                mock_json_dump.assert_called_once()
                mock_logger_adapter.critical.assert_called()

    def test_check_kill_switch_active(self, mock_logger_adapter):
        """Test checking active kill switch."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        kill_switch_data = {
            "active": True,
            "reason": "Test reason",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        with patch('os.path.exists', return_value=True):
            with patch('builtins.open', mock_open(read_data=json.dumps(kill_switch_data))):
                result = monitor.check_kill_switch()
                
                assert result is True

    def test_check_kill_switch_inactive(self, mock_logger_adapter):
        """Test checking inactive kill switch."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        kill_switch_data = {
            "active": False,
            "reason": "Test reason",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        with patch('os.path.exists', return_value=True):
            with patch('builtins.open', mock_open(read_data=json.dumps(kill_switch_data))):
                result = monitor.check_kill_switch()
                
                assert result is False

    def test_check_kill_switch_file_not_exists(self, mock_logger_adapter):
        """Test checking kill switch when file doesn't exist."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('os.path.exists', return_value=False):
            result = monitor.check_kill_switch()
            
            assert result is False

    def test_check_kill_switch_json_error(self, mock_logger_adapter):
        """Test checking kill switch with JSON error."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('os.path.exists', return_value=True):
            with patch('builtins.open', mock_open(read_data="invalid json")):
                result = monitor.check_kill_switch()
                
                assert result is False
                mock_logger_adapter.error.assert_called()

    def test_deactivate_kill_switch(self, mock_logger_adapter):
        """Test kill switch deactivation."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('os.path.exists', return_value=True):
            with patch('os.remove') as mock_remove:
                monitor.deactivate_kill_switch()
                
                mock_remove.assert_called_once()
                mock_logger_adapter.info.assert_called()

    def test_start_kill_switch_monitor(self, mock_logger_adapter):
        """Test starting kill switch monitor."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        with patch('threading.Thread') as mock_thread:
            monitor.start_kill_switch_monitor()
            
            mock_thread.assert_called_once()

    def test_backward_compatibility_functions(self, mock_logger_adapter, mock_config):
        """Test backward compatibility functions."""
        from src.forex_bot.system_monitor import (
            check_mt5_connection,
            check_market_hours,
            check_system_status,
            handle_connection_errors
        )
        
        with patch('src.forex_bot.system_monitor.config', mock_config):
            # Test check_mt5_connection
            with patch('src.forex_bot.system_monitor.mt5_client') as mock_mt5:
                mock_mt5.check_connection.return_value = True
                result = check_mt5_connection(mock_logger_adapter)
                assert result is True
            
            # Test check_market_hours
            with patch('src.forex_bot.system_monitor.market_hours') as mock_market:
                mock_market.is_market_open.return_value = True
                result = check_market_hours(mock_logger_adapter)
                assert result is True
            
            # Test check_system_status
            with patch('src.forex_bot.system_monitor.mt5_client') as mock_mt5:
                with patch('src.forex_bot.system_monitor.market_hours') as mock_market:
                    mock_mt5.check_connection.return_value = True
                    mock_market.is_market_open.return_value = True
                    result = check_system_status(mock_logger_adapter)
                    assert result['status'] == 'OK'
            
            # Test handle_connection_errors
            error_details = {"error_type": "test_error", "message": "Test message"}
            handle_connection_errors(error_details, mock_logger_adapter)
            mock_logger_adapter.error.assert_called()

    def test_edge_case_parameters(self, mock_logger_adapter):
        """Test edge case parameters."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        # Test with None error details
        monitor.handle_connection_errors(None)
        mock_logger_adapter.error.assert_called()
        
        # Test with empty error details
        monitor.handle_connection_errors({})
        mock_logger_adapter.error.assert_called()

    def test_kill_switch_monitor_thread_function(self, mock_logger_adapter):
        """Test kill switch monitor thread function."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger_adapter)
        
        # Mock the check_kill_switch to return True once, then False to exit loop
        with patch.object(monitor, 'check_kill_switch', side_effect=[False, True]):
            with patch('time.sleep') as mock_sleep:
                # This should exit quickly due to kill switch activation
                monitor._kill_switch_monitor_thread()
                
                mock_sleep.assert_called()

    def test_constants_and_globals(self):
        """Test module constants and globals."""
        from src.forex_bot import system_monitor
        
        # Test that constants exist
        assert hasattr(system_monitor, 'KILL_SWITCH_FILE')
        assert hasattr(system_monitor, 'KILL_SWITCH_CHECK_INTERVAL')
        assert hasattr(system_monitor, 'config')
        
        # Test constant values
        assert system_monitor.KILL_SWITCH_FILE == "kill_switch.json"
        assert system_monitor.KILL_SWITCH_CHECK_INTERVAL == 5
