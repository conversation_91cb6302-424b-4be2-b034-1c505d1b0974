"""
Comprehensive test coverage for system_monitor.py - Batch 6
Target: Push from 79% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import psutil
import time
from unittest.mock import patch, MagicMock, Mock
from datetime import datetime, timezone


class TestSystemMonitorBatch6Coverage:
    """Test class for system_monitor.py comprehensive coverage."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return Mock()

    def test_system_monitor_initialization(self, mock_logger):
        """Test SystemMonitor initialization."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        assert monitor.logger == mock_logger
        assert monitor.start_time is not None
        assert monitor.alert_thresholds is not None

    def test_get_cpu_usage(self, mock_logger):
        """Test get_cpu_usage method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.cpu_percent', return_value=75.5):
            cpu_usage = monitor.get_cpu_usage()
            assert cpu_usage == 75.5

    def test_get_memory_usage(self, mock_logger):
        """Test get_memory_usage method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        mock_memory = Mock()
        mock_memory.percent = 65.2
        mock_memory.used = 8589934592  # 8GB
        mock_memory.total = 17179869184  # 16GB
        
        with patch('psutil.virtual_memory', return_value=mock_memory):
            memory_info = monitor.get_memory_usage()
            
            assert memory_info['percent'] == 65.2
            assert memory_info['used_gb'] == 8.0
            assert memory_info['total_gb'] == 16.0

    def test_get_disk_usage(self, mock_logger):
        """Test get_disk_usage method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        mock_disk = Mock()
        mock_disk.percent = 45.8
        mock_disk.used = 107374182400  # 100GB
        mock_disk.total = 214748364800  # 200GB
        
        with patch('psutil.disk_usage', return_value=mock_disk):
            disk_info = monitor.get_disk_usage()
            
            assert disk_info['percent'] == 45.8
            assert disk_info['used_gb'] == 100.0
            assert disk_info['total_gb'] == 200.0

    def test_get_network_stats(self, mock_logger):
        """Test get_network_stats method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        mock_net = Mock()
        mock_net.bytes_sent = 1073741824  # 1GB
        mock_net.bytes_recv = 2147483648  # 2GB
        
        with patch('psutil.net_io_counters', return_value=mock_net):
            net_stats = monitor.get_network_stats()
            
            assert net_stats['bytes_sent'] == 1073741824
            assert net_stats['bytes_recv'] == 2147483648

    def test_get_process_count(self, mock_logger):
        """Test get_process_count method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        mock_processes = [Mock() for _ in range(150)]
        
        with patch('psutil.pids', return_value=list(range(150))):
            process_count = monitor.get_process_count()
            assert process_count == 150

    def test_get_uptime(self, mock_logger):
        """Test get_uptime method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        # Mock boot time to 1 hour ago
        boot_time = time.time() - 3600
        
        with patch('psutil.boot_time', return_value=boot_time):
            uptime = monitor.get_uptime()
            assert 3590 <= uptime <= 3610  # Allow some variance

    def test_check_system_health_normal(self, mock_logger):
        """Test check_system_health with normal values."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=50.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 60.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 70.0}):
            
            health_status = monitor.check_system_health()
            
            assert health_status['status'] == 'healthy'
            assert health_status['cpu_usage'] == 50.0
            assert health_status['memory_usage'] == 60.0
            assert health_status['disk_usage'] == 70.0

    def test_check_system_health_high_cpu(self, mock_logger):
        """Test check_system_health with high CPU usage."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=95.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 60.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 70.0}):
            
            health_status = monitor.check_system_health()
            
            assert health_status['status'] == 'warning'
            assert 'High CPU usage' in health_status['alerts']

    def test_check_system_health_high_memory(self, mock_logger):
        """Test check_system_health with high memory usage."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=50.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 95.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 70.0}):
            
            health_status = monitor.check_system_health()
            
            assert health_status['status'] == 'warning'
            assert 'High memory usage' in health_status['alerts']

    def test_check_system_health_high_disk(self, mock_logger):
        """Test check_system_health with high disk usage."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=50.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 60.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 95.0}):
            
            health_status = monitor.check_system_health()
            
            assert health_status['status'] == 'warning'
            assert 'High disk usage' in health_status['alerts']

    def test_check_system_health_critical(self, mock_logger):
        """Test check_system_health with critical values."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=99.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 98.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 97.0}):
            
            health_status = monitor.check_system_health()
            
            assert health_status['status'] == 'critical'
            assert len(health_status['alerts']) >= 3

    def test_log_system_stats(self, mock_logger):
        """Test log_system_stats method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=50.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 60.0, 'used_gb': 8.0, 'total_gb': 16.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 70.0, 'used_gb': 100.0, 'total_gb': 200.0}), \
             patch.object(monitor, 'get_network_stats', return_value={'bytes_sent': 1000, 'bytes_recv': 2000}), \
             patch.object(monitor, 'get_process_count', return_value=150), \
             patch.object(monitor, 'get_uptime', return_value=3600):
            
            monitor.log_system_stats()
            
            mock_logger.info.assert_called()

    def test_get_system_summary(self, mock_logger):
        """Test get_system_summary method."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch.object(monitor, 'get_cpu_usage', return_value=50.0), \
             patch.object(monitor, 'get_memory_usage', return_value={'percent': 60.0, 'used_gb': 8.0, 'total_gb': 16.0}), \
             patch.object(monitor, 'get_disk_usage', return_value={'percent': 70.0, 'used_gb': 100.0, 'total_gb': 200.0}), \
             patch.object(monitor, 'get_network_stats', return_value={'bytes_sent': 1000, 'bytes_recv': 2000}), \
             patch.object(monitor, 'get_process_count', return_value=150), \
             patch.object(monitor, 'get_uptime', return_value=3600):
            
            summary = monitor.get_system_summary()
            
            assert 'cpu_usage' in summary
            assert 'memory_usage' in summary
            assert 'disk_usage' in summary
            assert 'network_stats' in summary
            assert 'process_count' in summary
            assert 'uptime' in summary

    def test_psutil_exceptions(self, mock_logger):
        """Test handling of psutil exceptions."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        # Test CPU exception
        with patch('psutil.cpu_percent', side_effect=psutil.Error("CPU error")):
            cpu_usage = monitor.get_cpu_usage()
            assert cpu_usage == 0.0

        # Test memory exception
        with patch('psutil.virtual_memory', side_effect=psutil.Error("Memory error")):
            memory_info = monitor.get_memory_usage()
            assert memory_info['percent'] == 0.0

        # Test disk exception
        with patch('psutil.disk_usage', side_effect=psutil.Error("Disk error")):
            disk_info = monitor.get_disk_usage()
            assert disk_info['percent'] == 0.0

    def test_network_stats_exception(self, mock_logger):
        """Test network stats with exception."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.net_io_counters', side_effect=psutil.Error("Network error")):
            net_stats = monitor.get_network_stats()
            assert net_stats['bytes_sent'] == 0
            assert net_stats['bytes_recv'] == 0

    def test_process_count_exception(self, mock_logger):
        """Test process count with exception."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.pids', side_effect=psutil.Error("Process error")):
            process_count = monitor.get_process_count()
            assert process_count == 0

    def test_uptime_exception(self, mock_logger):
        """Test uptime with exception."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.boot_time', side_effect=psutil.Error("Boot time error")):
            uptime = monitor.get_uptime()
            assert uptime == 0.0

    def test_custom_alert_thresholds(self, mock_logger):
        """Test SystemMonitor with custom alert thresholds."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        custom_thresholds = {
            'cpu_warning': 60.0,
            'cpu_critical': 90.0,
            'memory_warning': 70.0,
            'memory_critical': 95.0,
            'disk_warning': 80.0,
            'disk_critical': 95.0
        }
        
        monitor = SystemMonitor(mock_logger, alert_thresholds=custom_thresholds)
        
        assert monitor.alert_thresholds == custom_thresholds

    def test_monitor_start_time(self, mock_logger):
        """Test SystemMonitor start time tracking."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        before_creation = datetime.now(timezone.utc)
        monitor = SystemMonitor(mock_logger)
        after_creation = datetime.now(timezone.utc)
        
        assert before_creation <= monitor.start_time <= after_creation

    def test_bytes_to_gb_conversion(self, mock_logger):
        """Test bytes to GB conversion in memory and disk usage."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        # Test memory conversion
        mock_memory = Mock()
        mock_memory.percent = 50.0
        mock_memory.used = 5368709120  # 5GB
        mock_memory.total = 10737418240  # 10GB
        
        with patch('psutil.virtual_memory', return_value=mock_memory):
            memory_info = monitor.get_memory_usage()
            assert memory_info['used_gb'] == 5.0
            assert memory_info['total_gb'] == 10.0

    def test_zero_values_handling(self, mock_logger):
        """Test handling of zero values."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.cpu_percent', return_value=0.0):
            cpu_usage = monitor.get_cpu_usage()
            assert cpu_usage == 0.0

        mock_memory = Mock()
        mock_memory.percent = 0.0
        mock_memory.used = 0
        mock_memory.total = 1073741824  # 1GB
        
        with patch('psutil.virtual_memory', return_value=mock_memory):
            memory_info = monitor.get_memory_usage()
            assert memory_info['percent'] == 0.0
            assert memory_info['used_gb'] == 0.0

    def test_very_high_values(self, mock_logger):
        """Test handling of very high values."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        with patch('psutil.cpu_percent', return_value=100.0):
            cpu_usage = monitor.get_cpu_usage()
            assert cpu_usage == 100.0

        mock_memory = Mock()
        mock_memory.percent = 99.9
        mock_memory.used = 1073741824000  # 1TB
        mock_memory.total = 1073741824000  # 1TB
        
        with patch('psutil.virtual_memory', return_value=mock_memory):
            memory_info = monitor.get_memory_usage()
            assert memory_info['percent'] == 99.9
            assert memory_info['used_gb'] == 1000.0

    def test_negative_values_handling(self, mock_logger):
        """Test handling of negative values (edge case)."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        # Some systems might return negative values in edge cases
        with patch('psutil.cpu_percent', return_value=-1.0):
            cpu_usage = monitor.get_cpu_usage()
            assert cpu_usage == -1.0  # Should handle gracefully

    def test_system_monitor_repr(self, mock_logger):
        """Test SystemMonitor string representation."""
        from src.forex_bot.system_monitor import SystemMonitor
        
        monitor = SystemMonitor(mock_logger)
        
        repr_str = repr(monitor)
        assert isinstance(repr_str, str)
        assert "SystemMonitor" in repr_str
