"""
Comprehensive test coverage for volume_profile/models.py - Batch 12
Target: Push from 89% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd


class TestVolumeProfileModelsBatch12Coverage:
    """Test class for volume_profile/models.py comprehensive coverage."""

    def test_volume_profile_result_initialization(self):
        """Test VolumeProfileResult initialization."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2345, 1.2350])
        volumes = np.array([500.0, 1000.0, 750.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2345,
            poc_volume=1000.0,
            value_area_high=1.2350,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.price_levels, price_levels)
        assert np.array_equal(result.volumes, volumes)
        assert result.poc_price == 1.2345
        assert result.poc_volume == 1000.0
        assert result.value_area_high == 1.2350
        assert result.value_area_low == 1.2340
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5

    def test_volume_profile_result_to_dataframe(self):
        """Test VolumeProfileResult to_dataframe method."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2345, 1.2350])
        volumes = np.array([500.0, 1000.0, 750.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2345,
            poc_volume=1000.0,
            value_area_high=1.2350,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'price_level' in df.columns
        assert 'volume' in df.columns
        assert 'normalized_volume' in df.columns
        assert 'cumulative_volume' in df.columns
        assert len(df) == 3
        assert np.array_equal(df['price_level'].values, price_levels)
        assert np.array_equal(df['volume'].values, volumes)

    def test_volume_profile_result_with_normalized_volumes(self):
        """Test VolumeProfileResult with normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2345, 1.2350])
        volumes = np.array([500.0, 1000.0, 750.0])
        normalized_volumes = np.array([0.5, 1.0, 0.75])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2345,
            poc_volume=1000.0,
            value_area_high=1.2350,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5,
            normalized_volumes=normalized_volumes
        )
        
        df = result.to_dataframe()
        assert np.array_equal(df['normalized_volume'].values, normalized_volumes)

    def test_volume_profile_result_with_cumulative_volumes(self):
        """Test VolumeProfileResult with cumulative volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2345, 1.2350])
        volumes = np.array([500.0, 1000.0, 750.0])
        cumulative_volumes = np.array([500.0, 1500.0, 2250.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2345,
            poc_volume=1000.0,
            value_area_high=1.2350,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5,
            cumulative_volumes=cumulative_volumes
        )
        
        df = result.to_dataframe()
        assert np.array_equal(df['cumulative_volume'].values, cumulative_volumes)

    def test_volume_profile_result_empty_arrays(self):
        """Test VolumeProfileResult with empty arrays."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=np.array([]),
            volumes=np.array([]),
            poc_price=0.0,
            poc_volume=0.0,
            value_area_high=0.0,
            value_area_low=0.0,
            symbol="TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        assert len(df) == 0
        assert 'price_level' in df.columns

    def test_volume_profile_result_default_normalized_volumes(self):
        """Test VolumeProfileResult with default normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0, 2.0])
        volumes = np.array([100.0, 200.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=2.0,
            poc_volume=200.0,
            value_area_high=2.0,
            value_area_low=1.0,
            symbol="TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        # Should use zeros when normalized_volumes is empty
        assert np.array_equal(df['normalized_volume'].values, np.zeros_like(volumes))

    def test_volume_profile_result_default_cumulative_volumes(self):
        """Test VolumeProfileResult with default cumulative volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0, 2.0])
        volumes = np.array([100.0, 200.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=2.0,
            poc_volume=200.0,
            value_area_high=2.0,
            value_area_low=1.0,
            symbol="TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        # Should use zeros when cumulative_volumes is empty
        assert np.array_equal(df['cumulative_volume'].values, np.zeros_like(volumes))

    def test_volume_zone_initialization(self):
        """Test VolumeZone initialization."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="high_volume",
            price_high=1.2350,
            price_low=1.2340,
            volume=1000.0,
            strength=0.8,
            description="High volume zone"
        )
        
        assert zone.zone_type == "high_volume"
        assert zone.price_high == 1.2350
        assert zone.price_low == 1.2340
        assert zone.volume == 1000.0
        assert zone.strength == 0.8
        assert zone.description == "High volume zone"

    def test_volume_zone_mid_price_property(self):
        """Test VolumeZone mid_price property."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="poc",
            price_high=1.2350,
            price_low=1.2340,
            volume=1000.0,
            strength=1.0
        )
        
        expected_mid_price = (1.2350 + 1.2340) / 2
        assert zone.mid_price == expected_mid_price

    def test_volume_zone_price_range_property(self):
        """Test VolumeZone price_range property."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="value_area",
            price_high=1.2350,
            price_low=1.2340,
            volume=1500.0,
            strength=0.7
        )
        
        expected_range = 1.2350 - 1.2340
        assert zone.price_range == expected_range

    def test_volume_zone_edge_cases(self):
        """Test VolumeZone with edge cases."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Zero volume and same price
        zone_zero = VolumeZone(
            zone_type="low_volume",
            price_high=1.2345,
            price_low=1.2345,  # Same price
            volume=0.0,
            strength=0.0
        )
        
        assert zone_zero.volume == 0.0
        assert zone_zero.price_range == 0.0
        assert zone_zero.mid_price == 1.2345

    def test_volume_zone_negative_values(self):
        """Test VolumeZone with negative values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="test",
            price_high=1.2340,  # Lower than price_low
            price_low=1.2350,
            volume=-100.0,  # Negative volume
            strength=-0.5   # Negative strength
        )
        
        assert zone.volume == -100.0
        assert zone.strength == -0.5
        assert zone.price_range == 1.2340 - 1.2350  # Negative range

    def test_volume_zone_different_types(self):
        """Test VolumeZone with different zone types."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone_types = ['high_volume', 'low_volume', 'poc', 'value_area']
        
        for zone_type in zone_types:
            zone = VolumeZone(
                zone_type=zone_type,
                price_high=1.2350,
                price_low=1.2340,
                volume=1000.0,
                strength=0.5
            )
            assert zone.zone_type == zone_type

    def test_volume_profile_result_optional_parameters(self):
        """Test VolumeProfileResult with optional parameters."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0])
        volumes = np.array([100.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.0,
            poc_volume=100.0,
            value_area_high=1.0,
            value_area_low=1.0,
            symbol="TEST",
            timeframe=1,
            start_time=pd.Timestamp('2024-01-01'),
            end_time=pd.Timestamp('2024-01-02'),
            num_bins=50,
            value_area_percent=80.0
        )
        
        assert result.start_time == pd.Timestamp('2024-01-01')
        assert result.end_time == pd.Timestamp('2024-01-02')
        assert result.num_bins == 50
        assert result.value_area_percent == 80.0

    def test_volume_profile_result_large_arrays(self):
        """Test VolumeProfileResult with large arrays."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        size = 1000
        price_levels = np.linspace(1.0, 2.0, size)
        volumes = np.random.rand(size) * 1000
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.5,
            poc_volume=500.0,
            value_area_high=1.8,
            value_area_low=1.2,
            symbol="LARGE_TEST",
            timeframe=60
        )
        
        df = result.to_dataframe()
        assert len(df) == size
        assert len(result.price_levels) == size
        assert len(result.volumes) == size

    def test_volume_zone_default_description(self):
        """Test VolumeZone with default description."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="test_zone",
            price_high=1.2350,
            price_low=1.2340,
            volume=1000.0,
            strength=0.8
        )
        
        assert zone.description == ""  # Default empty string

    def test_volume_profile_result_field_defaults(self):
        """Test VolumeProfileResult field defaults."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0])
        volumes = np.array([100.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.0,
            poc_volume=100.0,
            value_area_high=1.0,
            value_area_low=1.0,
            symbol="TEST",
            timeframe=1
        )
        
        # Test default values
        assert result.start_time is None
        assert result.end_time is None
        assert result.num_bins == 100
        assert result.value_area_percent == 70.0
        assert len(result.normalized_volumes) == 0
        assert len(result.cumulative_volumes) == 0

    def test_volume_zone_precision_calculations(self):
        """Test VolumeZone with high precision calculations."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="precision_test",
            price_high=1.234567890123456,
            price_low=1.234567890123455,
            volume=1000.123456789,
            strength=0.123456789
        )
        
        expected_mid = (1.234567890123456 + 1.234567890123455) / 2
        expected_range = 1.234567890123456 - 1.234567890123455
        
        assert zone.mid_price == expected_mid
        assert zone.price_range == expected_range

    def test_volume_profile_result_dataframe_column_order(self):
        """Test VolumeProfileResult DataFrame column order."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0, 2.0, 3.0])
        volumes = np.array([100.0, 200.0, 150.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=2.0,
            poc_volume=200.0,
            value_area_high=3.0,
            value_area_low=1.0,
            symbol="COLUMN_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        expected_columns = ['price_level', 'volume', 'normalized_volume', 'cumulative_volume']
        assert list(df.columns) == expected_columns

    def test_volume_profile_result_dataframe_data_types(self):
        """Test VolumeProfileResult DataFrame data types."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.0, 2.0, 3.0])
        volumes = np.array([100.0, 200.0, 150.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=2.0,
            poc_volume=200.0,
            value_area_high=3.0,
            value_area_low=1.0,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Check that all columns are numeric
        assert pd.api.types.is_numeric_dtype(df['price_level'])
        assert pd.api.types.is_numeric_dtype(df['volume'])
        assert pd.api.types.is_numeric_dtype(df['normalized_volume'])
        assert pd.api.types.is_numeric_dtype(df['cumulative_volume'])

    def test_volume_zone_extreme_values(self):
        """Test VolumeZone with extreme values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Very large values
        zone_large = VolumeZone(
            zone_type="extreme_large",
            price_high=999999.999999,
            price_low=0.000001,
            volume=999999999.0,
            strength=1.0
        )
        
        assert zone_large.mid_price == (999999.999999 + 0.000001) / 2
        assert zone_large.price_range == 999999.999999 - 0.000001

    def test_volume_profile_result_mixed_data_types(self):
        """Test VolumeProfileResult with mixed data types."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        # Mix of int and float
        price_levels = np.array([1, 2.5, 3])
        volumes = np.array([100, 200.5, 150])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=2.5,
            poc_volume=200.5,
            value_area_high=3.0,
            value_area_low=1.0,
            symbol="MIXED_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        assert len(df) == 3
        assert df['price_level'].iloc[1] == 2.5
        assert df['volume'].iloc[1] == 200.5