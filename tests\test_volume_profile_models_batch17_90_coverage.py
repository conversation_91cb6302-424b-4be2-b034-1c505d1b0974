"""
Comprehensive test coverage for volume_profile/models.py - Batch 17
Target: Push from 89% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from dataclasses import FrozenInstanceError


class TestVolumeProfileModelsBatch17Coverage:
    """Test class for volume_profile/models.py comprehensive coverage."""

    def test_volume_profile_result_initialization(self):
        """Test VolumeProfileResult initialization."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.price_levels, price_levels)
        assert np.array_equal(result.volumes, volumes)
        assert result.poc_price == 1.2341
        assert result.poc_volume == 150.0
        assert result.value_area_high == 1.2342
        assert result.value_area_low == 1.2340
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5

    def test_volume_profile_result_default_values(self):
        """Test VolumeProfileResult default values."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert result.start_time is None
        assert result.end_time is None
        assert result.num_bins == 100
        assert result.value_area_percent == 70.0
        assert len(result.normalized_volumes) == 0
        assert len(result.cumulative_volumes) == 0

    def test_volume_profile_result_with_optional_parameters(self):
        """Test VolumeProfileResult with optional parameters."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        normalized_volumes = np.array([0.67, 1.0, 0.8])
        cumulative_volumes = np.array([100.0, 250.0, 370.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5,
            start_time=pd.Timestamp('2024-01-01'),
            end_time=pd.Timestamp('2024-01-02'),
            num_bins=50,
            value_area_percent=80.0,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )
        
        assert result.start_time == pd.Timestamp('2024-01-01')
        assert result.end_time == pd.Timestamp('2024-01-02')
        assert result.num_bins == 50
        assert result.value_area_percent == 80.0
        assert np.array_equal(result.normalized_volumes, normalized_volumes)
        assert np.array_equal(result.cumulative_volumes, cumulative_volumes)

    def test_volume_profile_result_to_dataframe_basic(self):
        """Test VolumeProfileResult to_dataframe method basic functionality."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'price_level' in df.columns
        assert 'volume' in df.columns
        assert 'normalized_volume' in df.columns
        assert 'cumulative_volume' in df.columns
        assert len(df) == 3
        assert np.array_equal(df['price_level'].values, price_levels)
        assert np.array_equal(df['volume'].values, volumes)

    def test_volume_profile_result_to_dataframe_with_normalized_volumes(self):
        """Test VolumeProfileResult to_dataframe with normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        normalized_volumes = np.array([0.67, 1.0, 0.8])
        cumulative_volumes = np.array([100.0, 250.0, 370.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )
        
        df = result.to_dataframe()
        
        assert np.array_equal(df['normalized_volume'].values, normalized_volumes)
        assert np.array_equal(df['cumulative_volume'].values, cumulative_volumes)

    def test_volume_profile_result_to_dataframe_empty_normalized_volumes(self):
        """Test VolumeProfileResult to_dataframe with empty normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Should use zeros when normalized_volumes is empty
        expected_zeros = np.zeros_like(volumes)
        assert np.array_equal(df['normalized_volume'].values, expected_zeros)
        assert np.array_equal(df['cumulative_volume'].values, expected_zeros)

    def test_volume_zone_initialization(self):
        """Test VolumeZone initialization."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="high_volume",
            price_high=1.2350,
            price_low=1.2340,
            volume=1000.0,
            strength=0.8,
            description="High volume zone"
        )
        
        assert zone.zone_type == "high_volume"
        assert zone.price_high == 1.2350
        assert zone.price_low == 1.2340
        assert zone.volume == 1000.0
        assert zone.strength == 0.8
        assert zone.description == "High volume zone"

    def test_volume_zone_default_description(self):
        """Test VolumeZone default description."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="poc",
            price_high=1.2350,
            price_low=1.2340,
            volume=1000.0,
            strength=0.9
        )
        
        assert zone.description == ""

    def test_volume_zone_mid_price_property(self):
        """Test VolumeZone mid_price property."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="value_area",
            price_high=1.2350,
            price_low=1.2340,
            volume=800.0,
            strength=0.7
        )
        
        expected_mid_price = (1.2350 + 1.2340) / 2
        assert zone.mid_price == expected_mid_price
        assert abs(zone.mid_price - 1.2345) < 1e-10

    def test_volume_zone_price_range_property(self):
        """Test VolumeZone price_range property."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="low_volume",
            price_high=1.2360,
            price_low=1.2340,
            volume=200.0,
            strength=0.3
        )
        
        expected_range = 1.2360 - 1.2340
        assert zone.price_range == expected_range
        assert abs(zone.price_range - 0.002) < 1e-10

    def test_volume_zone_different_types(self):
        """Test VolumeZone with different zone types."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone_types = ["high_volume", "low_volume", "poc", "value_area"]
        
        for zone_type in zone_types:
            zone = VolumeZone(
                zone_type=zone_type,
                price_high=1.2350,
                price_low=1.2340,
                volume=500.0,
                strength=0.5
            )
            assert zone.zone_type == zone_type

    def test_volume_zone_extreme_values(self):
        """Test VolumeZone with extreme values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Very large values
        zone_large = VolumeZone(
            zone_type="high_volume",
            price_high=999999.999999,
            price_low=999999.999998,
            volume=999999999.0,
            strength=1.0
        )
        
        assert zone_large.price_high == 999999.999999
        assert zone_large.price_low == 999999.999998
        assert zone_large.volume == 999999999.0
        assert zone_large.strength == 1.0
        
        # Very small values
        zone_small = VolumeZone(
            zone_type="low_volume",
            price_high=0.000002,
            price_low=0.000001,
            volume=0.1,
            strength=0.0
        )
        
        assert zone_small.price_high == 0.000002
        assert zone_small.price_low == 0.000001
        assert zone_small.volume == 0.1
        assert zone_small.strength == 0.0

    def test_volume_zone_negative_values(self):
        """Test VolumeZone with negative values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="test",
            price_high=-1.2340,
            price_low=-1.2350,
            volume=-100.0,
            strength=-0.5
        )
        
        assert zone.price_high == -1.2340
        assert zone.price_low == -1.2350
        assert zone.volume == -100.0
        assert zone.strength == -0.5
        
        # Mid price calculation with negative values
        expected_mid = (-1.2340 + (-1.2350)) / 2
        assert zone.mid_price == expected_mid
        
        # Price range calculation with negative values
        expected_range = -1.2340 - (-1.2350)
        assert zone.price_range == expected_range

    def test_volume_zone_zero_values(self):
        """Test VolumeZone with zero values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="zero_test",
            price_high=0.0,
            price_low=0.0,
            volume=0.0,
            strength=0.0
        )
        
        assert zone.price_high == 0.0
        assert zone.price_low == 0.0
        assert zone.volume == 0.0
        assert zone.strength == 0.0
        assert zone.mid_price == 0.0
        assert zone.price_range == 0.0

    def test_volume_profile_result_large_arrays(self):
        """Test VolumeProfileResult with large arrays."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        size = 1000
        price_levels = np.linspace(1.2300, 1.2400, size)
        volumes = np.random.rand(size) * 1000
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2350,
            poc_volume=500.0,
            value_area_high=1.2380,
            value_area_low=1.2320,
            symbol="LARGE_TEST",
            timeframe=60
        )
        
        df = result.to_dataframe()
        assert len(df) == size
        assert len(result.price_levels) == size
        assert len(result.volumes) == size

    def test_volume_profile_result_empty_arrays(self):
        """Test VolumeProfileResult with empty arrays."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=np.array([]),
            volumes=np.array([]),
            poc_price=0.0,
            poc_volume=0.0,
            value_area_high=0.0,
            value_area_low=0.0,
            symbol="EMPTY_TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        assert len(df) == 0
        assert 'price_level' in df.columns
        assert 'volume' in df.columns

    def test_volume_profile_result_mixed_data_types(self):
        """Test VolumeProfileResult with mixed data types."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        # Mix of int and float
        price_levels = np.array([1, 1.5, 2])
        volumes = np.array([100, 150.5, 200])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.5,
            poc_volume=150.5,
            value_area_high=2.0,
            value_area_low=1.0,
            symbol="MIXED_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        assert len(df) == 3
        assert df['price_level'].iloc[1] == 1.5
        assert df['volume'].iloc[1] == 150.5

    def test_volume_zone_string_representation(self):
        """Test VolumeZone string representation."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="test_zone",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.6,
            description="Test zone"
        )
        
        # Test that string representation works
        str_repr = str(zone)
        assert isinstance(str_repr, str)

    def test_volume_profile_result_string_representation(self):
        """Test VolumeProfileResult string representation."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test that string representation works
        str_repr = str(result)
        assert isinstance(str_repr, str)

    def test_volume_profile_result_dataframe_column_order(self):
        """Test VolumeProfileResult DataFrame column order."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="COLUMN_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        expected_columns = ['price_level', 'volume', 'normalized_volume', 'cumulative_volume']
        assert list(df.columns) == expected_columns

    def test_volume_profile_result_dataframe_data_types(self):
        """Test VolumeProfileResult DataFrame data types."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Check that all columns are numeric
        assert pd.api.types.is_numeric_dtype(df['price_level'])
        assert pd.api.types.is_numeric_dtype(df['volume'])
        assert pd.api.types.is_numeric_dtype(df['normalized_volume'])
        assert pd.api.types.is_numeric_dtype(df['cumulative_volume'])

    def test_volume_zone_property_calculations(self):
        """Test VolumeZone property calculations with various scenarios."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Test with decimal precision
        zone = VolumeZone(
            zone_type="precision_test",
            price_high=1.23456789,
            price_low=1.23456780,
            volume=123.456789,
            strength=0.123456
        )
        
        expected_mid = (1.23456789 + 1.23456780) / 2
        expected_range = 1.23456789 - 1.23456780
        
        assert abs(zone.mid_price - expected_mid) < 1e-10
        assert abs(zone.price_range - expected_range) < 1e-10

    def test_volume_profile_result_type_consistency(self):
        """Test VolumeProfileResult type consistency."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="TYPE_TEST",
            timeframe=5
        )
        
        assert isinstance(result.price_levels, np.ndarray)
        assert isinstance(result.volumes, np.ndarray)
        assert isinstance(result.poc_price, float)
        assert isinstance(result.poc_volume, float)
        assert isinstance(result.value_area_high, float)
        assert isinstance(result.value_area_low, float)
        assert isinstance(result.symbol, str)
        assert isinstance(result.timeframe, int)

    def test_volume_zone_edge_case_calculations(self):
        """Test VolumeZone calculations with edge cases."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Test when price_high equals price_low
        zone_equal = VolumeZone(
            zone_type="equal_prices",
            price_high=1.2345,
            price_low=1.2345,
            volume=100.0,
            strength=0.5
        )
        
        assert zone_equal.mid_price == 1.2345
        assert zone_equal.price_range == 0.0
        
        # Test with very small differences
        zone_small_diff = VolumeZone(
            zone_type="small_diff",
            price_high=1.2345000001,
            price_low=1.2345000000,
            volume=100.0,
            strength=0.5
        )
        
        expected_mid = (1.2345000001 + 1.2345000000) / 2
        expected_range = 1.2345000001 - 1.2345000000
        
        assert abs(zone_small_diff.mid_price - expected_mid) < 1e-15
        assert abs(zone_small_diff.price_range - expected_range) < 1e-15