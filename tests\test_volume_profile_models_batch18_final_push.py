"""
Final push test coverage for volume_profile/models.py - Batch 18
Target: Push from 89% to 90%+ coverage
Focus: Missing lines and edge cases
"""

import pytest
import numpy as np
import pandas as pd


class TestVolumeProfileModelsBatch18FinalPush:
    """Test class for volume_profile/models.py final push to 90%+."""

    def test_volume_profile_result_to_dataframe_edge_case_mismatched_arrays(self):
        """Test VolumeProfileResult to_dataframe with mismatched array lengths."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        # Mismatched normalized volumes (different length)
        normalized_volumes = np.array([0.67, 1.0])  # Only 2 elements instead of 3
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="MISMATCH_TEST",
            timeframe=5,
            normalized_volumes=normalized_volumes
        )
        
        df = result.to_dataframe()
        
        # Should handle mismatched arrays gracefully
        assert len(df) == 3
        assert 'normalized_volume' in df.columns

    def test_volume_profile_result_to_dataframe_with_nan_values(self):
        """Test VolumeProfileResult to_dataframe with NaN values."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, np.nan, 1.2342])
        volumes = np.array([100.0, np.nan, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="NAN_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Should handle NaN values
        assert len(df) == 3
        assert pd.isna(df['price_level'].iloc[1])
        assert pd.isna(df['volume'].iloc[1])

    def test_volume_profile_result_to_dataframe_with_inf_values(self):
        """Test VolumeProfileResult to_dataframe with infinite values."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, np.inf, 1.2342])
        volumes = np.array([100.0, -np.inf, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="INF_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Should handle infinite values
        assert len(df) == 3
        assert np.isinf(df['price_level'].iloc[1])
        assert np.isinf(df['volume'].iloc[1])

    def test_volume_zone_with_inf_values(self):
        """Test VolumeZone with infinite values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="inf_test",
            price_high=np.inf,
            price_low=-np.inf,
            volume=np.inf,
            strength=1.0
        )
        
        assert np.isinf(zone.price_high)
        assert np.isinf(zone.price_low)
        assert np.isinf(zone.volume)
        assert np.isinf(zone.mid_price)
        assert np.isnan(zone.price_range)  # inf - (-inf) = nan

    def test_volume_zone_with_nan_values(self):
        """Test VolumeZone with NaN values."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="nan_test",
            price_high=np.nan,
            price_low=np.nan,
            volume=np.nan,
            strength=np.nan
        )
        
        assert np.isnan(zone.price_high)
        assert np.isnan(zone.price_low)
        assert np.isnan(zone.volume)
        assert np.isnan(zone.strength)
        assert np.isnan(zone.mid_price)
        assert np.isnan(zone.price_range)

    def test_volume_profile_result_repr_method(self):
        """Test VolumeProfileResult __repr__ method."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="REPR_TEST",
            timeframe=5
        )
        
        # Test __repr__ method
        repr_str = repr(result)
        assert isinstance(repr_str, str)
        assert "VolumeProfileResult" in repr_str

    def test_volume_zone_repr_method(self):
        """Test VolumeZone __repr__ method."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="repr_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        # Test __repr__ method
        repr_str = repr(zone)
        assert isinstance(repr_str, str)
        assert "VolumeZone" in repr_str

    def test_volume_profile_result_equality(self):
        """Test VolumeProfileResult equality comparison."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result1 = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        result2 = VolumeProfileResult(
            price_levels=price_levels.copy(),
            volumes=volumes.copy(),
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        # Test equality (dataclass should provide __eq__)
        assert result1 == result2

    def test_volume_zone_equality(self):
        """Test VolumeZone equality comparison."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone1 = VolumeZone(
            zone_type="equal_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        zone2 = VolumeZone(
            zone_type="equal_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        # Test equality (dataclass should provide __eq__)
        assert zone1 == zone2

    def test_volume_profile_result_hash(self):
        """Test VolumeProfileResult hash method."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="HASH_TEST",
            timeframe=5
        )
        
        # Test that hash works (if implemented)
        try:
            hash_value = hash(result)
            assert isinstance(hash_value, int)
        except TypeError:
            # Hash might not be implemented for mutable dataclass
            pass

    def test_volume_zone_hash(self):
        """Test VolumeZone hash method."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="hash_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        # Test that hash works (if implemented)
        try:
            hash_value = hash(zone)
            assert isinstance(hash_value, int)
        except TypeError:
            # Hash might not be implemented for mutable dataclass
            pass

    def test_volume_profile_result_field_access(self):
        """Test VolumeProfileResult field access patterns."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="FIELD_TEST",
            timeframe=5
        )
        
        # Test field access patterns
        fields = [
            'price_levels', 'volumes', 'poc_price', 'poc_volume',
            'value_area_high', 'value_area_low', 'symbol', 'timeframe',
            'start_time', 'end_time', 'num_bins', 'value_area_percent',
            'normalized_volumes', 'cumulative_volumes'
        ]
        
        for field in fields:
            assert hasattr(result, field)
            value = getattr(result, field)
            # Just ensure we can access the field

    def test_volume_zone_field_access(self):
        """Test VolumeZone field access patterns."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="field_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        # Test field access patterns
        fields = ['zone_type', 'price_high', 'price_low', 'volume', 'strength', 'description']
        
        for field in fields:
            assert hasattr(zone, field)
            value = getattr(zone, field)
            # Just ensure we can access the field

    def test_volume_profile_result_modification(self):
        """Test VolumeProfileResult field modification."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="MOD_TEST",
            timeframe=5
        )
        
        # Test field modification (dataclass should be mutable by default)
        original_symbol = result.symbol
        result.symbol = "MODIFIED_TEST"
        assert result.symbol == "MODIFIED_TEST"
        assert result.symbol != original_symbol

    def test_volume_zone_modification(self):
        """Test VolumeZone field modification."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        zone = VolumeZone(
            zone_type="mod_test",
            price_high=1.2350,
            price_low=1.2340,
            volume=500.0,
            strength=0.7
        )
        
        # Test field modification (dataclass should be mutable by default)
        original_type = zone.zone_type
        zone.zone_type = "modified_test"
        assert zone.zone_type == "modified_test"
        assert zone.zone_type != original_type

    def test_volume_profile_result_dataframe_index(self):
        """Test VolumeProfileResult DataFrame index behavior."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342])
        volumes = np.array([100.0, 150.0, 120.0])
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="INDEX_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Test DataFrame index
        assert df.index.tolist() == [0, 1, 2]
        assert len(df.index) == 3

    def test_volume_profile_result_dataframe_dtypes(self):
        """Test VolumeProfileResult DataFrame data types consistency."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        price_levels = np.array([1.2340, 1.2341, 1.2342], dtype=np.float64)
        volumes = np.array([100.0, 150.0, 120.0], dtype=np.float32)
        
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2341,
            poc_volume=150.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Test that DataFrame handles mixed dtypes
        assert len(df) == 3
        assert all(pd.api.types.is_numeric_dtype(df[col]) for col in df.columns)

    def test_volume_zone_property_edge_cases(self):
        """Test VolumeZone property calculations with edge cases."""
        from src.forex_bot.volume_profile.models import VolumeZone
        
        # Test with very large numbers
        zone_large = VolumeZone(
            zone_type="large_test",
            price_high=1e10,
            price_low=1e10 - 1,
            volume=1e15,
            strength=1.0
        )
        
        assert zone_large.mid_price == (1e10 + (1e10 - 1)) / 2
        assert zone_large.price_range == 1.0
        
        # Test with very small numbers
        zone_small = VolumeZone(
            zone_type="small_test",
            price_high=1e-10,
            price_low=1e-11,
            volume=1e-15,
            strength=0.0
        )
        
        expected_mid = (1e-10 + 1e-11) / 2
        expected_range = 1e-10 - 1e-11
        
        assert abs(zone_small.mid_price - expected_mid) < 1e-15
        assert abs(zone_small.price_range - expected_range) < 1e-15