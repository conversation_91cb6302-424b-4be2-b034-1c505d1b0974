"""
Comprehensive test coverage for volume_profile/models.py - Batch 5
Target: Push from 89% to 90%+ coverage (EASY WIN - only 1% needed!)
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock
import copy


class TestVolumeProfileModelsBatch5Coverage:
    """Test class for volume_profile/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_price_levels(self):
        """Sample price levels for testing."""
        return np.array([1.1000, 1.1005, 1.1010, 1.1015, 1.1020])

    @pytest.fixture
    def sample_volumes(self):
        """Sample volumes for testing."""
        return np.array([1000.0, 1500.0, 2000.0, 1200.0, 800.0])

    @pytest.fixture
    def sample_normalized_volumes(self):
        """Sample normalized volumes for testing."""
        return np.array([0.15, 0.23, 0.31, 0.18, 0.12])

    @pytest.fixture
    def sample_cumulative_volumes(self):
        """Sample cumulative volumes for testing."""
        return np.array([1000.0, 2500.0, 4500.0, 5700.0, 6500.0])

    def test_volume_profile_result_initialization(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult initialization."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.price_levels, sample_price_levels)
        assert np.array_equal(result.volumes, sample_volumes)
        assert result.poc_price == 1.1010
        assert result.poc_volume == 2000.0
        assert result.value_area_high == 1.1015
        assert result.value_area_low == 1.1005
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5

    def test_volume_profile_result_with_optional_parameters(self, sample_price_levels, sample_volumes, 
                                                           sample_normalized_volumes, sample_cumulative_volumes):
        """Test VolumeProfileResult with optional parameters."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        start_time = pd.Timestamp('2024-01-01 09:00:00', tz='UTC')
        end_time = pd.Timestamp('2024-01-01 15:00:00', tz='UTC')
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5,
            start_time=start_time,
            end_time=end_time,
            num_bins=50,
            value_area_percent=80.0,
            normalized_volumes=sample_normalized_volumes,
            cumulative_volumes=sample_cumulative_volumes
        )
        
        assert result.start_time == start_time
        assert result.end_time == end_time
        assert result.num_bins == 50
        assert result.value_area_percent == 80.0
        assert np.array_equal(result.normalized_volumes, sample_normalized_volumes)
        assert np.array_equal(result.cumulative_volumes, sample_cumulative_volumes)

    def test_volume_profile_result_to_dataframe_basic(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult to_dataframe method with basic data."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'price_level' in df.columns
        assert 'volume' in df.columns
        assert 'normalized_volume' in df.columns
        assert len(df) == len(sample_price_levels)
        assert np.array_equal(df['price_level'].values, sample_price_levels)
        assert np.array_equal(df['volume'].values, sample_volumes)

    def test_volume_profile_result_to_dataframe_with_normalized_volumes(self, sample_price_levels, sample_volumes, 
                                                                       sample_normalized_volumes, sample_cumulative_volumes):
        """Test VolumeProfileResult to_dataframe method with normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5,
            normalized_volumes=sample_normalized_volumes,
            cumulative_volumes=sample_cumulative_volumes
        )
        
        df = result.to_dataframe()
        
        assert 'normalized_volume' in df.columns
        assert 'cumulative_volume' in df.columns
        assert np.array_equal(df['normalized_volume'].values, sample_normalized_volumes)
        assert np.array_equal(df['cumulative_volume'].values, sample_cumulative_volumes)

    def test_volume_profile_result_empty_normalized_volumes(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult with empty normalized volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5
        )
        
        # normalized_volumes should be empty by default
        assert len(result.normalized_volumes) == 0
        
        df = result.to_dataframe()
        # Should use zeros for normalized_volume when empty
        assert np.array_equal(df['normalized_volume'].values, np.zeros_like(sample_volumes))

    def test_volume_profile_result_empty_cumulative_volumes(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult with empty cumulative volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="EURUSD",
            timeframe=5
        )
        
        # cumulative_volumes should be empty by default
        assert len(result.cumulative_volumes) == 0
        
        df = result.to_dataframe()
        # Should use zeros for cumulative_volume when empty
        assert np.array_equal(df['cumulative_volume'].values, np.zeros_like(sample_volumes))

    def test_volume_profile_result_single_price_level(self):
        """Test VolumeProfileResult with single price level."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        single_price = np.array([1.1000])
        single_volume = np.array([1000.0])
        
        result = VolumeProfileResult(
            price_levels=single_price,
            volumes=single_volume,
            poc_price=1.1000,
            poc_volume=1000.0,
            value_area_high=1.1000,
            value_area_low=1.1000,
            symbol="SINGLE",
            timeframe=5
        )
        
        assert len(result.price_levels) == 1
        assert len(result.volumes) == 1
        assert result.poc_price == 1.1000
        
        df = result.to_dataframe()
        assert len(df) == 1

    def test_volume_profile_result_large_dataset(self):
        """Test VolumeProfileResult with large dataset."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        large_size = 1000
        large_prices = np.linspace(1.1000, 1.1100, large_size)
        large_volumes = np.random.rand(large_size) * 1000
        
        result = VolumeProfileResult(
            price_levels=large_prices,
            volumes=large_volumes,
            poc_price=1.1050,
            poc_volume=max(large_volumes),
            value_area_high=1.1080,
            value_area_low=1.1020,
            symbol="LARGE",
            timeframe=1,
            num_bins=large_size
        )
        
        assert len(result.price_levels) == large_size
        assert len(result.volumes) == large_size
        assert result.num_bins == large_size
        
        df = result.to_dataframe()
        assert len(df) == large_size

    def test_volume_profile_result_extreme_values(self):
        """Test VolumeProfileResult with extreme values."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        extreme_prices = np.array([0.0001, 999999.9999])
        extreme_volumes = np.array([0.0001, 999999999.99])
        
        result = VolumeProfileResult(
            price_levels=extreme_prices,
            volumes=extreme_volumes,
            poc_price=999999.9999,
            poc_volume=999999999.99,
            value_area_high=999999.9999,
            value_area_low=0.0001,
            symbol="EXTREME",
            timeframe=1440
        )
        
        assert result.poc_price == 999999.9999
        assert result.poc_volume == 999999999.99
        
        df = result.to_dataframe()
        assert len(df) == 2

    def test_volume_profile_result_nan_values(self):
        """Test VolumeProfileResult with NaN values."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        nan_prices = np.array([1.1000, np.nan])
        nan_volumes = np.array([1000.0, np.nan])
        
        result = VolumeProfileResult(
            price_levels=nan_prices,
            volumes=nan_volumes,
            poc_price=1.1000,
            poc_volume=1000.0,
            value_area_high=1.1000,
            value_area_low=1.1000,
            symbol="NAN_TEST",
            timeframe=5
        )
        
        assert not np.isnan(result.price_levels[0])
        assert np.isnan(result.price_levels[1])
        
        df = result.to_dataframe()
        assert len(df) == 2
        assert np.isnan(df['price_level'].iloc[1])

    def test_volume_profile_result_zero_volumes(self):
        """Test VolumeProfileResult with zero volumes."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        zero_prices = np.array([1.1000, 1.1005, 1.1010])
        zero_volumes = np.array([0.0, 0.0, 0.0])
        
        result = VolumeProfileResult(
            price_levels=zero_prices,
            volumes=zero_volumes,
            poc_price=1.1000,
            poc_volume=0.0,
            value_area_high=1.1010,
            value_area_low=1.1000,
            symbol="ZERO_VOL",
            timeframe=5
        )
        
        assert result.poc_volume == 0.0
        assert all(result.volumes == 0.0)
        
        df = result.to_dataframe()
        assert all(df['volume'] == 0.0)

    def test_volume_profile_result_different_value_area_percentages(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult with different value area percentages."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        percentages = [50.0, 68.0, 70.0, 80.0, 90.0, 95.0]
        
        for pct in percentages:
            result = VolumeProfileResult(
                price_levels=sample_price_levels,
                volumes=sample_volumes,
                poc_price=1.1010,
                poc_volume=2000.0,
                value_area_high=1.1015,
                value_area_low=1.1005,
                symbol="PCT_TEST",
                timeframe=5,
                value_area_percent=pct
            )
            assert result.value_area_percent == pct

    def test_volume_profile_result_different_num_bins(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult with different number of bins."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        bin_counts = [10, 50, 100, 200, 500, 1000]
        
        for bins in bin_counts:
            result = VolumeProfileResult(
                price_levels=sample_price_levels,
                volumes=sample_volumes,
                poc_price=1.1010,
                poc_volume=2000.0,
                value_area_high=1.1015,
                value_area_low=1.1005,
                symbol="BINS_TEST",
                timeframe=5,
                num_bins=bins
            )
            assert result.num_bins == bins

    def test_volume_profile_result_copy_operations(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult copy operations."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="COPY_TEST",
            timeframe=5
        )
        
        # Test shallow copy
        result_copy = copy.copy(result)
        assert result_copy.symbol == result.symbol
        assert np.array_equal(result_copy.price_levels, result.price_levels)
        
        # Test deep copy
        result_deepcopy = copy.deepcopy(result)
        assert result_deepcopy.symbol == result.symbol
        assert np.array_equal(result_deepcopy.price_levels, result.price_levels)

    def test_volume_profile_result_string_representations(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult string representations."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test string representation
        str_repr = str(result)
        assert isinstance(str_repr, str)
        assert "VolumeProfileResult" in str_repr

    def test_volume_profile_result_dataframe_dtypes(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult DataFrame data types."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1015,
            value_area_low=1.1005,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Test DataFrame data types
        assert df.dtypes['price_level'] == np.float64
        assert df.dtypes['volume'] == np.float64
        assert df.dtypes['normalized_volume'] == np.float64

    def test_volume_profile_result_edge_case_value_areas(self, sample_price_levels, sample_volumes):
        """Test VolumeProfileResult with edge case value areas."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        # Test when value area high equals low
        result = VolumeProfileResult(
            price_levels=sample_price_levels,
            volumes=sample_volumes,
            poc_price=1.1010,
            poc_volume=2000.0,
            value_area_high=1.1010,
            value_area_low=1.1010,
            symbol="EDGE_VA",
            timeframe=5
        )
        
        assert result.value_area_high == result.value_area_low
        assert result.value_area_high == result.poc_price

    def test_volume_profile_result_memory_efficiency(self):
        """Test VolumeProfileResult memory efficiency with large arrays."""
        from src.forex_bot.volume_profile.models import VolumeProfileResult
        
        # Test with moderately large arrays
        size = 500
        prices = np.linspace(1.1000, 1.1050, size)
        volumes = np.random.rand(size) * 1000
        
        result = VolumeProfileResult(
            price_levels=prices,
            volumes=volumes,
            poc_price=1.1025,
            poc_volume=max(volumes),
            value_area_high=1.1040,
            value_area_low=1.1010,
            symbol="MEMORY_TEST",
            timeframe=1
        )
        
        # Should handle large arrays without issues
        df = result.to_dataframe()
        assert len(df) == size
        assert df.memory_usage(deep=True).sum() > 0
