"""
Comprehensive test coverage for vwap/models.py - Batch 13
Target: Push from 70% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime


class TestVWAPModelsBatch13Coverage:
    """Test class for vwap/models.py comprehensive coverage."""

    def test_vwap_result_initialization(self):
        """Test VWAPResult initialization."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3, 4, 5])
        vwap_values = np.array([1.2340, 1.2345, 1.2350, 1.2355, 1.2360])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.timestamps, timestamps)
        assert np.array_equal(result.vwap_values, vwap_values)
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5

    def test_vwap_result_to_dataframe_basic(self):
        """Test VWAPResult to_dataframe method basic functionality."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'timestamp' in df.columns
        assert 'vwap' in df.columns
        assert len(df) == 3
        assert np.array_equal(df['timestamp'].values, timestamps)
        assert np.array_equal(df['vwap'].values, vwap_values)

    def test_vwap_result_with_bands(self):
        """Test VWAPResult with standard deviation bands."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        upper_1sd = np.array([1.2350, 1.2355, 1.2360])
        lower_1sd = np.array([1.2330, 1.2335, 1.2340])
        upper_2sd = np.array([1.2360, 1.2365, 1.2370])
        lower_2sd = np.array([1.2320, 1.2325, 1.2330])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5,
            upper_band_1sd=upper_1sd,
            lower_band_1sd=lower_1sd,
            upper_band_2sd=upper_2sd,
            lower_band_2sd=lower_2sd
        )
        
        df = result.to_dataframe()
        
        assert 'upper_band_1sd' in df.columns
        assert 'lower_band_1sd' in df.columns
        assert 'upper_band_2sd' in df.columns
        assert 'lower_band_2sd' in df.columns
        assert np.array_equal(df['upper_band_1sd'].values, upper_1sd)
        assert np.array_equal(df['lower_band_1sd'].values, lower_1sd)
        assert np.array_equal(df['upper_band_2sd'].values, upper_2sd)
        assert np.array_equal(df['lower_band_2sd'].values, lower_2sd)

    def test_vwap_result_with_partial_bands(self):
        """Test VWAPResult with only some bands."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        upper_1sd = np.array([1.2350, 1.2355, 1.2360])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5,
            upper_band_1sd=upper_1sd,
            # Only upper_band_1sd, others are None
        )
        
        df = result.to_dataframe()
        
        assert 'upper_band_1sd' in df.columns
        assert 'lower_band_1sd' not in df.columns
        assert 'upper_band_2sd' not in df.columns
        assert 'lower_band_2sd' not in df.columns

    def test_vwap_result_with_mismatched_band_length(self):
        """Test VWAPResult with bands of different length."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        upper_1sd = np.array([1.2350, 1.2355])  # Different length
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5,
            upper_band_1sd=upper_1sd
        )
        
        df = result.to_dataframe()
        
        # Band should not be included due to length mismatch
        assert 'upper_band_1sd' not in df.columns

    def test_vwap_result_with_optional_parameters(self):
        """Test VWAPResult with optional parameters."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5,
            start_time=pd.Timestamp('2024-01-01'),
            end_time=pd.Timestamp('2024-01-02'),
            anchor_time=pd.Timestamp('2024-01-01 09:00:00'),
            is_anchored=True
        )
        
        assert result.start_time == pd.Timestamp('2024-01-01')
        assert result.end_time == pd.Timestamp('2024-01-02')
        assert result.anchor_time == pd.Timestamp('2024-01-01 09:00:00')
        assert result.is_anchored == True

    def test_vwap_result_default_values(self):
        """Test VWAPResult default values."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert result.start_time is None
        assert result.end_time is None
        assert result.anchor_time is None
        assert result.is_anchored == False
        assert result.upper_band_1sd is None
        assert result.lower_band_1sd is None
        assert result.upper_band_2sd is None
        assert result.lower_band_2sd is None

    def test_vwap_crossover_initialization(self):
        """Test VWAPCrossover initialization."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        crossover = VWAPCrossover(
            timestamp=pd.Timestamp('2024-01-01 10:00:00'),
            price=1.2345,
            vwap_value=1.2340,
            crossover_type="bullish",
            strength=0.8,
            volume=1000.0
        )
        
        assert crossover.timestamp == pd.Timestamp('2024-01-01 10:00:00')
        assert crossover.price == 1.2345
        assert crossover.vwap_value == 1.2340
        assert crossover.crossover_type == "bullish"
        assert crossover.strength == 0.8
        assert crossover.volume == 1000.0

    def test_vwap_crossover_default_values(self):
        """Test VWAPCrossover default values."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        crossover = VWAPCrossover(
            timestamp=pd.Timestamp('2024-01-01 10:00:00'),
            price=1.2345,
            vwap_value=1.2340,
            crossover_type="bullish"
        )
        
        assert crossover.strength is None
        assert crossover.volume is None

    def test_vwap_crossover_types(self):
        """Test VWAPCrossover with different crossover types."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        crossover_types = ["bullish", "bearish", "neutral"]
        
        for crossover_type in crossover_types:
            crossover = VWAPCrossover(
                timestamp=pd.Timestamp('2024-01-01 10:00:00'),
                price=1.2345,
                vwap_value=1.2340,
                crossover_type=crossover_type
            )
            assert crossover.crossover_type == crossover_type

    def test_vwap_result_empty_arrays(self):
        """Test VWAPResult with empty arrays."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=np.array([]),
            vwap_values=np.array([]),
            symbol="TEST",
            timeframe=1
        )
        
        df = result.to_dataframe()
        assert len(df) == 0
        assert 'timestamp' in df.columns
        assert 'vwap' in df.columns

    def test_vwap_result_large_arrays(self):
        """Test VWAPResult with large arrays."""
        from src.forex_bot.vwap.models import VWAPResult
        
        size = 1000
        timestamps = np.arange(size)
        vwap_values = np.random.rand(size) + 1.2
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="LARGE_TEST",
            timeframe=60
        )
        
        df = result.to_dataframe()
        assert len(df) == size
        assert len(result.timestamps) == size
        assert len(result.vwap_values) == size

    def test_vwap_result_dataframe_column_order(self):
        """Test VWAPResult DataFrame column order."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="COLUMN_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        expected_columns = ['timestamp', 'vwap']
        assert list(df.columns) == expected_columns

    def test_vwap_result_dataframe_with_all_bands(self):
        """Test VWAPResult DataFrame with all bands included."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        upper_1sd = np.array([1.2350, 1.2355, 1.2360])
        lower_1sd = np.array([1.2330, 1.2335, 1.2340])
        upper_2sd = np.array([1.2360, 1.2365, 1.2370])
        lower_2sd = np.array([1.2320, 1.2325, 1.2330])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="ALL_BANDS_TEST",
            timeframe=5,
            upper_band_1sd=upper_1sd,
            lower_band_1sd=lower_1sd,
            upper_band_2sd=upper_2sd,
            lower_band_2sd=lower_2sd
        )
        
        df = result.to_dataframe()
        expected_columns = [
            'timestamp', 'vwap', 'upper_band_1sd', 'lower_band_1sd',
            'upper_band_2sd', 'lower_band_2sd'
        ]
        assert list(df.columns) == expected_columns

    def test_vwap_result_dataframe_data_types(self):
        """Test VWAPResult DataFrame data types."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Check that all columns are numeric
        assert pd.api.types.is_numeric_dtype(df['timestamp'])
        assert pd.api.types.is_numeric_dtype(df['vwap'])

    def test_vwap_crossover_extreme_values(self):
        """Test VWAPCrossover with extreme values."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        # Very large values
        crossover_large = VWAPCrossover(
            timestamp=pd.Timestamp('2024-01-01 10:00:00'),
            price=999999.999999,
            vwap_value=999999.999998,
            crossover_type="bullish",
            strength=1.0,
            volume=999999999.0
        )
        
        assert crossover_large.price == 999999.999999
        assert crossover_large.vwap_value == 999999.999998
        assert crossover_large.volume == 999999999.0

    def test_vwap_result_mixed_data_types(self):
        """Test VWAPResult with mixed data types."""
        from src.forex_bot.vwap.models import VWAPResult
        
        # Mix of int and float
        timestamps = np.array([1, 2.5, 3])
        vwap_values = np.array([1, 2.5, 3])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="MIXED_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        assert len(df) == 3
        assert df['timestamp'].iloc[1] == 2.5
        assert df['vwap'].iloc[1] == 2.5

    def test_vwap_crossover_negative_values(self):
        """Test VWAPCrossover with negative values."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        crossover = VWAPCrossover(
            timestamp=pd.Timestamp('2024-01-01 10:00:00'),
            price=-1.2345,
            vwap_value=-1.2340,
            crossover_type="bearish",
            strength=-0.5,
            volume=-1000.0
        )
        
        assert crossover.price == -1.2345
        assert crossover.vwap_value == -1.2340
        assert crossover.strength == -0.5
        assert crossover.volume == -1000.0

    def test_vwap_result_string_representation(self):
        """Test VWAPResult string representation."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1, 2, 3])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test that string representation works
        str_repr = str(result)
        assert isinstance(str_repr, str)

    def test_vwap_crossover_string_representation(self):
        """Test VWAPCrossover string representation."""
        from src.forex_bot.vwap.models import VWAPCrossover
        
        crossover = VWAPCrossover(
            timestamp=pd.Timestamp('2024-01-01 10:00:00'),
            price=1.2345,
            vwap_value=1.2340,
            crossover_type="bullish"
        )
        
        # Test that string representation works
        str_repr = str(crossover)
        assert isinstance(str_repr, str)

    def test_vwap_result_type_consistency(self):
        """Test VWAPResult type consistency."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timestamps = np.array([1.0, 2.0, 3.0])
        vwap_values = np.array([1.2340, 1.2345, 1.2350])
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol="TYPE_TEST",
            timeframe=5
        )
        
        assert isinstance(result.timestamps, np.ndarray)
        assert isinstance(result.vwap_values, np.ndarray)
        assert isinstance(result.symbol, str)
        assert isinstance(result.timeframe, int)
        assert isinstance(result.is_anchored, bool)