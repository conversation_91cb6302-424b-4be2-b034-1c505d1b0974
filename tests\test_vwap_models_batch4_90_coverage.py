"""
Comprehensive test coverage for vwap/models.py - Batch 4
Target: Push from 70% to 90%+ coverage
Focus: Edge cases, error handling, and uncovered code paths
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock
import copy


class TestVWAPModelsBatch4Coverage:
    """Test class for vwap/models.py comprehensive coverage."""

    @pytest.fixture
    def sample_timestamps(self):
        """Sample timestamps for testing."""
        return np.array([
            pd.Timestamp('2024-01-01 10:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 11:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 12:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 13:00:00', tz='UTC'),
            pd.Timestamp('2024-01-01 14:00:00', tz='UTC')
        ])

    @pytest.fixture
    def sample_vwap_values(self):
        """Sample VWAP values for testing."""
        return np.array([1.1000, 1.1010, 1.1005, 1.1015, 1.1020])

    @pytest.fixture
    def sample_bands(self):
        """Sample band arrays for testing."""
        upper_1sd = np.array([1.1020, 1.1030, 1.1025, 1.1035, 1.1040])
        lower_1sd = np.array([1.0980, 1.0990, 1.0985, 1.0995, 1.1000])
        upper_2sd = np.array([1.1040, 1.1050, 1.1045, 1.1055, 1.1060])
        lower_2sd = np.array([1.0960, 1.0970, 1.0965, 1.0975, 1.0980])
        return upper_1sd, lower_1sd, upper_2sd, lower_2sd

    def test_vwap_result_initialization(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult initialization."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        assert np.array_equal(result.timestamps, sample_timestamps)
        assert np.array_equal(result.vwap_values, sample_vwap_values)
        assert result.symbol == "EURUSD"
        assert result.timeframe == 5
        assert result.start_time is None
        assert result.end_time is None
        assert result.anchor_time is None
        assert result.is_anchored is False

    def test_vwap_result_with_optional_parameters(self, sample_timestamps, sample_vwap_values, sample_bands):
        """Test VWAPResult with optional parameters."""
        from src.forex_bot.vwap.models import VWAPResult
        
        upper_1sd, lower_1sd, upper_2sd, lower_2sd = sample_bands
        start_time = pd.Timestamp('2024-01-01 09:00:00', tz='UTC')
        end_time = pd.Timestamp('2024-01-01 15:00:00', tz='UTC')
        anchor_time = pd.Timestamp('2024-01-01 09:30:00', tz='UTC')
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5,
            start_time=start_time,
            end_time=end_time,
            anchor_time=anchor_time,
            is_anchored=True,
            upper_band_1sd=upper_1sd,
            lower_band_1sd=lower_1sd,
            upper_band_2sd=upper_2sd,
            lower_band_2sd=lower_2sd
        )
        
        assert result.start_time == start_time
        assert result.end_time == end_time
        assert result.anchor_time == anchor_time
        assert result.is_anchored is True
        assert np.array_equal(result.upper_band_1sd, upper_1sd)
        assert np.array_equal(result.lower_band_1sd, lower_1sd)
        assert np.array_equal(result.upper_band_2sd, upper_2sd)
        assert np.array_equal(result.lower_band_2sd, lower_2sd)

    def test_vwap_result_to_dataframe_basic(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult to_dataframe method with basic data."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert 'timestamp' in df.columns
        assert 'vwap' in df.columns
        assert len(df) == len(sample_timestamps)
        assert np.array_equal(df['vwap'].values, sample_vwap_values)

    def test_vwap_result_to_dataframe_with_bands(self, sample_timestamps, sample_vwap_values, sample_bands):
        """Test VWAPResult to_dataframe method with band data."""
        from src.forex_bot.vwap.models import VWAPResult
        
        upper_1sd, lower_1sd, upper_2sd, lower_2sd = sample_bands
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5,
            upper_band_1sd=upper_1sd,
            lower_band_1sd=lower_1sd,
            upper_band_2sd=upper_2sd,
            lower_band_2sd=lower_2sd
        )
        
        df = result.to_dataframe()
        
        assert 'upper_band_1sd' in df.columns
        assert 'lower_band_1sd' in df.columns
        assert 'upper_band_2sd' in df.columns
        assert 'lower_band_2sd' in df.columns
        assert np.array_equal(df['upper_band_1sd'].values, upper_1sd)
        assert np.array_equal(df['lower_band_1sd'].values, lower_1sd)
        assert np.array_equal(df['upper_band_2sd'].values, upper_2sd)
        assert np.array_equal(df['lower_band_2sd'].values, lower_2sd)

    def test_vwap_result_to_dataframe_mismatched_lengths(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult to_dataframe with mismatched array lengths."""
        from src.forex_bot.vwap.models import VWAPResult
        
        # Create band arrays with different lengths
        short_upper_band = np.array([1.1020, 1.1030])  # Only 2 elements instead of 5
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5,
            upper_band_1sd=short_upper_band
        )
        
        df = result.to_dataframe()
        
        # Should not include upper_band_1sd due to length mismatch
        assert 'upper_band_1sd' not in df.columns
        assert 'timestamp' in df.columns
        assert 'vwap' in df.columns

    def test_vwap_result_anchored_vwap(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult with anchored VWAP."""
        from src.forex_bot.vwap.models import VWAPResult
        
        anchor_time = pd.Timestamp('2024-01-01 09:30:00', tz='UTC')
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5,
            anchor_time=anchor_time,
            is_anchored=True
        )
        
        assert result.is_anchored is True
        assert result.anchor_time == anchor_time
        
        df = result.to_dataframe()
        assert len(df) == len(sample_timestamps)

    def test_vwap_result_non_anchored_vwap(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult with non-anchored VWAP."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EURUSD",
            timeframe=5,
            is_anchored=False
        )
        
        assert result.is_anchored is False
        assert result.anchor_time is None

    def test_vwap_result_empty_arrays(self):
        """Test VWAPResult with empty arrays."""
        from src.forex_bot.vwap.models import VWAPResult
        
        empty_timestamps = np.array([])
        empty_values = np.array([])
        
        result = VWAPResult(
            timestamps=empty_timestamps,
            vwap_values=empty_values,
            symbol="EMPTY",
            timeframe=1
        )
        
        assert len(result.timestamps) == 0
        assert len(result.vwap_values) == 0
        
        df = result.to_dataframe()
        assert len(df) == 0

    def test_vwap_result_single_value(self):
        """Test VWAPResult with single value."""
        from src.forex_bot.vwap.models import VWAPResult
        
        single_timestamp = np.array([pd.Timestamp('2024-01-01 10:00:00', tz='UTC')])
        single_value = np.array([1.1000])
        
        result = VWAPResult(
            timestamps=single_timestamp,
            vwap_values=single_value,
            symbol="SINGLE",
            timeframe=5
        )
        
        assert len(result.timestamps) == 1
        assert len(result.vwap_values) == 1
        
        df = result.to_dataframe()
        assert len(df) == 1
        assert df['vwap'].iloc[0] == 1.1000

    def test_vwap_result_large_dataset(self):
        """Test VWAPResult with large dataset."""
        from src.forex_bot.vwap.models import VWAPResult
        
        large_size = 10000
        large_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC') + pd.Timedelta(minutes=i)
            for i in range(large_size)
        ])
        large_values = 1.1000 + np.random.rand(large_size) * 0.01
        
        result = VWAPResult(
            timestamps=large_timestamps,
            vwap_values=large_values,
            symbol="LARGE",
            timeframe=1
        )
        
        assert len(result.timestamps) == large_size
        assert len(result.vwap_values) == large_size
        
        df = result.to_dataframe()
        assert len(df) == large_size

    def test_vwap_result_extreme_values(self):
        """Test VWAPResult with extreme values."""
        from src.forex_bot.vwap.models import VWAPResult
        
        extreme_timestamps = np.array([
            pd.Timestamp('1900-01-01', tz='UTC'),
            pd.Timestamp('2100-12-31', tz='UTC')
        ])
        extreme_values = np.array([0.0001, 999999.9999])
        
        result = VWAPResult(
            timestamps=extreme_timestamps,
            vwap_values=extreme_values,
            symbol="EXTREME",
            timeframe=1440
        )
        
        assert result.vwap_values[0] == 0.0001
        assert result.vwap_values[1] == 999999.9999
        
        df = result.to_dataframe()
        assert len(df) == 2

    def test_vwap_result_nan_values(self):
        """Test VWAPResult with NaN values."""
        from src.forex_bot.vwap.models import VWAPResult
        
        nan_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC'),
            pd.Timestamp('2024-01-02', tz='UTC')
        ])
        nan_values = np.array([1.1000, np.nan])
        
        result = VWAPResult(
            timestamps=nan_timestamps,
            vwap_values=nan_values,
            symbol="NAN_TEST",
            timeframe=1440
        )
        
        assert not np.isnan(result.vwap_values[0])
        assert np.isnan(result.vwap_values[1])
        
        df = result.to_dataframe()
        assert len(df) == 2
        assert np.isnan(df['vwap'].iloc[1])

    def test_vwap_result_inf_values(self):
        """Test VWAPResult with infinite values."""
        from src.forex_bot.vwap.models import VWAPResult
        
        inf_timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC'),
            pd.Timestamp('2024-01-02', tz='UTC')
        ])
        inf_values = np.array([np.inf, -np.inf])
        
        result = VWAPResult(
            timestamps=inf_timestamps,
            vwap_values=inf_values,
            symbol="INF_TEST",
            timeframe=1440
        )
        
        assert np.isinf(result.vwap_values[0])
        assert np.isinf(result.vwap_values[1])
        
        df = result.to_dataframe()
        assert len(df) == 2

    def test_vwap_result_different_timeframes(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult with different timeframes."""
        from src.forex_bot.vwap.models import VWAPResult
        
        timeframes = [1, 5, 15, 30, 60, 240, 1440]
        
        for tf in timeframes:
            result = VWAPResult(
                timestamps=sample_timestamps,
                vwap_values=sample_vwap_values,
                symbol="MULTI_TF",
                timeframe=tf
            )
            assert result.timeframe == tf

    def test_vwap_result_different_symbols(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult with different symbols."""
        from src.forex_bot.vwap.models import VWAPResult
        
        symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "NZDUSD"]
        
        for symbol in symbols:
            result = VWAPResult(
                timestamps=sample_timestamps,
                vwap_values=sample_vwap_values,
                symbol=symbol,
                timeframe=5
            )
            assert result.symbol == symbol

    def test_vwap_result_copy_operations(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult copy operations."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="COPY_TEST",
            timeframe=5
        )
        
        # Test shallow copy
        result_copy = copy.copy(result)
        assert result_copy.symbol == result.symbol
        assert np.array_equal(result_copy.timestamps, result.timestamps)
        
        # Test deep copy
        result_deepcopy = copy.deepcopy(result)
        assert result_deepcopy.symbol == result.symbol
        assert np.array_equal(result_deepcopy.timestamps, result.timestamps)

    def test_vwap_result_string_representations(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult string representations."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="STR_TEST",
            timeframe=5
        )
        
        # Test string representation
        str_repr = str(result)
        assert isinstance(str_repr, str)
        assert "VWAPResult" in str_repr

    def test_vwap_result_equality_comparison(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult equality comparison."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result1 = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        result2 = VWAPResult(
            timestamps=sample_timestamps.copy(),
            vwap_values=sample_vwap_values.copy(),
            symbol="EQUAL_TEST",
            timeframe=5
        )
        
        # Test individual attributes for equality
        assert result1.symbol == result2.symbol
        assert result1.timeframe == result2.timeframe
        assert np.array_equal(result1.timestamps, result2.timestamps)
        assert np.array_equal(result1.vwap_values, result2.vwap_values)

    def test_vwap_result_partial_bands(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult with partial band data."""
        from src.forex_bot.vwap.models import VWAPResult
        
        upper_1sd = np.array([1.1020, 1.1030, 1.1025, 1.1035, 1.1040])
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="PARTIAL_BANDS",
            timeframe=5,
            upper_band_1sd=upper_1sd
            # Only upper_band_1sd, no other bands
        )
        
        df = result.to_dataframe()
        
        assert 'upper_band_1sd' in df.columns
        assert 'lower_band_1sd' not in df.columns
        assert 'upper_band_2sd' not in df.columns
        assert 'lower_band_2sd' not in df.columns

    def test_vwap_result_dataframe_dtypes(self, sample_timestamps, sample_vwap_values):
        """Test VWAPResult DataFrame data types."""
        from src.forex_bot.vwap.models import VWAPResult
        
        result = VWAPResult(
            timestamps=sample_timestamps,
            vwap_values=sample_vwap_values,
            symbol="DTYPE_TEST",
            timeframe=5
        )
        
        df = result.to_dataframe()
        
        # Test DataFrame data types
        assert df.dtypes['vwap'] == np.float64
        assert pd.api.types.is_datetime64_any_dtype(df['timestamp'])

    def test_vwap_result_memory_efficiency(self):
        """Test VWAPResult memory efficiency with large arrays."""
        from src.forex_bot.vwap.models import VWAPResult
        
        # Test with moderately large arrays
        size = 1000
        timestamps = np.array([
            pd.Timestamp('2024-01-01', tz='UTC') + pd.Timedelta(seconds=i)
            for i in range(size)
        ])
        values = 1.1000 + np.arange(size, dtype=np.float64) * 0.00001
        
        result = VWAPResult(
            timestamps=timestamps,
            vwap_values=values,
            symbol="MEMORY_TEST",
            timeframe=1
        )
        
        # Should handle large arrays without issues
        df = result.to_dataframe()
        assert len(df) == size
        assert df.memory_usage(deep=True).sum() > 0
